<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Fetch ticket data for dashboard overview
$tickets = getRecentTickets(); // Function to fetch all tickets
$openTickets = countTicketsByStatus('Open'); // Function to count tickets by status
$inProgressTickets = countTicketsByStatus('In Progress');
$pendingTickets = countTicketsByStatus('Pending'); // Add this line
$closedTickets = countTicketsByStatus('Closed');

// Include header
require_once '../../includes/header.php';
?>

<div class="container">
    <h1>Dashboard</h1>
    <div class="row">
        <div class="col-md-3">
            <div class="card border-primary mb-3">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-folder-open"></i> Open Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $openTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets กำลังเปิดอยู่</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info mb-3">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-sync"></i> In Progress Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $inProgressTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets กำลังดำเนินการอยู่</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning mb-3">
                <div class="card-header bg-warning text-white">
                    <i class="fas fa-clock"></i> Pending Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $pendingTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets รอเนินการ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success mb-3">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-check-circle"></i> Closed Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $closedTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets ที่ดำเนินการเสร็จแล้ว</p>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="container-fluid p-1 d-flex align-items-center justify-content-center mt-5">
        <h3 class="text-primary">Recent Tickets</h3>
        <span class="badge rounded-pill bg-success ms-3 text-white">(Last 10)</span>
    </div>
    <table class="table table-hover small">
        <thead>
            <tr>
                <th>Ticket No.</th>
                <th>Customer ID</th>
                <th>Customer Name</th>
                <th>Issue Detail</th>
                <th>Priority</th>
                <th>Assigned Team</th>
                <th>Status</th>
                <th>Created At</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($tickets as $ticket): ?>
                <tr onclick="window.location='<?=BASE_URL?>/modules/tickets/view.php?tkt_id=<?php echo urlencode($ticket['ticket_number']); ?>'" 
                    style="cursor: pointer;">
                    <td><?php echo htmlspecialchars($ticket['ticket_number']); ?></td>
                    <td><?php echo htmlspecialchars($ticket['customer_number']); ?></td>
                    <td><?php echo htmlspecialchars($ticket['customer_name']); ?></td>
                    <td><?php echo text_to_readmore(htmlspecialchars($ticket['issue_details'])); ?></td>
                    <td>
                        <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                            <?php echo htmlspecialchars($ticket['priority']); ?>
                        </span>
                    </td>
                    <td><?php echo htmlspecialchars($ticket['assigne_team']); ?></td>
                    <td>
                        <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                            <?php echo htmlspecialchars($ticket['status']); ?>
                        </span>
                    </td>
                    <td><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <style>
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
        transition: background-color 0.2s ease;
    }
    </style>
</div>
<?php
// Include main script loader
// This is where you can include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
// Include footer
require_once '../../includes/footer.php';
