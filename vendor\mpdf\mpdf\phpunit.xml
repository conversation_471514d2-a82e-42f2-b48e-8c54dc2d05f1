<phpunit
	bootstrap="tests/bootstrap.php"
	colors="true"
	backupGlobals="false"
	beStrictAboutTestsThatDoNotTestAnything="false">

	<testsuites>
		<testsuite name="Tests">
			<directory suffix="Test.php">./tests</directory>
		</testsuite>
	</testsuites>

	<coverage processUncoveredFiles="true">
		<include>
			<directory suffix=".php">src</directory>
		</include>
		<report>
			<html outputDirectory="build/coverage"/>
			<text outputFile="php://stdout" showOnlySummary="true"/>
		</report>
	</coverage>

</phpunit>
