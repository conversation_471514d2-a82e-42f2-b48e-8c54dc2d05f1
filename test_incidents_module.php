<?php
// Test script for Incident Management Module
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>Incident Management Module Test</h1>\n";

// Test 1: Check if tables exist
echo "<h2>1. Database Tables Check</h2>\n";
try {
    $tables = ['incidents', 'incident_attachments', 'incident_status_history'];
    foreach ($tables as $table) {
        $query = "SHOW TABLES LIKE '$table'";
        $result = $pdo->query($query);
        if ($result->rowCount() > 0) {
            echo "✓ Table '$table' exists<br>\n";
        } else {
            echo "❌ Table '$table' does not exist<br>\n";
        }
    }
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>\n";
}

// Test 2: Check helper functions
echo "<h2>2. Helper Functions Check</h2>\n";
$functions = [
    'generateIncidentNumber',
    'getIncidentStatuses',
    'getIncidentSeverities',
    'getIncidentTypes',
    'getIncidentChannels',
    'getIncidentRowClass',
    'getIncidentStatusBadgeClass',
    'getSeverityBadgeClass',
    'formatFileSize'
];

foreach ($functions as $function) {
    if (function_exists($function)) {
        echo "✓ Function '$function' exists<br>\n";
    } else {
        echo "❌ Function '$function' does not exist<br>\n";
    }
}

// Test 3: Test helper function outputs
echo "<h2>3. Helper Function Outputs</h2>\n";
if (function_exists('generateIncidentNumber')) {
    $incident_no = generateIncidentNumber($pdo);
    echo "Generated incident number: " . ($incident_no ? $incident_no : 'Failed') . "<br>\n";
}

if (function_exists('getIncidentStatuses')) {
    $statuses = getIncidentStatuses();
    echo "Incident statuses: " . implode(', ', $statuses) . "<br>\n";
}

if (function_exists('getIncidentSeverities')) {
    $severities = getIncidentSeverities();
    echo "Incident severities: " . implode(', ', $severities) . "<br>\n";
}

if (function_exists('formatFileSize')) {
    echo "File size formatting: " . formatFileSize(1024) . " | " . formatFileSize(1048576) . " | " . formatFileSize(1073741824) . "<br>\n";
}

// Test 4: Check module files
echo "<h2>4. Module Files Check</h2>\n";
$module_files = [
    'modules/incidents/index.php',
    'modules/incidents/list.php',
    'modules/incidents/create.php',
    'modules/incidents/view.php',
    'modules/incidents/edit.php',
    'modules/incidents/download.php'
];

foreach ($module_files as $file) {
    if (file_exists($file)) {
        echo "✓ File '$file' exists<br>\n";
    } else {
        echo "❌ File '$file' does not exist<br>\n";
    }
}

// Test 5: Check upload directory
echo "<h2>5. Upload Directory Check</h2>\n";
$upload_dir = 'uploads/incidents/';
if (!is_dir($upload_dir)) {
    if (mkdir($upload_dir, 0755, true)) {
        echo "✓ Upload directory created: $upload_dir<br>\n";
    } else {
        echo "❌ Failed to create upload directory: $upload_dir<br>\n";
    }
} else {
    echo "✓ Upload directory exists: $upload_dir<br>\n";
}

// Check if directory is writable
if (is_writable($upload_dir)) {
    echo "✓ Upload directory is writable<br>\n";
} else {
    echo "❌ Upload directory is not writable<br>\n";
}

echo "<h2>Test Complete</h2>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li>1. Run this script to create database tables: <code>php create_incidents_tables.php</code></li>\n";
echo "<li>2. Access the incidents module at: <a href='modules/incidents/list.php'>modules/incidents/list.php</a></li>\n";
echo "<li>3. Create a test incident report</li>\n";
echo "<li>4. Test file upload functionality</li>\n";
echo "<li>5. Test search and filtering</li>\n";
echo "</ul>\n";
?>
