<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';

require_once '../../vendor/autoload.php'; 
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require_once '../../includes/functions.php';

// Fetch ticket ID from URL
$ticket_id = $_GET['tkt_id'] ?? 0;

$interim_mailto = '<EMAIL>,<EMAIL>';

// Fetch ticket details with JOIN
$query = "SELECT 
    t.*, 
    c.CusName AS customer_name,
    c<PERSON><PERSON>ddress AS customer_address,
    tm.name AS team_name,
    u.username AS created_by,
    t.ticket_type,
    t.affecting_service,
    t.symptoms_details,
    t.product_type,
    t.severity
FROM 
    tickets t 
    LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
    LEFT JOIN teams tm ON t.assigned_team = tm.id
    LEFT JOIN users u ON t.username = u.username
WHERE 
    t.ticket_number = ?";

$stmt = $pdo->prepare($query);
$stmt->execute([$ticket_id]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

// Fetch symptoms and product types for reference
$symptoms_query = "SELECT code, name FROM symptoms WHERE is_active = 1";
$symptoms_stmt = $pdo->query($symptoms_query);
$symptoms_list = $symptoms_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$product_types_query = "SELECT code, name FROM product_types WHERE is_active = 1";
$product_types_stmt = $pdo->query($product_types_query);
$product_types_list = $product_types_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Fetch ticket comments
$comments_query = "SELECT 
    tc.*, u.username, u.role
    FROM ticket_comments tc
    LEFT JOIN users u ON tc.user_id = u.id
    WHERE tc.ticket_number = ?
    ORDER BY tc.created_at DESC";
$comments_stmt = $pdo->prepare($comments_query);
$comments_stmt->execute([$ticket_id]);
$comments = $comments_stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle new comment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comment'])) {

    $comment = base64_encode(trim($_POST['comment']));
    if (!empty($comment)) {
        $insert_query = "INSERT INTO ticket_comments (ticket_number, user_id, comment_base64, created_at) 
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP)";

        $insert_stmt = $pdo->prepare($insert_query);
        $insert_stmt->execute([$ticket_id, $_SESSION['user_id'], $comment]);

        // Redirect to prevent form resubmission
        header("Location: ?tkt_id=" . $ticket_id);
        exit();
    }
}

// Handle status update submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    $new_status = trim($_POST['status']);
    $current_status = trim($_POST['current_status'] ?? '');
    $status_comment = trim($_POST['status_comment'] ?? '');
    if (empty($new_status) || $new_status === $current_status) {
        $error_message = "Invalid status update.";
        // Redirect to prevent form resubmission
        header("Location: view.php?tkt_id=" . $ticket_id . "&status_updated=0&error=" . urlencode($error_message));
        exit();
    } else {
        $ticket_id = $_POST['ticket_number'];
        try {
            // Start transaction
            $pdo->beginTransaction();

            // Update ticket status
            $update_query = "UPDATE tickets SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE ticket_number = ?";
            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute([$new_status, $ticket_id]);

            // Record status change in history
            $history_query = "INSERT INTO ticket_status_history 
            (ticket_number, status, changed_by, comments) 
            VALUES (?, ?, ?, ?)";
            $history_stmt = $pdo->prepare($history_query);
            $history_stmt->execute([
                $ticket_id,
                $new_status,
                $_SESSION['user_id'],
                $status_comment
            ]);

            $pdo->commit();

            // Redirect to prevent form resubmission
            header("Location: view.php?tkt_id=" . $ticket_id . "&status_updated=1");
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $error_message = "Error updating status: " . $e->getMessage();
        }
    } //check if status is empty or same as current status
}

// Handle ticket forwarding
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'forward_ticket') {
    $forward_team = trim($_POST['forward_team'] ?? '');
    $forward_priority = trim($_POST['forward_priority'] ?? '');
    $forward_notes = trim($_POST['forward_notes'] ?? '');
    $ticket_number = trim($_POST['ticket_number'] ?? '');
    
    if (empty($forward_team) || empty($forward_notes) || empty($ticket_number)) {
        $error_message = "All fields are required for forwarding.";
        // Redirect with error
        header("Location: view.php?tkt_id=" . $ticket_id . "&forward_error=" . urlencode($error_message));
        exit();
    }
    
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // 1. Update the ticket's assigned team and priority
        $update_query = "UPDATE tickets SET 
                        assigned_team = ?, 
                        priority = ?,
                        updated_at = CURRENT_TIMESTAMP 
                        WHERE ticket_number = ?";
        $update_stmt = $pdo->prepare($update_query);
        $update_stmt->execute([$forward_team, $forward_priority, $ticket_number]);
        
        // 2. Get the team name for the comment
        $team_query = "SELECT name FROM teams WHERE id = ?";
        $team_stmt = $pdo->prepare($team_query);
        $team_stmt->execute([$forward_team]);
        $team_name = $team_stmt->fetchColumn();
        
        // 3. Add a system comment about the forwarding
        $forward_comment = "<div class='alert alert-info'>
            <strong>Ticket Forwarded</strong><br>
            This ticket has been forwarded to the <strong>{$team_name}</strong> team by " . 
            htmlspecialchars($_SESSION['userdata']['username']) . ".<br>
            <strong>Notes:</strong> " . nl2br(htmlspecialchars($forward_notes)) . "
        </div>";
        
        $comment_query = "INSERT INTO ticket_comments 
                        (ticket_number, user_id, comment, created_at, is_system) 
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP, 1)";
        $comment_stmt = $pdo->prepare($comment_query);
        $comment_stmt->execute([$ticket_number, $_SESSION['user_id'], $forward_comment]);
        
        // 4. Record in ticket_forwards table
        $forward_query = "INSERT INTO ticket_forwards 
                        (ticket_number, from_team, to_team, forwarded_by, notes, created_at) 
                        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
        $forward_stmt = $pdo->prepare($forward_query);
        $forward_stmt->execute([
            $ticket_number,
            $ticket['assigned_team'],
            $forward_team,
            $_SESSION['user_id'],
            $forward_notes
        ]);
        
        // 5. Send notification to the team (optional)
        $team_email_query = "SELECT email FROM teams WHERE id = ?";
        $team_email_stmt = $pdo->prepare($team_email_query);
        $team_email_stmt->execute([$forward_team]);
        $team_email = $team_email_stmt->fetchColumn();
        
        if ($team_email) {
            // Send email notification (implement your email function)
            $subject = "Ticket #{$ticket_number} has been forwarded to your team";
            $message = "A ticket has been forwarded to your team.\n\n";
            $message .= "Ticket: #{$ticket_number}\n";
            $message .= "Customer: {$ticket['customer_name']}\n";
            $message .= "Notes: {$forward_notes}\n\n";
            $message .= "Please review this ticket at: " . BASE_URL . "/modules/tickets/view.php?tkt_id={$ticket_number}";
            
            // Uncomment when you have an email function
            // sendEmail($team_email, $subject, $message);
        }
        
        $pdo->commit();
        
        // Redirect to prevent form resubmission
        header("Location: view.php?tkt_id=" . $ticket_id . "&forward_success=1");
        exit();
    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = "Error forwarding ticket: " . $e->getMessage();
        header("Location: view.php?tkt_id=" . $ticket_id . "&forward_error=" . urlencode($error_message));
        exit();
    }
}

require_once '../../includes/header.php';

// Display status update success message
if (isset($_GET['status_updated'])) {
    if ($_GET['status_updated'] == 0) {
        echo '<div class="alert alert-danger alert-dismissible fade show">
            ' . htmlspecialchars($_GET['error']) . '
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    } else {
        echo '<div class="alert alert-success alert-dismissible fade show">
            Status updated successfully
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    }
}

// Display forwarding success/error messages
if (isset($_GET['forward_success'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Ticket successfully forwarded to another team.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}

if (isset($_GET['forward_error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show">
        ' . htmlspecialchars($_GET['forward_error']) . '
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}

// Display interim update success message
if (isset($_GET['interim_updated'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Interim information updated successfully
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}

// Add this where you handle other alerts
if (isset($_GET['email_sent'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Interim information updated and notification email sent successfully.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}

if (isset($_GET['email_failed'])) {
    echo '<div class="alert alert-warning alert-dismissible fade show">
        Interim information updated but email notification failed to send.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}

// Fetch all teams for the forward dropdown
$teams_query = "SELECT id, name FROM teams WHERE is_active = 1 ORDER BY name";
$teams_stmt = $pdo->query($teams_query);
$teams = $teams_stmt->fetchAll(PDO::FETCH_ASSOC);

// If no teams were found, create a fallback
if (empty($teams)) {
    // Log this issue
    error_log("No teams found in the database for ticket forwarding");
    
    // Create a fallback array with at least the current team
    if (!empty($ticket['assigned_team'])) {
        $teams = [
            [
                'id' => $ticket['assigned_team'],
                'name' => $ticket['team_name'] ?? 'Current Team'
            ],
            // Add some dummy teams
            ['id' => 'support', 'name' => 'Support Team'],
            ['id' => 'technical', 'name' => 'Technical Team'],
            ['id' => 'billing', 'name' => 'Billing Team']
        ];
    }
}
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-11">
            <?php if (!$ticket): ?>
                <div class="alert alert-danger">
                    Ticket not found. <a href="list.php" class="alert-link">Return to ticket list</a>
                </div>
            <?php else: ?>
                <div class="card shadow">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Ticket #<?php echo htmlspecialchars($ticket['ticket_number']); ?></h4>
                        <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                            <?php echo htmlspecialchars($ticket['status']); ?>
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">Customer Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Customer Name:</th>
                                        <td><?php echo htmlspecialchars($ticket['customer_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Customer ID:</th>
                                        <td><?php echo htmlspecialchars($ticket['customer_number']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Address:</th>
                                        <td>
                                            <Address class="small"><?php echo htmlspecialchars($ticket['customer_address']); ?></Address>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-3">Ticket Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Ticket Type:</th>
                                        <td>
                                            <?php
                                            $ticket_types = [
                                                '1' => 'Incident',
                                                '2' => 'Problem',
                                                '3' => 'Change',
                                                '4' => 'Request',
                                                '5' => 'Service Request'
                                            ];
                                            echo htmlspecialchars($ticket_types[$ticket['ticket_type']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Service Impact:</th>
                                        <td>
                                            <?php
                                            $impact_types = [
                                                '1' => 'กระทบ',
                                                '2' => 'ไม่กระทบ'
                                            ];
                                            echo htmlspecialchars($impact_types[$ticket['affecting_service']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Symptoms:</th>
                                        <td>
                                            <?php
                                            echo htmlspecialchars($symptoms_list[$ticket['symptoms_details']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Product Type:</th>
                                        <td>
                                            <?php
                                            echo htmlspecialchars($product_types_list[$ticket['product_type']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Severity:</th>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php
                                                $severity_types = [
                                                    'S1' => 'Severity 1 (S1- Service Critical Impact)',
                                                    'S2' => 'Severity 2 (S2- Significant Impact)',
                                                    'S3' => 'Severity 3 (S3- Minor impact)',
                                                    'S4' => 'Severity 4 (S4- Low Impact)'
                                                ];
                                                echo htmlspecialchars($severity_types[$ticket['severity']] ?? 'N/A');
                                                ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Priority:</th>
                                        <td>
                                            <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                                                <?php echo htmlspecialchars($ticket['priority']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Current Status:</th>
                                        <td>
                                            <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                                <?php echo htmlspecialchars($ticket['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Assigned Team:</th>
                                        <td><?php echo htmlspecialchars($ticket['team_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created By:</th>
                                        <td><?php echo htmlspecialchars($ticket['created_by']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created At:</th>
                                        <td><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Working Time:</th>
                                        <td>
                                            <?php
                                            $working_time = calculateTicketWorkingTimeById($ticket['ticket_number']);
                                            if ($working_time) {
                                                echo '<span class="badge badge-info">' . $working_time['formatted'] . '</span>';
                                                if ($ticket['status'] == 'Pending') {
                                                    echo ' <small class="text-muted">(Paused)</small>';
                                                }elseif ($ticket['status'] !== 'Closed') {
                                                    echo ' <small class="text-muted">(Running)</small>';
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th width="150">Channel:</th>
                                        <td>
                                            <?php
                                            $channel_types = [
                                                '1' => 'Monitoring',
                                                '2' => 'Telephone',
                                                '3' => 'Email',
                                                '4' => 'Chat',
                                                '5' => 'Walk-in'
                                            ];
                                            echo isset($channel_types[$ticket['channel_type']]) ? 
                                                htmlspecialchars($channel_types[$ticket['channel_type']]) : 
                                                'Unknown';
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h5 class="text-<?=getStatusBadgeClass($ticket['status'])?>">
                                <i class="fas fa-exclamation-circle"></i> Issue Details
                            </h5>
                            <div class="p-3 rounded issue-details-box">
                                <?php echo nl2br(htmlspecialchars($ticket['issue_details'])); ?>
                            </div>
                        </div>

                        <div class="mt-4 small">
                            <h5>Update Status</h5>
                            <form method="POST" class="form-horizontal">
                                <?php if ($ticket['status'] === 'Closed'): ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-lock"></i> This ticket is closed and cannot be modified.
                                    </div>
                                <?php else: ?>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">New Status:</label>
                                        <div class="col-sm-4">
                                            <select name="status" class="form-control" required <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>>
                                                <option value="">Select Status</option>
                                                <option value="Open" <?php echo ($ticket['status'] == 'Open') ? 'selected' : ''; ?>>Open</option>
                                                <option value="In Progress" <?php echo ($ticket['status'] == 'In Progress') ? 'selected' : ''; ?>>In Progress</option>
                                                <option value="Pending" <?php echo ($ticket['status'] == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                                <option value="Closed" <?php echo ($ticket['status'] == 'Closed') ? 'selected' : ''; ?>>Closed</option>
                                            </select>
                                            <input type="hidden" name="ticket_number" value="<?php echo htmlspecialchars($ticket['ticket_number']); ?>">
                                            <input type="hidden" name="current_status" value="<?php echo htmlspecialchars($ticket['status']); ?>">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">Comments:</label>
                                        <div class="col-sm-6">
                                            <textarea name="status_comment" class="form-control" rows="2"
                                                placeholder="Add any comments about this status change..." <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-10 offset-sm-2">
                                            <button type="submit" class="btn btn-primary" <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>>
                                                <i class="fas fa-save"></i> Update Status
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </form>
                        </div>

                        <div class="mt-4 small text-secondary">
                            <h5>Status History</h5>
                            <?php
                            $history_query = "SELECT 
                                tsh.*, u.username, u.role
                                FROM ticket_status_history tsh
                                LEFT JOIN users u ON tsh.changed_by = u.id
                                WHERE tsh.ticket_number = ?
                                ORDER BY tsh.changed_at DESC";
                            $history_stmt = $pdo->prepare($history_query);
                            $history_stmt->execute([$ticket_id]);
                            $status_history = $history_stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>

                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>Status</th>
                                            <th>Changed By</th>
                                            <th>Comments</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($status_history as $history): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H:i', strtotime($history['changed_at'])); ?></td>
                                                <td>
                                                    <span class="badge badge-<?php echo getStatusBadgeClass($history['status']); ?>">
                                                        <?php echo htmlspecialchars($history['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($history['username']); ?>
                                                    <span class="badge badge-secondary"><?php echo htmlspecialchars($history['role']); ?></span>
                                                </td>
                                                <td><?php echo nl2br(htmlspecialchars($history['comments'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if (empty($status_history)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">No status changes recorded</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="mt-4 small text-secondary">
                            <h5>Forwarding History</h5>
                            <?php
                            $forwards_query = "SELECT 
                                tf.*, 
                                t1.name AS from_team_name,
                                t2.name AS to_team_name,
                                u.username AS forwarded_by_name
                                FROM ticket_forwards tf
                                LEFT JOIN teams t1 ON tf.from_team = t1.id
                                LEFT JOIN teams t2 ON tf.to_team = t2.id
                                LEFT JOIN users u ON tf.forwarded_by = u.id
                                WHERE tf.ticket_number = ?
                                ORDER BY tf.created_at DESC";
                            $forwards_stmt = $pdo->prepare($forwards_query);
                            $forwards_stmt->execute([$ticket_id]);
                            $forwards_history = $forwards_stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>

                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>From Team</th>
                                            <th>To Team</th>
                                            <th>Forwarded By</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($forwards_history as $forward): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H:i', strtotime($forward['created_at'])); ?></td>
                                                <td><?php echo htmlspecialchars($forward['from_team_name']); ?></td>
                                                <td><?php echo htmlspecialchars($forward['to_team_name']); ?></td>
                                                <td><?php echo htmlspecialchars($forward['forwarded_by_name']); ?></td>
                                                <td><?php echo nl2br(htmlspecialchars($forward['notes'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if (empty($forwards_history)): ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No forwarding history</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Comments Section with Tabs -->
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <ul class="nav nav-tabs card-header-tabs" id="ticketActionTabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="comments-tab" data-toggle="tab" href="#comments-content" role="tab" aria-controls="comments-content" aria-selected="true">
                                                <i class="fas fa-comments"></i> Comments
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="forward-tab" data-toggle="tab" href="#forward-content" role="tab" aria-controls="forward-content" aria-selected="false">
                                                <i class="fas fa-exchange-alt"></i> Forward Ticket
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="interim-tab" data-toggle="tab" href="#interim-content" role="tab" aria-controls="interim-content" aria-selected="false">
                                                <i class="fas fa-clipboard-list"></i> Interim
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content" id="ticketActionTabsContent">
                                        <!-- Comments Tab -->
                                        <div class="tab-pane fade show active" id="comments-content" role="tabpanel" aria-labelledby="comments-tab">
                                            <!-- Comment Form -->
                                            <form id="editorForm" method="POST" enctype="multipart/form-data">
                                                <div class="mb-3">
                                                    <div id="editorComment"></div>
                                                    <textarea id="hiddenComment" name="comment" style="display:none;"></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-comment"></i> Add Comment
                                                    </button>
                                                </div>
                                            </form>

                                            <!-- Comments List -->
                                            <div class="comments-section mt-4">
                                                <h5>Comment History</h5>
                                                <?php if (!empty($comments)): ?>
                                                    <?php foreach ($comments as $comment): ?>
                                                        <div class="comment card mb-3">
                                                            <div class="card-body">
                                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                                    <div>
                                                                        <strong class="text-primary">
                                                                            <i class="fas fa-user-circle"></i>
                                                                            <?php echo htmlspecialchars($comment['username']); ?>
                                                                        </strong>
                                                                        <span class="badge badge-secondary ml-2">
                                                                            <?php echo htmlspecialchars($comment['role']); ?>
                                                                        </span>
                                                                    </div>
                                                                    <small class="text-muted">
                                                                        <i class="fas fa-clock"></i>
                                                                        <?php echo date('d/m/Y H:i', strtotime($comment['created_at'])); ?>
                                                                    </small>
                                                                </div>
                                                                <div class="comment-content">
                                                                    <?php echo $comment['comment'] ?? base64_decode($comment['comment_base64']); // Display HTML content ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <div class="alert alert-info">
                                                        No comments yet. Be the first to comment!
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Forward Ticket Tab -->
                                        <div class="tab-pane fade" id="forward-content" role="tabpanel" aria-labelledby="forward-tab">
                                            <form method="POST" id="forwardForm">
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="forward_team">Forward to Team:</label>
                                                        <select name="forward_team" id="forward_team" class="form-control" required>
                                                            <option value="">Select Team</option>
                                                            <?php
                                                            foreach ($teams as $team): 
                                                                // Don't show current team in the dropdown
                                                                if ($team['id'] != $ticket['assigned_team']):
                                                            ?>
                                                                <option value="<?php echo $team['id']; ?>">
                                                                    <?php echo htmlspecialchars($team['name']); ?>
                                                                </option>
                                                            <?php 
                                                                endif;
                                                            endforeach; 
                                                            ?>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="forward_priority">Priority:</label>
                                                        <select name="forward_priority" id="forward_priority" class="form-control">
                                                            <option value="<?php echo htmlspecialchars($ticket['priority']); ?>">
                                                                Keep Current (<?php echo htmlspecialchars($ticket['priority']); ?>)
                                                            </option>
                                                            <option value="High">High</option>
                                                            <option value="Medium">Medium</option>
                                                            <option value="Low">Low</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="forward_notes">Notes for Team:</label>
                                                    <textarea name="forward_notes" id="forward_notes" class="form-control" rows="3" 
                                                        placeholder="Explain why you're forwarding this ticket and what needs to be done next..." required></textarea>
                                                </div>
                                                <input type="hidden" name="action" value="forward_ticket">
                                                <input type="hidden" name="ticket_number" value="<?php echo htmlspecialchars($ticket['ticket_number']); ?>">
                                                <button type="submit" class="btn btn-info" <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>>
                                                    <i class="fas fa-paper-plane"></i> Forward Ticket
                                                </button>
                                            </form>

                                            <!-- Forwarding History -->
                                            <div class="mt-4">
                                                <h5>Forwarding History</h5>
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-hover">
                                                        <thead>
                                                            <tr>
                                                                <th>Date/Time</th>
                                                                <th>From Team</th>
                                                                <th>To Team</th>
                                                                <th>Forwarded By</th>
                                                                <th>Notes</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php foreach ($forwards_history as $forward): ?>
                                                                <tr>
                                                                    <td><?php echo date('d/m/Y H:i', strtotime($forward['created_at'])); ?></td>
                                                                    <td><?php echo htmlspecialchars($forward['from_team_name']); ?></td>
                                                                    <td><?php echo htmlspecialchars($forward['to_team_name']); ?></td>
                                                                    <td><?php echo htmlspecialchars($forward['forwarded_by_name']); ?></td>
                                                                    <td><?php echo nl2br(htmlspecialchars($forward['notes'])); ?></td>
                                                                </tr>
                                                            <?php endforeach; ?>
                                                            <?php if (empty($forwards_history)): ?>
                                                                <tr>
                                                                    <td colspan="5" class="text-center text-muted">No forwarding history</td>
                                                                </tr>
                                                            <?php endif; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Interim Tab -->
                                        <div class="tab-pane fade" id="interim-content" role="tabpanel" aria-labelledby="interim-tab">
                                            <?php
                                            // Fetch existing interim data if available
                                            $interim_query = "SELECT * FROM ticket_interim WHERE ticket_number = ?";
                                            $interim_stmt = $pdo->prepare($interim_query);
                                            $interim_stmt->execute([$ticket_id]);
                                            $interim_data = $interim_stmt->fetch(PDO::FETCH_ASSOC);
                                            
                                            // Handle interim form submission
                                            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_interim') {
                                                try {
                                                    // Check if record exists
                                                    if ($interim_data) {
                                                        // Update existing record
                                                        $update_query = "UPDATE ticket_interim SET 
                                                            mailto = ?, 
                                                            login_temp = ?, 
                                                            sim_no = ?, 
                                                            sim_serial = ?, 
                                                            sim_operator = ?, 
                                                            sim_package = ?,
                                                            updated_at = CURRENT_TIMESTAMP,
                                                            updated_by = ?
                                                            WHERE ticket_number = ?";
                                                        
                                                        $update_stmt = $pdo->prepare($update_query);
                                                        $update_stmt->execute([
                                                            $_POST['mailto'] ?? null,
                                                            $_POST['login_temp'] ?? null,
                                                            $_POST['sim_no'] ?? null,
                                                            $_POST['sim_serial'] ?? null,
                                                            $_POST['sim_operator'] ?? null,
                                                            $_POST['sim_package'] ?? null,
                                                            $_SESSION['user_id'],
                                                            $ticket_id
                                                        ]);
                                                    } else {
                                                        // Insert new record
                                                        $insert_query = "INSERT INTO ticket_interim (
                                                            ticket_number, 
                                                            mailto, 
                                                            login_temp, 
                                                            sim_no, 
                                                            sim_serial, 
                                                            sim_operator, 
                                                            sim_package,
                                                            created_at,
                                                            created_by
                                                        ) VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?)";
                                                        
                                                        $insert_stmt = $pdo->prepare($insert_query);
                                                        $insert_stmt->execute([
                                                            $ticket_id,
                                                            $_POST['mailto'] ?? null,
                                                            $_POST['login_temp'] ?? null,
                                                            $_POST['sim_no'] ?? null,
                                                            $_POST['sim_serial'] ?? null,
                                                            $_POST['sim_operator'] ?? null,
                                                            $_POST['sim_package'] ?? null,
                                                            $_SESSION['user_id']
                                                        ]);
                                                    }
                                                    
                                                    // Prepare email content
                                                    $emailSubject = "Testing Interim Information Updated - Ticket #{$ticket_id}";
                                                    $emailBody = "
                                                    <html>
                                                    <body>
                                                        <h2>Interim Information Updated</h2>
                                                        <p>Ticket #{$ticket_id} interim information has been updated.</p>
                                                        
                                                        <h3>Details:</h3>
                                                        <ul>
                                                            <li><strong>Login Temp:</strong> {$_POST['login_temp']}</li>
                                                            <li><strong>SIM Number:</strong> {$_POST['sim_no']}</li>
                                                            <li><strong>SIM Serial:</strong> {$_POST['sim_serial']}</li>
                                                            <li><strong>SIM Operator:</strong> {$_POST['sim_operator']}</li>
                                                            <li><strong>SIM Package:</strong> {$_POST['sim_package']}</li>
                                                        </ul>
                                                        
                                                        <p>Updated by: {$_SESSION['userdata']['username']}</p>
                                                        <p><a href='" . BASE_URL . "/modules/tickets/view.php?tkt_id={$ticket_id}'>View Ticket</a></p>
                                                    </body>
                                                    </html>";

                                                    // Send email
                                                    $emailSent = sendInterimEmail($_POST['mailto'], $emailSubject, $emailBody);
                                                    
                                                    if (!$emailSent) {
                                                        // Log email sending failure but don't stop the process
                                                        error_log("Failed to send interim update email for ticket #{$ticket_id}");
                                                    }

                                                    // Add email status to system comment
                                                    $interim_comment = "<div class='alert alert-info'>
                                                        <strong>Interim Information Updated</strong><br>
                                                        Interim details have been updated by " . htmlspecialchars($_SESSION['userdata']['username']) . ".<br>" .
                                                        ($emailSent ? "<span class='text-success'>✓ Notification email sent</span>" : 
                                                                     "<span class='text-warning'>⚠ Email notification failed</span>") .
                                                    "</div>";
                                                    
                                                    $comment_query = "INSERT INTO ticket_comments 
                                                                    (ticket_number, user_id, comment, created_at, is_system) 
                                                                    VALUES (?, ?, ?, CURRENT_TIMESTAMP, 1)";
                                                    $comment_stmt = $pdo->prepare($comment_query);
                                                    $comment_stmt->execute([$ticket_id, $_SESSION['user_id'], $interim_comment]);
                                                    
                                                    // Refresh the page to show updated data
                                                    header("Location: view.php?tkt_id=" . $ticket_id . "&interim_updated=1");
                                                    exit();
                                                    
                                                } catch (Exception $e) {
                                                    $error_message = "Error updating interim information: " . $e->getMessage();
                                                }
                                            }
                                            ?>
                                            
                                            <form method="POST" id="interimForm">
                                                <input type="hidden" name="action" value="update_interim">
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="mailto"><i class="fas fa-envelope"></i> Mail To: <span class="text-danger">*</span></label>
                                                            <input type="email" 
                                                                   class="form-control" 
                                                                   id="mailto" 
                                                                   name="mailto"
                                                                   multiple
                                                                   value="<?php echo htmlspecialchars($interim_data['mailto'] ?? $interim_mailto); ?>" 
                                                                   required>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label for="login_temp"><i class="fas fa-key"></i> Login Temp: <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="login_temp" name="login_temp" 
                                                                value="<?php echo htmlspecialchars($interim_data['login_temp'] ?? ''); ?>" required>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label for="sim_no"><i class="fas fa-sim-card"></i> SIM Number: <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="sim_no" name="sim_no" 
                                                                value="<?php echo htmlspecialchars($interim_data['sim_no'] ?? ''); ?>" required>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label for="sim_serial"><i class="fas fa-barcode"></i> SIM Serial Number: <span class="text-danger">*</span></label>
                                                            <input type="text" class="form-control" id="sim_serial" name="sim_serial" 
                                                                value="<?php echo htmlspecialchars($interim_data['sim_serial'] ?? ''); ?>" required>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label for="sim_operator"><i class="fas fa-broadcast-tower"></i> SIM Operator: <span class="text-danger">*</span></label>
                                                            <select class="form-control" id="sim_operator" name="sim_operator" required>
                                                                <option value="">-- Select Operator --</option>
                                                                <?php
                                                                $operators = ['AIS', 'DTAC', 'NT', 'TRUE', 'Other'];
                                                                foreach ($operators as $operator) {
                                                                    $selected = ($interim_data['sim_operator'] ?? '') === $operator ? 'selected' : '';
                                                                    echo "<option value=\"$operator\" $selected>$operator</option>";
                                                                }
                                                                ?>
                                                            </select>
                                                        </div>
                                                        
                                                        <div class="form-group">
                                                            <label for="sim_package"><i class="fas fa-box"></i> SIM Package: <span class="text-danger">*</span></label>
                                                            <select class="form-control" id="sim_package" name="sim_package" required>
                                                                <option value="">-- Select Package --</option>
                                                                <?php
                                                                $packages = ['3G_AWifi 1ToAll M2MBusiness199BFixIPCorp', '3G_AWifi 1ToAll M2MBusiness349BFixIPCorp', '3G_AWifi CorpAPN M2M Pay 599BFixIPCorp', '3G_Corp APN Business Pay299BFixIPCorp', 'Other'];
                                                                foreach ($packages as $package) {
                                                                    $selected = ($interim_data['sim_package'] ?? '') === $package ? 'selected' : '';
                                                                    echo "<option value=\"$package\" $selected>$package</option>";
                                                                }
                                                                ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="form-group mt-3">
                                                    <button type="submit" class="btn btn-primary" <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>>
                                                        <i class="fas fa-save"></i> Save Interim Information
                                                    </button>
                                                </div>
                                            </form>
                                            
                                            <?php if ($interim_data): ?>
                                            <div class="mt-4">
                                                <div class="card bg-light">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">Interim Information History</h6>
                                                    </div>
                                                    <div class="card-body small">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <p><strong>Created By:</strong> 
                                                                    <?php 
                                                                    $creator_query = "SELECT username FROM users WHERE id = ?";
                                                                    $creator_stmt = $pdo->prepare($creator_query);
                                                                    $creator_stmt->execute([$interim_data['created_by']]);
                                                                    echo htmlspecialchars($creator_stmt->fetchColumn() ?: 'Unknown'); 
                                                                    ?>
                                                                </p>
                                                                <p><strong>Created At:</strong> 
                                                                    <?php echo date('d/m/Y H:i', strtotime($interim_data['created_at'])); ?>
                                                                </p>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <?php if (!empty($interim_data['updated_by'])): ?>
                                                                <p><strong>Last Updated By:</strong> 
                                                                    <?php 
                                                                    $updater_query = "SELECT username FROM users WHERE id = ?";
                                                                    $updater_stmt = $pdo->prepare($updater_query);
                                                                    $updater_stmt->execute([$interim_data['updated_by']]);
                                                                    echo htmlspecialchars($updater_stmt->fetchColumn() ?: 'Unknown'); 
                                                                    ?>
                                                                </p>
                                                                <p><strong>Last Updated At:</strong> 
                                                                    <?php echo date('d/m/Y H:i', strtotime($interim_data['updated_at'])); ?>
                                                                </p>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-right">
                        <a href="list.php" class="btn btn-secondary">Back to List</a>
                        <a href="edit.php?tkt_id=<?php echo $ticket_id; ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Ticket
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Floating Help Note -->
<div id="helpNote" class="position-fixed shadow-sm" style="width: 300px; z-index: 1000; right: 20px; top: 80px;">
    <div class="card note-card">
        <div class="card-header note-header py-2" id="helpDragHandle">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Help Guide
                </h6>
                <button type="button" class="btn btn-link btn-sm text-secondary p-0" id="minimizeHelp">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body note-body py-2" id="helpContent">
            <div class="help-item mb-2">
                <strong><i class="fas fa-ticket-alt text-primary"></i> Status Flow:</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Open → In Progress → Closed</li>
                    <li>Use Pending when waiting for response</li>
                    <li>Closed tickets cannot be reopened</li>
                </ul>
            </div>
            <div class="help-item mb-2">
                <strong><i class="fas fa-clock text-warning"></i> Working Time:</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Starts when ticket is created</li>
                    <li>Pauses during Pending status</li>
                    <li>Stops when status is Closed</li>
                </ul>
            </div>
            <div class="help-item">
                <strong><i class="fas fa-comments text-success"></i> Comments:</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Add updates about progress</li>
                    <li>Include relevant details</li>
                    <li>Mention next actions</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer and scripts
// This is where you would include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
?>
<!--Include the JS & CSS-->
<link rel="stylesheet" href="<?= BASE_URL ?>/assets/plugins/MyWYSIWYG/css/rich-text-editor.min.css" />
<script type="text/javascript" src='<?= BASE_URL ?>/assets/plugins/MyWYSIWYG/js/rich-text-editor.min.js'></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const editor = new RichTextEditor('#editorComment');
        
        document.getElementById('editorForm').addEventListener('submit', (e) => {
            document.getElementById('hiddenComment').value = editor.getContent();
        });
    });
</script>
<script src="../../assets/js/ticket-view.js?v=<?= $timestamp ?>"></script>
<?php
require_once '../../includes/footer.php';


?>















