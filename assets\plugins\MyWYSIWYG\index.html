<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>WYSIWYG Rich Text Editor</title>
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css" integrity="sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2" crossorigin="anonymous">
  <link rel="stylesheet" href="css/rich-text-editor.css">
</head>
<body class="container py-4">
  <form id="editorForm">
    <div class="card mb-3">
      <div class="card-header">
        <h4>Rich Text Editor</h4>
      </div>
      <div class="card-body">
        <div id="editor"></div>
        <textarea id="hiddenContent" name="content" style="display:none;"></textarea>
      </div>
      <div class="card-footer">
        <button type="submit" class="btn btn-primary">Submit</button>
      </div>
    </div>
  </form>
  
  <!-- Bootstrap JS and dependencies -->
  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ho+j7jyWK8fNQe+A12Hb8AhRq26LrZ/JpcUGGOn+Y7RsweNrtN/tE3MoK7ZeZDyx" crossorigin="anonymous"></script>
  <script src="js/rich-text-editor.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const editor = new RichTextEditor('#editor');
      
      document.getElementById('editorForm').addEventListener('submit', (e) => {
        document.getElementById('hiddenContent').value = editor.getContent();
      });
    });
  </script>
</body>
</html>

