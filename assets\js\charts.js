// Function to render a bar chart for ticket statuses
function renderTicketStatusChart(data) {
    const ctx = document.getElementById('ticketStatusChart').getContext('2d');
    const chart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'Ticket Statuses',
                data: data.values,
                backgroundColor: [
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(255, 206, 86, 0.2)',
                    'rgba(54, 162, 235, 0.2)'
                ],
                borderColor: [
                    'rgba(75, 192, 192, 1)',
                    'rgba(255, 99, 132, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(54, 162, 235, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Function to fetch ticket data and render the chart
function fetchTicketData() {
    $.ajax({
        url: 'api/tickets.php',
        method: 'GET',
        success: function(response) {
            const data = JSON.parse(response);
            renderTicketStatusChart(data);
        },
        error: function(error) {
            console.error('Error fetching ticket data:', error);
        }
    });
}

// Call the fetchTicketData function on page load
$(document).ready(function() {
    fetchTicketData();
});