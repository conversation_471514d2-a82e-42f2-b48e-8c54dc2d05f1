<?php
if (!defined('ROOT_PATH')) {
    define("ROOT_PATH", '/var/www/html/cs.1-to-all.com/app');
}
require_once ROOT_PATH.'/IncidentFrm/vendor/autoload.php';
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;

function text_to_readmore($text, $limit = 50) {
   
    $txt_readmore = mb_substr($text, 0, $limit, 'UTF-8'). '...';
    return $txt_readmore;
}
function getUserByUsername($username) {
    global $pdo;
    
    $query = "SELECT * FROM users WHERE username = :username";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
function getUserSettings($userId) {
    global $pdo;
    
    $query = "SELECT * FROM user_settings WHERE user_id = :user_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function updateUserSettings($userId, $settings) {
    global $pdo;
    
    // Check if settings exist for user
    $checkQuery = "SELECT COUNT(*) FROM user_settings WHERE user_id = :user_id";
    $checkStmt = $pdo->prepare($checkQuery);
    $checkStmt->bindParam(':user_id', $userId);
    $checkStmt->execute();
    
    if ($checkStmt->fetchColumn() > 0) {
        // Update existing settings
        $query = "UPDATE user_settings SET 
            email_notifications = :email_notifications,
            sms_notifications = :sms_notifications,
            system_notifications = :system_notifications,
            theme = :theme,
            language = :language,
            items_per_page = :items_per_page,
            updated_at = CURRENT_TIMESTAMP
            WHERE user_id = :user_id";
    } else {
        // Insert new settings
        $query = "INSERT INTO user_settings 
            (user_id, email_notifications, sms_notifications, system_notifications, 
             theme, language, items_per_page) 
            VALUES 
            (:user_id, :email_notifications, :sms_notifications, :system_notifications,
             :theme, :language, :items_per_page)";
    }
    
    try {
        $stmt = $pdo->prepare($query);
        
        // Bind all parameters
        $stmt->bindParam(':user_id', $userId);
        $stmt->bindParam(':email_notifications', $settings['email_notifications']);
        $stmt->bindParam(':sms_notifications', $settings['sms_notifications']);
        $stmt->bindParam(':system_notifications', $settings['system_notifications']);
        $stmt->bindParam(':theme', $settings['theme']);
        $stmt->bindParam(':language', $settings['language']);
        $stmt->bindParam(':items_per_page', $settings['items_per_page'], PDO::PARAM_INT);
        
        return $stmt->execute();
    } catch (PDOException $e) {
        error_log("Error updating user settings: " . $e->getMessage());
        throw $e;
    }
}


function getTicketPriorities() {
    return ['High', 'Medium', 'Low'];
}

function logActivity($activity) {
    // Code to log activities related to ticket management
}

function sendNotification($userId, $message) {
    // Code to send notifications to users
}

/**
 * Get CSS class for ticket table row based on priority and status
 * @param string $priority Ticket priority (High/Medium/Low)
 * @param string $status Ticket status (Open/In Progress/Pending/Closed)
 * @return string CSS class name
 */
function getTicketRowClass($priority, $status) {
    // First check status
    if ($status == 'Closed') {
        return 'table-success';
    }
    
    // Then check priority if not closed
    switch ($priority) {
        case 'High':
            return 'table-danger';
        case 'Medium':
            return 'table-warning';
        case 'Low':
            return '';
        default:
            return '';
    }
}

function getStatusBadgeClass($status) {
    switch ($status) {
        case 'Open':
            //return 'info';
            return 'danger';
        case 'In Progress':
            return 'primary';
        case 'Pending':
            return 'warning';
        case 'Closed':
            return 'success';
        default:
            return 'secondary';
    }
}
function getPriorityBadgeClass($priority) {
    switch ($priority) {
        case 'High':
            return 'danger';
        case 'Medium':
            return 'warning';
        case 'Low':
            return 'success';
        default:
            return 'secondary';
    }
}
function getUserRole($userId) {
    global $pdo;
    
    $query = "SELECT role FROM users WHERE id = :user_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetchColumn();
}
function getUserById($userId) {
    global $pdo;
    
    $query = "SELECT * FROM users WHERE id = :user_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':user_id', $userId);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}


/**
 * Get customer details by reference number
 * @param PDO $pdo Database connection
 * @param string $ref Reference number to search for
 * @return array Customer details or null if not found
 */
function getCustomerByRef($pdo,$ref) {
    
    $query = "SELECT Ref,CusCode,Site_Name FROM KCS_DB.Main WHERE Ref=:ref";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':ref', $ref);
    $stmt->execute();
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}


function sendInterimEmail($to, $subject, $body) {
    
    try {
        $mail = new PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;  // Add these constants to your config file
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = ''; // Use '' for no encryption, 'tls' or 'ssl' as needed
        $mail->SMTPAutoTLS = false; // Disable auto TLS if not using encryption
        $mail->CharSet = 'UTF-8';
        $mail->Encoding = 'base64';
        $mail->SMTPDebug = 0; // Set to 2 for verbose debug output
        $mail->Debugoutput = 'html'; // Change to 'error' for error output
        $mail->Port = SMTP_PORT;
        
        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        
        // Handle multiple recipients
        $recipients = explode(',', $to);
        foreach ($recipients as $recipient) {
            $mail->addAddress(trim($recipient));
        }
        $mail->addCC('<EMAIL>');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = '=?UTF-8?B?'.base64_encode($subject).'?=';// $subject;
        $mail->Body = $body;
        
        return $mail->send();
    } catch (Exception $e) {
        error_log("Email sending failed: " . $mail->ErrorInfo);
        return false;
    }
}

// ==================== INCIDENT MANAGEMENT FUNCTIONS ====================

/**
 * Generate incident number in format INCyyyymm-NNNN
 * Format: INC + year + month + '-' + running number
 * Resets sequence number every month
 * @param PDO $pdo Database connection
 * @return string Generated incident number
 */
function generateIncidentNumber($pdo) {
    $currentYear = date('Y');
    $currentMonth = date('m');
    $yearMonth = $currentYear . $currentMonth;

    try {
        // Get the last incident number for current year and month
        $query = "SELECT MAX(CAST(SUBSTRING(incident_no, 11) AS UNSIGNED)) as last_sequence
                  FROM incidents
                  WHERE SUBSTRING(incident_no, 4, 6) = :year_month";

        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':year_month', $yearMonth);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        // If no incidents exist for this year/month, start with 1
        $nextSequence = empty($result['last_sequence']) ? 1 : intval($result['last_sequence']) + 1;

        // Format: INC + YYYYMM + "-" + 4-digit sequence padded with zeros
        $incidentNumber = sprintf("INC%s-%04d", $yearMonth, $nextSequence);

        return $incidentNumber;
    } catch (PDOException $e) {
        error_log("Error generating incident number: " . $e->getMessage());
        return false;
    }
}

/**
 * Get incident status options
 * @return array List of incident statuses
 */
function getIncidentStatuses() {
    return ['Open', 'In Progress', 'Under Investigation', 'Resolved', 'Closed'];
}

/**
 * Get incident severity options
 * @return array List of incident severities
 */
function getIncidentSeverities() {
    return ['Critical', 'High', 'Medium', 'Low'];
}

/**
 * Get incident type options
 * @return array List of incident types
 */
function getIncidentTypes() {
    return [
        'Malware Attack',
        'Phishing Attack',
        'Data Breach',
        'Unauthorized Access',
        'DDoS Attack',
        'System Compromise',
        'Social Engineering',
        'Other'
    ];
}

/**
 * Get incident channel options
 * @return array List of incident channels
 */
function getIncidentChannels() {
    return [
        'Email',
        'Phone',
        'Internal System',
        'External Report',
        'Monitoring Alert'
    ];
}

/**
 * Get CSS class for incident table row based on severity and status
 * @param string $severity Incident severity (Critical/High/Medium/Low)
 * @param string $status Incident status
 * @return string CSS class name
 */
function getIncidentRowClass($severity, $status) {
    // First check status
    if ($status == 'Closed' || $status == 'Resolved') {
        return 'table-success';
    }

    // Then check severity if not closed/resolved
    switch ($severity) {
        case 'Critical':
            return 'table-danger';
        case 'High':
            return 'table-warning';
        case 'Medium':
            return 'table-info';
        case 'Low':
            return '';
        default:
            return '';
    }
}

/**
 * Get badge class for incident status
 * @param string $status Incident status
 * @return string Bootstrap badge class
 */
function getIncidentStatusBadgeClass($status) {
    switch ($status) {
        case 'Open':
            return 'danger';
        case 'In Progress':
            return 'primary';
        case 'Under Investigation':
            return 'warning';
        case 'Resolved':
            return 'info';
        case 'Closed':
            return 'success';
        default:
            return 'secondary';
    }
}

/**
 * Get badge class for incident severity
 * @param string $severity Incident severity
 * @return string Bootstrap badge class
 */
function getSeverityBadgeClass($severity) {
    switch ($severity) {
        case 'Critical':
            return 'danger';
        case 'High':
            return 'warning';
        case 'Medium':
            return 'info';
        case 'Low':
            return 'success';
        default:
            return 'secondary';
    }
}

/**
 * Count incidents by status
 * @param string $status Incident status
 * @return int Number of incidents with the given status
 */
function countIncidentsByStatus($status) {
    global $pdo;

    $query = "SELECT COUNT(*) FROM incidents WHERE status = :status";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':status', $status);
    $stmt->execute();

    return $stmt->fetchColumn();
}

/**
 * Count incidents by severity
 * @param string $severity Incident severity
 * @return int Number of incidents with the given severity
 */
function countIncidentsBySeverity($severity) {
    global $pdo;

    $query = "SELECT COUNT(*) FROM incidents WHERE severity = :severity";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':severity', $severity);
    $stmt->execute();

    return $stmt->fetchColumn();
}

/**
 * Get recent incidents
 * @param int $limit Number of incidents to retrieve
 * @return array Recent incidents
 */
function getRecentIncidents($limit = 10) {
    global $pdo;

    $query = "SELECT
        i.incident_no, i.event_date, i.reporter, i.type_of_event,
        i.severity, i.status, i.responsible_person, i.created_at,
        u.username AS created_by_username
        FROM incidents i
        LEFT JOIN users u ON i.created_by = u.id
        ORDER BY i.created_at DESC
        LIMIT :limit";

    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Format file size in human readable format
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
