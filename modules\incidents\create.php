<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Initialize variables
$errors = [];
$success_message = "";

// Get teams for responsible person dropdown
$teams_query = "SELECT id, name FROM teams ORDER BY name";
$teams_result = $pdo->query($teams_query);
$teams = $teams_result->fetchAll(PDO::FETCH_ASSOC);

// Get users for responsible person dropdown
$users_query = "SELECT id, username, full_name FROM users ORDER BY full_name, username";
$users_result = $pdo->query($users_query);
$users = $users_result->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Collect form data
    $event_date = trim($_POST["event_date"]);
    $reporter = trim($_POST["reporter"]);
    $channel = trim($_POST["channel"]);
    $ticket_no = trim($_POST["ticket_no"]);
    $is_ever_happened = isset($_POST["is_ever_happened"]) ? 1 : 0;
    $incident_ref_no = trim($_POST["incident_ref_no"]);
    $type_of_event = trim($_POST["type_of_event"]);
    $event_detail = trim($_POST["event_detail"]);
    $effect = trim($_POST["effect"]);
    $preliminary_operations = trim($_POST["preliminary_operations"]);
    $severity = trim($_POST["severity"]);
    $responsible_person = trim($_POST["responsible_person"]);
    $editing_details = trim($_POST["editing_details"]);
    $date_of_completion = trim($_POST["date_of_completion"]);
    $investigation_summary = trim($_POST["investigation_summary"]);
    $future_preventive_measures = trim($_POST["future_preventive_measures"]);
    $approver = trim($_POST["approver"]);
    $approval_date = trim($_POST["approval_date"]);

    // Validation
    if (empty($event_date)) {
        $errors[] = "Event Date is required.";
    }
    if (empty($reporter)) {
        $errors[] = "Reporter is required.";
    }
    if (empty($channel)) {
        $errors[] = "Channel is required.";
    }
    if (empty($type_of_event)) {
        $errors[] = "Type of Event is required.";
    }
    if (empty($event_detail)) {
        $errors[] = "Event Detail is required.";
    }
    if (empty($severity)) {
        $errors[] = "Severity is required.";
    }
    if (empty($responsible_person)) {
        $errors[] = "Responsible Person is required.";
    }

    // Handle file uploads
    $uploaded_files = [];
    if (!empty($_FILES['attach_files']['name'][0])) {
        $upload_dir = '../../uploads/incidents/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip'];
        $max_file_size = 10 * 1024 * 1024; // 10MB

        foreach ($_FILES['attach_files']['name'] as $key => $filename) {
            if ($_FILES['attach_files']['error'][$key] == UPLOAD_ERR_OK) {
                $file_extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

                if (!in_array($file_extension, $allowed_types)) {
                    $errors[] = "File type not allowed: " . $filename;
                    continue;
                }

                if ($_FILES['attach_files']['size'][$key] > $max_file_size) {
                    $errors[] = "File too large: " . $filename;
                    continue;
                }

                $new_filename = uniqid() . '_' . $filename;
                $upload_path = $upload_dir . $new_filename;

                if (move_uploaded_file($_FILES['attach_files']['tmp_name'][$key], $upload_path)) {
                    $uploaded_files[] = [
                        'original_name' => $filename,
                        'stored_name' => $new_filename,
                        'path' => $upload_path,
                        'size' => $_FILES['attach_files']['size'][$key],
                        'type' => $_FILES['attach_files']['type'][$key]
                    ];
                } else {
                    $errors[] = "Failed to upload file: " . $filename;
                }
            }
        }
    }

    // If no errors, insert incident into database
    if (empty($errors)) {
        try {
            $pdo->beginTransaction();

            $next_incident_number = generateIncidentNumber($pdo);
            $attach_files_list = !empty($uploaded_files) ? implode(',', array_column($uploaded_files, 'stored_name')) : null;

            $query = "INSERT INTO incidents (
                incident_no, event_date, reporter, channel, ticket_no,
                is_ever_happened, incident_ref_no, type_of_event, event_detail,
                effect, preliminary_operations, severity, attach_files, responsible_person,
                editing_details, date_of_completion, investigation_summary,
                future_preventive_measures, approver, approval_date,
                status, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'Open', ?, NOW())";

            $stmt = $pdo->prepare($query);
            $result = $stmt->execute([
                $next_incident_number, $event_date, $reporter, $channel, $ticket_no,
                $is_ever_happened, $incident_ref_no, $type_of_event, $event_detail,
                $effect, $preliminary_operations, $severity, $attach_files_list, $responsible_person,
                $editing_details, $date_of_completion, $investigation_summary,
                $future_preventive_measures, $approver, $approval_date,
                $_SESSION['user_id']
            ]);

            // Insert file attachments
            if ($result && !empty($uploaded_files)) {
                $file_query = "INSERT INTO incident_attachments (incident_no, file_name, file_path, file_size, file_type, uploaded_by) VALUES (?, ?, ?, ?, ?, ?)";
                $file_stmt = $pdo->prepare($file_query);

                foreach ($uploaded_files as $file) {
                    $file_stmt->execute([
                        $next_incident_number,
                        $file['original_name'],
                        $file['path'],
                        $file['size'],
                        $file['type'],
                        $_SESSION['user_id']
                    ]);
                }
            }

            if ($result) {
                $pdo->commit();
                $success_message = "Incident report created successfully! Incident No: " . $next_incident_number;
                if (!empty($uploaded_files)) {
                    $success_message .= " (" . count($uploaded_files) . " file(s) uploaded)";
                }
                // Clear form data
                $_POST = [];
            } else {
                $pdo->rollBack();
                $errors[] = "Failed to create incident report. Please try again.";
            }
        } catch (PDOException $e) {
            $pdo->rollBack();
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> 
                        Create Cybersecurity Incident Report
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success_message); ?>
                            <div class="mt-2">
                                <a href="list.php" class="btn btn-sm btn-primary">View All Incidents</a>
                                <a href="create.php" class="btn btn-sm btn-secondary">Create Another</a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Basic Information</h5>
                                
                                <div class="form-group">
                                    <label for="event_date">Event Date <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="event_date" name="event_date" 
                                           value="<?php echo isset($_POST['event_date']) ? htmlspecialchars($_POST['event_date']) : ''; ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="reporter">Reporter <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="reporter" name="reporter" 
                                           value="<?php echo isset($_POST['reporter']) ? htmlspecialchars($_POST['reporter']) : ''; ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="channel">Channel <span class="text-danger">*</span></label>
                                    <select class="form-control" id="channel" name="channel" required>
                                        <option value="">Select Channel</option>
                                        <option value="Email" <?php echo (isset($_POST['channel']) && $_POST['channel'] == 'Email') ? 'selected' : ''; ?>>Email</option>
                                        <option value="Phone" <?php echo (isset($_POST['channel']) && $_POST['channel'] == 'Phone') ? 'selected' : ''; ?>>Phone</option>
                                        <option value="Internal System" <?php echo (isset($_POST['channel']) && $_POST['channel'] == 'Internal System') ? 'selected' : ''; ?>>Internal System</option>
                                        <option value="External Report" <?php echo (isset($_POST['channel']) && $_POST['channel'] == 'External Report') ? 'selected' : ''; ?>>External Report</option>
                                        <option value="Monitoring Alert" <?php echo (isset($_POST['channel']) && $_POST['channel'] == 'Monitoring Alert') ? 'selected' : ''; ?>>Monitoring Alert</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="ticket_no">Ticket No.</label>
                                    <input type="text" class="form-control" id="ticket_no" name="ticket_no" 
                                           value="<?php echo isset($_POST['ticket_no']) ? htmlspecialchars($_POST['ticket_no']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_ever_happened" name="is_ever_happened" value="1"
                                               <?php echo (isset($_POST['is_ever_happened']) && $_POST['is_ever_happened']) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_ever_happened">
                                            Has this type of incident ever happened before?
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="incident_ref_no">Incident Reference No.</label>
                                    <input type="text" class="form-control" id="incident_ref_no" name="incident_ref_no" 
                                           value="<?php echo isset($_POST['incident_ref_no']) ? htmlspecialchars($_POST['incident_ref_no']) : ''; ?>">
                                </div>
                            </div>

                            <!-- Incident Details -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Incident Details</h5>
                                
                                <div class="form-group">
                                    <label for="type_of_event">Type of Event <span class="text-danger">*</span></label>
                                    <select class="form-control" id="type_of_event" name="type_of_event" required>
                                        <option value="">Select Event Type</option>
                                        <option value="Malware Attack" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'Malware Attack') ? 'selected' : ''; ?>>Malware Attack</option>
                                        <option value="Phishing Attack" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'Phishing Attack') ? 'selected' : ''; ?>>Phishing Attack</option>
                                        <option value="Data Breach" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'Data Breach') ? 'selected' : ''; ?>>Data Breach</option>
                                        <option value="Unauthorized Access" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'Unauthorized Access') ? 'selected' : ''; ?>>Unauthorized Access</option>
                                        <option value="DDoS Attack" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'DDoS Attack') ? 'selected' : ''; ?>>DDoS Attack</option>
                                        <option value="System Compromise" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'System Compromise') ? 'selected' : ''; ?>>System Compromise</option>
                                        <option value="Social Engineering" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'Social Engineering') ? 'selected' : ''; ?>>Social Engineering</option>
                                        <option value="Other" <?php echo (isset($_POST['type_of_event']) && $_POST['type_of_event'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="severity">Severity of the Incident <span class="text-danger">*</span></label>
                                    <select class="form-control" id="severity" name="severity" required>
                                        <option value="">Select Severity</option>
                                        <option value="Critical" <?php echo (isset($_POST['severity']) && $_POST['severity'] == 'Critical') ? 'selected' : ''; ?>>Critical</option>
                                        <option value="High" <?php echo (isset($_POST['severity']) && $_POST['severity'] == 'High') ? 'selected' : ''; ?>>High</option>
                                        <option value="Medium" <?php echo (isset($_POST['severity']) && $_POST['severity'] == 'Medium') ? 'selected' : ''; ?>>Medium</option>
                                        <option value="Low" <?php echo (isset($_POST['severity']) && $_POST['severity'] == 'Low') ? 'selected' : ''; ?>>Low</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="responsible_person">Responsible Person <span class="text-danger">*</span></label>
                                    <select class="form-control" id="responsible_person" name="responsible_person" required>
                                        <option value="">Select Responsible Person</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo htmlspecialchars($user['username']); ?>" 
                                                    <?php echo (isset($_POST['responsible_person']) && $_POST['responsible_person'] == $user['username']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="attach_files">Attach Files</label>
                                    <input type="file" class="form-control-file" id="attach_files" name="attach_files[]" multiple>
                                    <small class="form-text text-muted">You can select multiple files. Max size: 10MB per file.</small>
                                </div>
                            </div>
                        </div>

                        <!-- Event Details Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">Event Details</h5>
                                
                                <div class="form-group">
                                    <label for="event_detail">Event Detail <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="event_detail" name="event_detail" rows="4" required><?php echo isset($_POST['event_detail']) ? htmlspecialchars($_POST['event_detail']) : ''; ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="effect">Effect</label>
                                    <textarea class="form-control" id="effect" name="effect" rows="3"><?php echo isset($_POST['effect']) ? htmlspecialchars($_POST['effect']) : ''; ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="preliminary_operations">Preliminary Operations</label>
                                    <textarea class="form-control" id="preliminary_operations" name="preliminary_operations" rows="3"><?php echo isset($_POST['preliminary_operations']) ? htmlspecialchars($_POST['preliminary_operations']) : ''; ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Investigation and Resolution Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">Investigation and Resolution</h5>
                                
                                <div class="form-group">
                                    <label for="editing_details">Editing Details</label>
                                    <textarea class="form-control" id="editing_details" name="editing_details" rows="3"><?php echo isset($_POST['editing_details']) ? htmlspecialchars($_POST['editing_details']) : ''; ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="date_of_completion">Date of Completion</label>
                                    <input type="datetime-local" class="form-control" id="date_of_completion" name="date_of_completion" 
                                           value="<?php echo isset($_POST['date_of_completion']) ? htmlspecialchars($_POST['date_of_completion']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="investigation_summary">Investigation Summary</label>
                                    <textarea class="form-control" id="investigation_summary" name="investigation_summary" rows="4"><?php echo isset($_POST['investigation_summary']) ? htmlspecialchars($_POST['investigation_summary']) : ''; ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="future_preventive_measures">Future Preventive Measures</label>
                                    <textarea class="form-control" id="future_preventive_measures" name="future_preventive_measures" rows="4"><?php echo isset($_POST['future_preventive_measures']) ? htmlspecialchars($_POST['future_preventive_measures']) : ''; ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Section -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Approval</h5>
                                
                                <div class="form-group">
                                    <label for="approver">Approver</label>
                                    <select class="form-control" id="approver" name="approver">
                                        <option value="">Select Approver</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo htmlspecialchars($user['username']); ?>" 
                                                    <?php echo (isset($_POST['approver']) && $_POST['approver'] == $user['username']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="approval_date">Approval Date</label>
                                    <input type="datetime-local" class="form-control" id="approval_date" name="approval_date" 
                                           value="<?php echo isset($_POST['approval_date']) ? htmlspecialchars($_POST['approval_date']) : ''; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-danger btn-lg">
                                <i class="fas fa-save"></i> Create Incident Report
                            </button>
                            <a href="list.php" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>
