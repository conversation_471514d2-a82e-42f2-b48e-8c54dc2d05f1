<?php
date_default_timezone_set("Asia/Bangkok");
$Auth_Methode = 'AZURE-OAUTH';//AZURE-OAUTH->azure AD, ' '->local,NONE_AZURE-OAUTH
define("ROOT_PATH", '/var/www/html/cs.1-to-all.com/app'); 
define("BASE_URL", 'https://uat.1-to-all.com/IncidentFrm');
define("WEBSERVER_URL", 'https://uat.1-to-all.com');
define("PAGE_EXPIRE", 480 );  // 8 Hr.
define("WEBTITLE", 'Incident Management System' );
define("WEBTITLE_SUB", 'uat.1-to-all.com' );
define("COPYRIGHT", '1-TO-ALL CO., LTD. &copy; 2025' );

define('SMTP_HOST', '1-system.1-to-all.in.th');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', '$InTh@5uPP0rt!!');
define('SMTP_PORT', 587);
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Incident Management System');
