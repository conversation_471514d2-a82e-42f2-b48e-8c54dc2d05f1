/* Tabs styling */
.nav-tabs .nav-link {
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6 #dee2e6 #fff;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    background-color: #e9ecef;
}

/* Comment section styling */
.comments-section .comment:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.3s ease;
}

/* Forward form styling */
#forwardForm {
    border-left: 4px solid #17a2b8;
    padding-left: 15px;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

/* Team Management Styles */
.team-management-section {
    margin-top: 30px;
}

.team-management-section .nav-tabs .nav-link {
    color: #495057;
    background-color: #f8f9fa;
    border-color: #dee2e6 #dee2e6 #fff;
}

.team-management-section .nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 500;
}

.team-management-section .table th {
    background-color: #f8f9fa;
}

.team-management-section .table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* User selection highlight */
#user_id option:hover,
#team_id option:hover {
    background-color: #007bff;
    color: white;
}

/* Interim tab styling */
#interim-content {
    background-color: #f9f9f9;
    border-radius: 0 0 5px 5px;
    padding: 15px;
}

#interim-content .form-group label {
    font-weight: 500;
    color: #495057;
}

#interim-content .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Copy button styling */
.copy-btn {
    cursor: pointer;
    color: #6c757d;
    transition: color 0.2s;
}

.copy-btn:hover {
    color: #007bff;
}

/* History card styling */
.card.bg-light {
    border-left: 4px solid #17a2b8;
}

/* Interim history section */
.interim-history {
    font-size: 0.9rem;
    color: #6c757d;
}

.interim-history strong {
    color: #495057;
}


