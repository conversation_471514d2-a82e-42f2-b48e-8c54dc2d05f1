﻿//Polish , Polski
RTE_DefaultConfig.text_language = "język";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "anulować";	//"Cancel"
RTE_DefaultConfig.text_normal = "normalne";	//"Normal"
RTE_DefaultConfig.text_h1 = "Nagłówek 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "Nagłówek 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "Nagłówek 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "Nagłówek 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "Nagłówek 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "Nagłówek 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "Nagłówek 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "zamknij";	//"Close"
RTE_DefaultConfig.text_bold = "pogrubienie";	//"Bold"
RTE_DefaultConfig.text_italic = "kursywa";	//"Italic"
RTE_DefaultConfig.text_underline = "podkreślenie";	//"Underline"
RTE_DefaultConfig.text_strike = "Linia uderzeniowa";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "indeks górny";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Podkrupuł";	//"Subcript"
RTE_DefaultConfig.text_ucase = "Wielkie litery";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "Dolna obudowa";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Usuń format";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Wstaw łącze";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "Otwórz łącze";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Edytuj łącze";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Usuń łącze";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "Wysokość linii";	//"Line Height"
RTE_DefaultConfig.text_indent = "tiret";	//"Indent"
RTE_DefaultConfig.text_outdent = "Outdent (wyc.";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "Oferta bloku";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "Uporządkowana lista";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "Lista nieurządzonych";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "Wstaw regułę poziomą";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Wstaw datę";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "Wstawianie tabeli";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "Wstawianie obrazu";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Wstawianie wideo";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "Wstaw kod";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "Tworzenie pliku PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "Wstawianie emoji";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "Znaki specjalne";	//"Special characters"
RTE_DefaultConfig.text_characters = "znaków";	//"Characters"
RTE_DefaultConfig.text_fontname = "czcionki";	//"Font"
RTE_DefaultConfig.text_fontsize = "rozmiar";	//"Size"
RTE_DefaultConfig.text_forecolor = "Kolor tekstu";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Kolor pleców";	//"Back Color"
RTE_DefaultConfig.text_justify = "uzasadnić";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "Wyjustuj lewą stronę";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "Wyjustuj prawą";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "Wyjustuj środek";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "Wyjustuj pełne";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "Nie uzasadnij";	//"Justify None"
RTE_DefaultConfig.text_delete = "usunąć";	//"Delete"
RTE_DefaultConfig.text_save = "Zapisz plik";	//"Save file"
RTE_DefaultConfig.text_selectall = "Wybierz wszystkie";	//"Select All"
RTE_DefaultConfig.text_code = "Kod HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "podgląd";	//"Preview"
RTE_DefaultConfig.text_print = "drukowania";	//"Print"
RTE_DefaultConfig.text_undo = "cofnij";	//"Undo"
RTE_DefaultConfig.text_redo = "ponów";	//"Redo"
RTE_DefaultConfig.text_more = "Więcej...";	//"More..."
RTE_DefaultConfig.text_newdoc = "Nowy dok.";	//"New Doc"
RTE_DefaultConfig.text_help = "pomoc";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "Dopasuj do okna";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "Wyjdź z pełnego ekranu";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "Edytor obrazów";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "Style obrazów";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "Style wbudowane";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "Style akapitowe";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "Style łączy";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "style";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Klasy Css";	//"Css Classes"
RTE_DefaultConfig.text_url = "adres url";	//"Url"
RTE_DefaultConfig.text_byurl = "Według adresu URL";	//"By Url"
RTE_DefaultConfig.text_upload = "przesłać";	//"Upload"
RTE_DefaultConfig.text_size = "rozmiar";	//"Size"
RTE_DefaultConfig.text_text = "tekst";	//"Text"
RTE_DefaultConfig.text_opennewwin = "Otwórz w nowej karcie";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "wstawić";	//"Insert"
RTE_DefaultConfig.text_update = "aktualizacji";	//"Update"
RTE_DefaultConfig.text_find = "Znajdź i zamień";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "znaleźć";	//"Find"
RTE_DefaultConfig.text_replacewith = "zastąpić";	//"Replace"
RTE_DefaultConfig.text_findnext = "następnego";	//"Next"
RTE_DefaultConfig.text_replaceonce = "zastąpić";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Zamień wszystkie";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Obudowa dopasowania";	//"Match Case"
RTE_DefaultConfig.text_matchword = "Dopasuj słowo";	//"Match Word"
RTE_DefaultConfig.text_move_down = "Przenieś w dół";	//"Move Down"
RTE_DefaultConfig.text_move_up = "Przenieś w górę";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "Rozmiar automatyczny";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% szerokości";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% szerokości";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% szerokości";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% szerokości";	//"25% width"
RTE_DefaultConfig.text_controlsize = "Rozmiar zestawu";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Tekst alternatywny";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "uzasadnić";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "Podpis obrazu";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Scalanie komórek";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "Dzielenie komórek w pionie";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "Dzielenie komórek w poziomie";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "Kolor tekstu komórki";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "Kolor tylny komórki";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Wstaw wiersz powyżej";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Wstaw wiersz poniżej";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "Wstaw kolumnę do lewej";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "Wstaw kolumnę z prawej";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "Usuń kolumnę";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Usuń wiersz";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "Usuń tabelę";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "Rozmiar automatyczny";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "Nagłówek tabeli";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "Dodawanie nowego akapitu";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "wklej";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "wklej";	//"Paste"
RTE_DefaultConfig.text_pastetext = "Wklej tekst";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Wklej jako Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "Wklej słowo";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "Użyj klawiszy CTRL+V, aby wkleić zawartość do poniższego pola. \r\nUżycie zostanie wyczyszczone automatycznie.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "ustępów";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "ustępów";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "Przenieś w górę";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "Przenieś w dół";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "zduplikowane";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "usunąć";	//"Delete"
RTE_DefaultConfig.text_pmore = "Więcej..";	//"More.."
RTE_DefaultConfig.text_togglemore = "Więcej..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "Przełączanie obramowania";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "wyciąć";	//"Cut"
RTE_DefaultConfig.text_copy = "skopiować";	//"Copy"
RTE_DefaultConfig.text_copied = "kopiowane";	//"copied"
RTE_DefaultConfig.text_insertgallery = "Wstaw galerię";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Wstawianie dokumentu";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Wstaw szablon";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "podgląd";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "normalne";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tabletka";	//"Tablet"
RTE_DefaultConfig.text_table = "tabeli";	//"Table"
RTE_DefaultConfig.text_tablecell = "Komórka tabeli";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "Wiersz tabeli";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "Kolumna tabeli";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatyczne";	//"Automatic"
RTE_DefaultConfig.text_colormore = "więcej";	//"More"
RTE_DefaultConfig.text_colorpicker = "Próbnik kolorów";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "Paleta sieci Web";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Nazwane kolory";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "podstawowe";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "dodatek";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "Przeciąganie i upuszczanie";	//"Drag and drop"
RTE_DefaultConfig.text_or = "lub";	//"or"
RTE_DefaultConfig.text_clicktoupload = "Kliknij, aby przesłać";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "Domyślny podpis obrazu";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "szukaj";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "Dodawany tekst osiągnął limit znaków dla tego pola.";	//"The text to be added has reached the character limit for this field."
