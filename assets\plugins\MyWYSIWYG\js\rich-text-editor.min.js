class RichTextEditor{constructor(t){this.container=document.querySelector(t),this.init()}init(){this.createEditorDOM(),this.attachEventListeners()}createEditorDOM(){this.container.innerHTML='\n      <div class="rte-toolbar">\n        <button type="button" class="btn btn-sm btn-light" data-command="bold"><b>B</b></button>\n        <button type="button" class="btn btn-sm btn-light" data-command="italic"><i>I</i></button>\n        <button type="button" class="btn btn-sm btn-light" data-command="underline"><u>U</u></button>\n        \n        <select class="heading-select form-control form-control-sm">\n          <option value="">Normal</option>\n          <option value="h1">H1</option>\n          <option value="h2">H2</option>\n          <option value="h3">H3</option>\n          <option value="h4">H4</option>\n          <option value="h5">H5</option>\n        </select>\n        \n        <div class="alignment-buttons btn-group btn-group-sm">\n          <button type="button" class="btn btn-light" data-command="justifyLeft">Left</button>\n          <button type="button" class="btn btn-light" data-command="justifyCenter">Center</button>\n          <button type="button" class="btn btn-light" data-command="justifyRight">Right</button>\n        </div>\n        \n        <input type="color" id="colorPicker" class="form-control-sm" value="#000000">\n        <button type="button" class="btn btn-sm btn-light" data-command="foreColor">A</button>\n        \n        <button type="button" class="btn btn-sm btn-light" data-command="insertImage">Image</button>\n      </div>\n      <div class="rte-content" contenteditable="true"></div>\n      <input type="file" id="imageUpload" accept="image/*" style="display:none">\n    ',this.toolbar=this.container.querySelector(".rte-toolbar"),this.content=this.container.querySelector(".rte-content"),this.imageUpload=this.container.querySelector("#imageUpload"),this.headingSelect=this.container.querySelector(".heading-select"),this.colorPicker=this.container.querySelector("#colorPicker")}attachEventListeners(){this.toolbar.addEventListener("click",t=>{const e=t.target.closest("button");if(e){const t=e.dataset.command;"insertImage"===t?this.imageUpload.click():"foreColor"===t?document.execCommand(t,!1,this.colorPicker.value):document.execCommand(t,!1,null),this.content.focus()}}),this.colorPicker.addEventListener("change",()=>{document.execCommand("foreColor",!1,this.colorPicker.value),this.content.focus()}),this.headingSelect.addEventListener("change",()=>{const t=this.headingSelect.value;t?document.execCommand("formatBlock",!1,`<${t}>`):document.execCommand("formatBlock",!1,"<p>"),this.content.focus()}),this.imageUpload.addEventListener("change",t=>{const e=t.target.files[0];e&&this.insertImageAsBase64(e)}),this.content.addEventListener("click",t=>{if("IMG"===t.target.tagName){const e=this.content.querySelectorAll(".img-wrapper.selected");e.forEach(t=>{t.classList.remove("selected");const e=t.querySelectorAll(".resize-handle");e.forEach(t=>t.style.display="none")});let n=t.target.closest(".img-wrapper");n||(n=this.wrapImageWithResizeHandles(t.target)),n.classList.add("selected");const o=n.querySelectorAll(".resize-handle");o.forEach(t=>t.style.display="block")}else if(!t.target.classList.contains("resize-handle")){const t=this.content.querySelectorAll(".img-wrapper.selected");t.forEach(t=>{t.classList.remove("selected");const e=t.querySelectorAll(".resize-handle");e.forEach(t=>t.style.display="none")})}})}wrapImageWithResizeHandles(t){const e=document.createElement("div");e.className="img-wrapper",e.style.position="relative",e.style.display="inline-block",t.parentNode.insertBefore(e,t),e.appendChild(t);const n=document.createElement("div");let o,s,i,a;n.className="resize-handle",n.style.position="absolute",n.style.width="10px",n.style.height="10px",n.style.background="#4285f4",n.style.bottom="0",n.style.right="0",n.style.cursor="nwse-resize",n.style.display="none",e.appendChild(n),n.addEventListener("mousedown",e=>{o=e.clientX,s=e.clientY,i=t.offsetWidth,a=t.offsetHeight,document.addEventListener("mousemove",c),document.addEventListener("mouseup",l),e.preventDefault()});const c=e=>{const n=i+(e.clientX-o),c=a+(e.clientY-s);t.style.width=n+"px",t.style.height=c+"px"},l=()=>{document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",l)};return e}insertImageAsBase64(t){const e=new FileReader;e.onload=(t=>{const e=t.target.result,n=`<img src="${e}" style="max-width: 100%;">`;document.execCommand("insertHTML",!1,n)}),e.readAsDataURL(t)}getContent(){return this.content.innerHTML}}