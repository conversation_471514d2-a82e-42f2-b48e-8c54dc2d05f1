<?php
session_start();
header('Content-Type: application/json');

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../config/defined.conf.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

$term = isset($_GET['term']) ? trim($_GET['term']) : '';

if (strlen($term) < 2) {
    echo json_encode([]);
    exit;
}

try {
    $query = "SELECT cus.CusCode, cus.CusName,
        cus.CusAddress, m.Ref, m.Login
        FROM KCS_DB.Customers cus
        LEFT JOIN KCS_DB.Main m ON cus.CusCode = m.CusCode
        WHERE 
        m.Ref IS NOT NULL AND (
            cus.CusName LIKE :term
            OR cus.CusCode LIKE :term
            OR m.Ref LIKE :term
            OR m.Login LIKE :term
        ) LIMIT 10";

    $stmt = $pdo->prepare($query);
    $stmt->execute(['term' => "%$term%"]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($results);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
