<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: ../../modules/dashboard/index.php");
    exit();
}
$error = '';
if ($Auth_Methode == 'AZURE-OAUTH' && !isset($_SESSION["principal"])) {
  header("location: ".BASE_URL."/oauth/index.php?action=".@$_GET['action']);
}else{
    if($_SESSION["principal"]){
        login($_SESSION["principal"],$Auth_Methode);
    }
}
function login($principal, $Auth_Methode='') {
    global $pdo;
    $username='';
    $password='';
    $query = '';
    if ($Auth_Methode == 'AZURE-OAUTH') {
        $username = $principal;
        $query = "SELECT * FROM users WHERE email = :username LIMIT 1";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':username', $username);
        
    } else {
      $query = "SELECT * FROM users WHERE username = :username AND `password`=PASSWORD(:pass) LIMIT 1";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':pass', $password);
    }
        
        try {
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && $stmt->rowCount() > 0) {
                $_SESSION['user_id'] = $user['id'];
                // $user['username'];
                $_SESSION['userdata'] = $user;
                
                // Use absolute path for redirect
                header("Location: " . BASE_URL . "/modules/dashboard/index.php");
                exit();
            } else {
                $error = "Invalid username or password.";
            }
        } catch (PDOException $e) {
            $error = "Database error: " . $e->getMessage();
        }
}

if (isset($_POST['btnlogin'])) {
   
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);

    if (empty($username) || empty($password)) {
        $error = "Please fill in all fields.";
    } else {
       login($username, $password);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= WEBTITLE ?></title>
    <link rel="icon" href="<?= BASE_URL ?>/assets/images/favicon.png" sizes="32x32">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<div class="container">
    <h2 class="mt-5">Login</h1>
        <div class="row">
            <div class="col-md-5 offset-md-2">
                <div class="login-container">
                    <?php if (isset($error)): ?>
                        <div class="error"><?php echo $error; ?></div>
                    <?php endif; ?>
                    <form action="" method="POST">
                        <table class="table">
                            <tr>
                                <td><label for="ticketId" class="form-label">User name</label></td>
                                <td><input type="text" name="username" placeholder="Username" required></td>
                        </tr>
                            <tr>
                                <td><label for="assignedTo" class="form-label">Password</label></td>
                                <td><input type="password" name="password" placeholder="Password" required></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td><button type="submit" id="btnlogin" name="btnlogin" value="login" class="btn btn-primary">Login</button></td>
                            </tr>
                        </table>
                    </form>

                </div>
            </div>
        </div>
</div>
<p>
    <b>Demo:</b><br>
    user:d_amin ,role:admin ,password:1q2w3e4r<br>
    user:d_tech ,role:technician ,password:1q2w3e4r<br>
    user:d_cust ,role:customer_service ,password:1q2w3e4r<br>
</p>
<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
