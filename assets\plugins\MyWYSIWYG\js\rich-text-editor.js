class RichTextEditor {
  constructor(selector) {
    this.container = document.querySelector(selector);
    this.init();
  }

  init() {
    this.createEditorDOM();
    this.attachEventListeners();
  }

  createEditorDOM() {
    this.container.innerHTML = `
      <div class="rte-toolbar">
        <button type="button" class="btn btn-sm btn-light" data-command="bold"><b>B</b></button>
        <button type="button" class="btn btn-sm btn-light" data-command="italic"><i>I</i></button>
        <button type="button" class="btn btn-sm btn-light" data-command="underline"><u>U</u></button>
        
        <select class="heading-select form-control form-control-sm">
          <option value="">Normal</option>
          <option value="h1">H1</option>
          <option value="h2">H2</option>
          <option value="h3">H3</option>
          <option value="h4">H4</option>
          <option value="h5">H5</option>
        </select>
        
        <div class="alignment-buttons btn-group btn-group-sm">
          <button type="button" class="btn btn-light" data-command="justifyLeft">Left</button>
          <button type="button" class="btn btn-light" data-command="justifyCenter">Center</button>
          <button type="button" class="btn btn-light" data-command="justifyRight">Right</button>
        </div>
        
        <input type="color" id="colorPicker" class="form-control-sm" value="#000000">
        <button type="button" class="btn btn-sm btn-light" data-command="foreColor">A</button>
        
        <button type="button" class="btn btn-sm btn-light" data-command="insertImage">Image</button>
      </div>
      <div class="rte-content" contenteditable="true"></div>
      <input type="file" id="imageUpload" accept="image/*" style="display:none">
    `;
    
    this.toolbar = this.container.querySelector('.rte-toolbar');
    this.content = this.container.querySelector('.rte-content');
    this.imageUpload = this.container.querySelector('#imageUpload');
    this.headingSelect = this.container.querySelector('.heading-select');
    this.colorPicker = this.container.querySelector('#colorPicker');
  }

  attachEventListeners() {
    // Handle toolbar button clicks
    this.toolbar.addEventListener('click', (e) => {
      const button = e.target.closest('button');
      if (button) {
        const command = button.dataset.command;
        
        if (command === 'insertImage') {
          this.imageUpload.click();
        } else if (command === 'foreColor') {
          document.execCommand(command, false, this.colorPicker.value);
        } else {
          document.execCommand(command, false, null);
        }
        
        this.content.focus();
      }
    });

    // Handle color picker changes
    this.colorPicker.addEventListener('change', () => {
      document.execCommand('foreColor', false, this.colorPicker.value);
      this.content.focus();
    });

    // Handle heading selection
    this.headingSelect.addEventListener('change', () => {
      const headingTag = this.headingSelect.value;
      if (headingTag) {
        document.execCommand('formatBlock', false, `<${headingTag}>`);
      } else {
        document.execCommand('formatBlock', false, '<p>');
      }
      this.content.focus();
    });

    // Handle image upload
    this.imageUpload.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        this.insertImageAsBase64(file);
      }
    });

    // Make images resizable with custom handles
    this.content.addEventListener('click', (e) => {
      if (e.target.tagName === 'IMG') {
        // Remove resize handles from any previously selected images
        const selectedImages = this.content.querySelectorAll('.img-wrapper.selected');
        selectedImages.forEach(wrapper => {
          wrapper.classList.remove('selected');
          const handles = wrapper.querySelectorAll('.resize-handle');
          handles.forEach(handle => handle.style.display = 'none');
        });

        // Get or create wrapper
        let wrapper = e.target.closest('.img-wrapper');
        if (!wrapper) {
          // First time clicking this image, wrap it
          wrapper = this.wrapImageWithResizeHandles(e.target);
        }
        
        // Show resize handles
        wrapper.classList.add('selected');
        const handles = wrapper.querySelectorAll('.resize-handle');
        handles.forEach(handle => handle.style.display = 'block');
      } else if (!e.target.classList.contains('resize-handle')) {
        // Hide all resize handles when clicking elsewhere
        const selectedImages = this.content.querySelectorAll('.img-wrapper.selected');
        selectedImages.forEach(wrapper => {
          wrapper.classList.remove('selected');
          const handles = wrapper.querySelectorAll('.resize-handle');
          handles.forEach(handle => handle.style.display = 'none');
        });
      }
    });
  }

  wrapImageWithResizeHandles(img) {
    // Create wrapper
    const wrapper = document.createElement('div');
    wrapper.className = 'img-wrapper';
    wrapper.style.position = 'relative';
    wrapper.style.display = 'inline-block';
    
    // Replace image with wrapper containing image
    img.parentNode.insertBefore(wrapper, img);
    wrapper.appendChild(img);
    
    // Add resize handle
    const handle = document.createElement('div');
    handle.className = 'resize-handle';
    handle.style.position = 'absolute';
    handle.style.width = '10px';
    handle.style.height = '10px';
    handle.style.background = '#4285f4';
    handle.style.bottom = '0';
    handle.style.right = '0';
    handle.style.cursor = 'nwse-resize';
    handle.style.display = 'none';
    wrapper.appendChild(handle);
    
    // Add resize functionality
    let startX, startY, startWidth, startHeight;
    
    handle.addEventListener('mousedown', (e) => {
      startX = e.clientX;
      startY = e.clientY;
      startWidth = img.offsetWidth;
      startHeight = img.offsetHeight;
      
      document.addEventListener('mousemove', resize);
      document.addEventListener('mouseup', stopResize);
      
      e.preventDefault(); // Prevent text selection
    });
    
    const resize = (e) => {
      const width = startWidth + (e.clientX - startX);
      const height = startHeight + (e.clientY - startY);
      
      img.style.width = width + 'px';
      img.style.height = height + 'px';
    };
    
    const stopResize = () => {
      document.removeEventListener('mousemove', resize);
      document.removeEventListener('mouseup', stopResize);
    };
    
    return wrapper;
  }

  insertImageAsBase64(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const base64 = e.target.result;
      const img = `<img src="${base64}" style="max-width: 100%;">`;
      document.execCommand('insertHTML', false, img);
    };
    reader.readAsDataURL(file);
  }

  getContent() {
    return this.content.innerHTML;
  }
}





