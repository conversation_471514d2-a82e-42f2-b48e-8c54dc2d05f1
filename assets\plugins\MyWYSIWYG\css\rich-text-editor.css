.rte-toolbar {
  padding: 0.5rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-bottom: none;
  border-radius: 0.25rem 0.25rem 0 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.rte-toolbar button {
  margin-right: 0.25rem;
  margin-bottom: 0.25rem;
  padding: 0.375rem 0.75rem;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.rte-toolbar button:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.rte-toolbar .heading-select {
  margin: 0 0.5rem 0.25rem 0;
  padding: 0.375rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.rte-toolbar .alignment-buttons {
  display: flex;
  margin: 0 0.5rem 0.25rem 0;
}

.rte-toolbar .alignment-buttons button {
  margin-right: 0.125rem;
}

.rte-toolbar input[type="color"] {
  width: 2rem;
  height: 2rem;
  margin: 0 0.25rem 0.25rem 0;
  padding: 0;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  cursor: pointer;
}

.rte-content {
  min-height: 200px;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 0 0 0.25rem 0.25rem;
  overflow-y: auto;
  background-color: #fff;
}

.rte-content img {
  max-width: 100%;
  height: auto;
  border: 1px dashed #ced4da;
  margin: 0.25rem;
  border-radius: 0.25rem;
}

.rte-content img:hover {
  box-shadow: 0 0 5px rgba(0,0,0,0.2);
}

.img-wrapper {
  display: inline-block;
  position: relative;
  margin: 0.25rem;
}

.img-wrapper.selected {
  outline: 2px solid #007bff;
}

.img-wrapper img {
  display: block;
  max-width: 100%;
  border-radius: 0.25rem;
}

.resize-handle {
  position: absolute;
  width: 10px;
  height: 10px;
  background: #007bff;
  bottom: 0;
  right: 0;
  cursor: nwse-resize;
  z-index: 10;
  border-radius: 0 0 0.25rem 0;
}



