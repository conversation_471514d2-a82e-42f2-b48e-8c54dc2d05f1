<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Handle search and filters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$severity_filter = $_GET['severity'] ?? '';
$type_filter = $_GET['type'] ?? '';

// Build query with filters
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(i.incident_no LIKE :search OR i.reporter LIKE :search OR i.event_detail LIKE :search OR i.responsible_person LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "i.status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($severity_filter)) {
    $where_conditions[] = "i.severity = :severity";
    $params[':severity'] = $severity_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = "i.type_of_event = :type";
    $params[':type'] = $type_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Fetch incidents from the database
$sql = "SELECT
    i.incident_no,
    i.event_date,
    i.reporter,
    i.channel,
    i.ticket_no,
    i.type_of_event,
    i.severity,
    i.status,
    i.responsible_person,
    i.created_at,
    u.username AS created_by
FROM
    incidents i
    LEFT JOIN users u ON i.created_by = u.id
$where_clause
ORDER BY i.created_at DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$result = $stmt;

require_once '../../includes/header.php';
?>
<div class="container-fluid mt-4 small">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1>Cybersecurity Incident Reports</h1>
        <a href="create.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Incident Report
        </a>
    </div>

    <!-- Search and Filter Form -->
    <div class="card mb-3">
        <div class="card-body">
            <form method="GET" class="row">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search" placeholder="Search incidents..."
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <select class="form-control" name="status">
                        <option value="">All Statuses</option>
                        <?php foreach (getIncidentStatuses() as $status): ?>
                            <option value="<?php echo $status; ?>" <?php echo ($status_filter == $status) ? 'selected' : ''; ?>>
                                <?php echo $status; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-control" name="severity">
                        <option value="">All Severities</option>
                        <?php foreach (getIncidentSeverities() as $severity): ?>
                            <option value="<?php echo $severity; ?>" <?php echo ($severity_filter == $severity) ? 'selected' : ''; ?>>
                                <?php echo $severity; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-control" name="type">
                        <option value="">All Types</option>
                        <?php foreach (getIncidentTypes() as $type): ?>
                            <option value="<?php echo $type; ?>" <?php echo ($type_filter == $type) ? 'selected' : ''; ?>>
                                <?php echo $type; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-info btn-block">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </form>
            <?php if (!empty($search) || !empty($status_filter) || !empty($severity_filter) || !empty($type_filter)): ?>
                <div class="mt-2">
                    <a href="list.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-times"></i> Clear Filters
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="card shadow">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">Incident Reports List</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="incidentList">
                    <thead>
                        <tr>
                            <th>Incident No.</th>
                            <th>Event Date</th>
                            <th>Reporter</th>
                            <th>Channel</th>
                            <th>Ticket No.</th>
                            <th>Type of Event</th>
                            <th>Severity</th>
                            <th>Status</th>
                            <th>Responsible Person</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($result && $result->rowCount() > 0): ?>
                            <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)): ?>
                                <tr class="<?php echo getIncidentRowClass($row['severity'], $row['status']); ?>">
                                    <td><?php echo htmlspecialchars($row['incident_no']); ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($row['event_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($row['reporter']); ?></td>
                                    <td><?php echo htmlspecialchars($row['channel']); ?></td>
                                    <td><?php echo htmlspecialchars($row['ticket_no']); ?></td>
                                    <td><?php echo htmlspecialchars($row['type_of_event']); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo getSeverityBadgeClass($row['severity']); ?>">
                                            <?php echo htmlspecialchars($row['severity']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo getIncidentStatusBadgeClass($row['status']); ?>">
                                            <?php echo htmlspecialchars($row['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($row['responsible_person']); ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                    <td>
                                        <a href="view.php?incident_id=<?php echo urlencode($row['incident_no']); ?>" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <a href="edit.php?incident_id=<?php echo urlencode($row['incident_no']); ?>" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> Edit
                                        </a>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="11" class="text-center text-muted">No incident reports found.</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#incidentList').DataTable({
        "order": [[ 9, "desc" ]], // Sort by created_at column
        "pageLength": 25,
        "responsive": true
    });
});
</script>

<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>
