<?php
session_start();
// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once 'config/defined.conf.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is not logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: " . BASE_URL . "/modules/auth/login.php");
    exit();
}

// Pagination settings
$records_per_page = 100;
$current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($current_page - 1) * $records_per_page;

// Get total count of incidents (limit to last 1000)
$count_query = "SELECT COUNT(*) as total FROM (
    SELECT id FROM incidents ORDER BY created_at DESC LIMIT 1000
) as limited_incidents";
$count_stmt = $pdo->prepare($count_query);
$count_stmt->execute();
$total_records = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];
$total_pages = ceil($total_records / $records_per_page);

// Fetch incident data with pagination (last 1000 records)
$query = "SELECT
    i.incident_no,
    i.event_date,
    i.reporter,
    i.channel,
    i.ticket_no,
    i.is_ever_happened,
    i.incident_ref_no,
    i.type_of_event,
    i.event_detail,
    i.effect,
    i.preliminary_operations,
    i.severity,
    i.attach_files,
    i.responsible_person,
    i.editing_details,
    i.date_of_completion,
    i.investigation_summary,
    i.future_preventive_measures,
    i.approver,
    i.approval_date,
    i.status,
    i.created_at,
    u.username AS created_by
FROM (
    SELECT * FROM incidents ORDER BY created_at DESC LIMIT 1000
) i
LEFT JOIN users u ON i.created_by = u.id
ORDER BY i.created_at DESC
LIMIT :limit OFFSET :offset";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':limit', $records_per_page, PDO::PARAM_INT);
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$incidents = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once 'includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    Cybersecurity Incident Reports
                </h2>
                <div>
                    <a href="<?=BASE_URL?>/modules/incidents/create.php" class="btn btn-danger">
                        <i class="fas fa-plus"></i> Create New Incident
                    </a>
                    <a href="<?=BASE_URL?>/modules/incidents/list.php" class="btn btn-info">
                        <i class="fas fa-list"></i> Full Incident List
                    </a>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-header bg-danger text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Latest Incident Reports (Last 1000 Records)</h5>
                    <div>
                        <span class="badge badge-light">
                            Page <?php echo $current_page; ?> of <?php echo $total_pages; ?>
                        </span>
                        <span class="badge badge-light">
                            Total: <?php echo $total_records; ?> records
                        </span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0 small">
                            <thead class="thead-dark">
                                <tr>
                                    <th style="min-width: 120px;">Incident No.</th>
                                    <th style="min-width: 130px;">Event Date</th>
                                    <th style="min-width: 120px;">Reporter</th>
                                    <th style="min-width: 100px;">Channel</th>
                                    <th style="min-width: 100px;">Ticket No.</th>
                                    <th style="min-width: 80px;">Ever Happened</th>
                                    <th style="min-width: 120px;">Incident Ref.No</th>
                                    <th style="min-width: 130px;">Type of Event</th>
                                    <th style="min-width: 200px;">Event Detail</th>
                                    <th style="min-width: 150px;">Effect</th>
                                    <th style="min-width: 150px;">Preliminary Operations</th>
                                    <th style="min-width: 100px;">Severity</th>
                                    <th style="min-width: 100px;">Attach Files</th>
                                    <th style="min-width: 130px;">Responsible Person</th>
                                    <th style="min-width: 150px;">Editing Details</th>
                                    <th style="min-width: 130px;">Date of Completion</th>
                                    <th style="min-width: 200px;">Investigation Summary</th>
                                    <th style="min-width: 200px;">Future Preventive Measures</th>
                                    <th style="min-width: 120px;">Approver</th>
                                    <th style="min-width: 130px;">Approval Date</th>
                                    <th style="min-width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($incidents)): ?>
                                    <?php foreach ($incidents as $incident): ?>
                                        <tr class="<?php echo getIncidentRowClass($incident['severity'], $incident['status']); ?>">
                                            <td>
                                                <strong><?php echo htmlspecialchars($incident['incident_no']); ?></strong>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($incident['status']); ?></small>
                                            </td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($incident['event_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($incident['reporter']); ?></td>
                                            <td><?php echo htmlspecialchars($incident['channel']); ?></td>
                                            <td><?php echo htmlspecialchars($incident['ticket_no'] ?: '-'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo $incident['is_ever_happened'] ? 'warning' : 'success'; ?>">
                                                    <?php echo $incident['is_ever_happened'] ? 'Yes' : 'No'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($incident['incident_ref_no'] ?: '-'); ?></td>
                                            <td><?php echo htmlspecialchars($incident['type_of_event']); ?></td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['event_detail']), 80); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="max-width: 150px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['effect'] ?: '-'), 60); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="max-width: 150px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['preliminary_operations'] ?: '-'), 60); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php echo getSeverityBadgeClass($incident['severity']); ?>">
                                                    <?php echo htmlspecialchars($incident['severity']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($incident['attach_files']): ?>
                                                    <i class="fas fa-paperclip text-info" title="Has attachments"></i>
                                                    <small>Files</small>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($incident['responsible_person']); ?></td>
                                            <td>
                                                <div style="max-width: 150px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['editing_details'] ?: '-'), 60); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo $incident['date_of_completion'] ? date('d/m/Y H:i', strtotime($incident['date_of_completion'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['investigation_summary'] ?: '-'), 80); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['future_preventive_measures'] ?: '-'), 80); ?>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($incident['approver'] ?: '-'); ?></td>
                                            <td>
                                                <?php echo $incident['approval_date'] ? date('d/m/Y H:i', strtotime($incident['approval_date'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical btn-group-sm">
                                                    <a href="<?=BASE_URL?>/modules/incidents/view.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>"
                                                       class="btn btn-info btn-sm" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?=BASE_URL?>/modules/incidents/edit.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>"
                                                       class="btn btn-warning btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="21" class="text-center text-muted py-4">
                                            <i class="fas fa-exclamation-circle fa-2x mb-2"></i><br>
                                            No incident reports found.
                                            <br><br>
                                            <a href="<?=BASE_URL?>/modules/incidents/create.php" class="btn btn-danger">
                                                <i class="fas fa-plus"></i> Create First Incident Report
                                            </a>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="Incident pagination">
                        <ul class="pagination pagination-sm justify-content-center mb-0">
                            <!-- First Page -->
                            <?php if ($current_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=1" title="First Page">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $current_page - 1; ?>" title="Previous Page">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <li class="page-item <?php echo ($i == $current_page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                </li>
                            <?php endfor; ?>

                            <!-- Next Page -->
                            <?php if ($current_page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $current_page + 1; ?>" title="Next Page">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $total_pages; ?>" title="Last Page">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>

                    <!-- Page Info -->
                    <div class="text-center mt-2">
                        <small class="text-muted">
                            Showing <?php echo (($current_page - 1) * $records_per_page) + 1; ?> to
                            <?php echo min($current_page * $records_per_page, $total_records); ?> of
                            <?php echo $total_records; ?> records
                            (<?php echo $records_per_page; ?> records per page)
                        </small>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styles for the incident table */
.table th {
    background-color: #343a40;
    color: white;
    border-color: #454d55;
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.5rem 0.3rem;
    vertical-align: middle;
}

.table td {
    padding: 0.4rem 0.3rem;
    vertical-align: middle;
    font-size: 0.8rem;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}

/* Sticky header */
.table thead th {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Row hover effect */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,.075);
}

/* Badge adjustments */
.badge {
    font-size: 0.7rem;
}

/* Button group adjustments */
.btn-group-vertical .btn {
    margin-bottom: 2px;
}

.btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
}

/* Pagination styling */
.pagination-sm .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.page-item.active .page-link {
    background-color: #dc3545;
    border-color: #dc3545;
}
</style>

<?php
require_once 'includes/main_script_loader.php';
require_once 'includes/footer.php';
?>