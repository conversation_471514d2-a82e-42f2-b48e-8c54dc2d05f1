/* This file contains the custom styles for the Ticket Management system. */

body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

h1 {
    text-align: center;
    color: rgb(70, 70, 73);
}
h2 {
    color: rgb(70, 70, 73);
}
h3 {
    color: rgb(70, 70, 73);
}
.form-control_sm {
    font-size: small !important;
}
.button {
    display: inline-block;
    font-size: 16px;
    color: #ffffff;
    background: #5cb85c;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
}

.button:hover {
    background: #4cae4c;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.table th, .table td {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
}

.table th {
    background-color: #c1c4c9;
    color: rgb(97, 97, 100);
}

.table td {
    vertical-align: middle;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

.modal-header {
    background-color: #007bff;
    color: white;
}
.comments-section {
    font-size: small;
    /* max-height: 500px;
    overflow-y: auto; */
}

.comment {
    border-left: 4px solid #007bff;
}

.comment-content {
    white-space: pre-line;
    color: #444;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.075);
    transition: background-color 0.2s ease;
}

#draggableProfile {
    min-width: 250px;
    max-width: 300px;
    font-size: 0.875rem;
}

/* Help Note Styles */
.note-card {
    background: #fffdf0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: rotate(1deg);
    transition: all 0.3s ease;
}

.note-card:hover {
    transform: rotate(0deg) scale(1.02);
}

.note-header {
    background: rgba(0,0,0,0.05);
    border-bottom: 1px dashed #ccc;
    cursor: move;
}

.note-body {
    font-size: 0.9rem;
}

.help-item {
    border-bottom: 1px dashed rgba(0,0,0,0.1);
    padding-bottom: 0.5rem;
}

.help-item:last-child {
    border-bottom: none;
}

.help-item ul {
    margin-top: 0.3rem;
}

.help-item i {
    width: 16px;
    text-align: center;
    margin-right: 4px;
}

.note-header {
    background: rgba(0,0,0,0.05) !important;
    border-bottom: 1px dashed #ccc !important;
    color: #666 !important;
}

.note-body {
    background: transparent;
}

.note-table td, .note-table th {
    padding: 0.2rem;
    border: none !important;
    color: #666;
}

#dragHandle {
    cursor: move;
    user-select: none;
}

.note-footer {
    border-top: 1px dashed #ccc !important;
    background: transparent !important;
}

.btn-note {
    background: rgba(0,0,0,0.1);
    border: 1px solid rgba(0,0,0,0.2);
    color: #666;
}

.btn-note:hover {
    background: rgba(0,0,0,0.2);
    color: #333;
}
/* Add this CSS to style the issue details box */
.issue-details-box {
    background-color: #fff3f3;
    border: 2px solid #dc3545;
    border-left: 8px solid #dc3545;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
    font-size: 1.1em;
    line-height: 1.6;
    color: #333;
    position: relative;
    margin: 10px 0;
    transition: all 0.3s ease;
}

.issue-details-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.issue-details-box::before {
    content: "!";
    position: absolute;
    top: -15px;
    right: -15px;
    background: #dc3545;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 20px;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.badge-info {
    font-size: 0.85em;
    font-weight: normal;
    padding: 0.4em 0.6em;
}

.working-time {
    white-space: nowrap;
}