<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Get incident ID from URL
$incident_id = $_GET['incident_id'] ?? '';

if (empty($incident_id)) {
    header("Location: list.php");
    exit();
}

// Fetch incident details
$query = "SELECT
    i.*,
    u.username AS created_by_username,
    u.full_name AS created_by_name
FROM
    incidents i
    LEFT JOIN users u ON i.created_by = u.id
WHERE
    i.incident_no = ?";

$stmt = $pdo->prepare($query);
$stmt->execute([$incident_id]);
$incident = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$incident) {
    header("Location: list.php?error=Incident not found");
    exit();
}

// Fetch incident attachments
$attachments_query = "SELECT * FROM incident_attachments WHERE incident_no = ? ORDER BY uploaded_at DESC";
$attachments_stmt = $pdo->prepare($attachments_query);
$attachments_stmt->execute([$incident_id]);
$attachments = $attachments_stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h2>
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                    Incident Report: <?php echo htmlspecialchars($incident['incident_no']); ?>
                </h2>
                <div>
                    <a href="edit.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>" 
                       class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="list.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Incident No.:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['incident_no']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Event Date:</strong></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($incident['event_date'])); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Reporter:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['reporter']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Channel:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['channel']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Ticket No.:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['ticket_no'] ?: 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Ever Happened Before:</strong></td>
                                    <td>
                                        <span class="badge badge-<?php echo $incident['is_ever_happened'] ? 'warning' : 'success'; ?>">
                                            <?php echo $incident['is_ever_happened'] ? 'Yes' : 'No'; ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Incident Ref. No.:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['incident_ref_no'] ?: 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        <span class="badge badge-<?php echo getIncidentStatusBadgeClass($incident['status']); ?>">
                                            <?php echo htmlspecialchars($incident['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Incident Classification -->
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">Incident Classification</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Type of Event:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['type_of_event']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Severity:</strong></td>
                                    <td>
                                        <span class="badge badge-<?php echo getSeverityBadgeClass($incident['severity']); ?> badge-lg">
                                            <?php echo htmlspecialchars($incident['severity']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Responsible Person:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['responsible_person']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Created By:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['created_by_name'] ?: $incident['created_by_username']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>Created At:</strong></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($incident['created_at'])); ?></td>
                                </tr>
                                <?php if ($incident['updated_at']): ?>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($incident['updated_at'])); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Event Details -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Event Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <h6><strong>Event Detail:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($incident['event_detail'])); ?>
                                    </div>
                                </div>
                                
                                <?php if ($incident['effect']): ?>
                                <div class="col-md-6 mb-3">
                                    <h6><strong>Effect:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($incident['effect'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($incident['preliminary_operations']): ?>
                                <div class="col-md-6 mb-3">
                                    <h6><strong>Preliminary Operations:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($incident['preliminary_operations'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Investigation and Resolution -->
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Investigation and Resolution</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php if ($incident['editing_details']): ?>
                                <div class="col-md-6 mb-3">
                                    <h6><strong>Editing Details:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($incident['editing_details'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($incident['date_of_completion']): ?>
                                <div class="col-md-6 mb-3">
                                    <h6><strong>Date of Completion:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo date('d/m/Y H:i', strtotime($incident['date_of_completion'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($incident['investigation_summary']): ?>
                                <div class="col-md-12 mb-3">
                                    <h6><strong>Investigation Summary:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($incident['investigation_summary'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($incident['future_preventive_measures']): ?>
                                <div class="col-md-12 mb-3">
                                    <h6><strong>Future Preventive Measures:</strong></h6>
                                    <div class="border p-3 bg-light">
                                        <?php echo nl2br(htmlspecialchars($incident['future_preventive_measures'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Approval Information -->
            <?php if ($incident['approver'] || $incident['approval_date']): ?>
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">Approval Information</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <?php if ($incident['approver']): ?>
                                <tr>
                                    <td><strong>Approver:</strong></td>
                                    <td><?php echo htmlspecialchars($incident['approver']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <?php if ($incident['approval_date']): ?>
                                <tr>
                                    <td><strong>Approval Date:</strong></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($incident['approval_date'])); ?></td>
                                </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Attachments -->
            <?php if (!empty($attachments)): ?>
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">Attachments (<?php echo count($attachments); ?>)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($attachments as $attachment): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-2">
                                                <i class="fas fa-file"></i>
                                                <?php echo htmlspecialchars($attachment['file_name']); ?>
                                            </h6>
                                            <p class="card-text small text-muted mb-2">
                                                Size: <?php echo formatFileSize($attachment['file_size']); ?><br>
                                                Type: <?php echo htmlspecialchars($attachment['file_type']); ?><br>
                                                Uploaded: <?php echo date('d/m/Y H:i', strtotime($attachment['uploaded_at'])); ?>
                                            </p>
                                            <a href="download.php?file_id=<?php echo $attachment['id']; ?>"
                                               class="btn btn-sm btn-primary" target="_blank">
                                                <i class="fas fa-download"></i> Download
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.badge-lg {
    font-size: 1.1em;
    padding: 0.5em 0.75em;
}
</style>

<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>
