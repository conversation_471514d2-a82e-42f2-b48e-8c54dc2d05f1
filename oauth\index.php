<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once('../config/defined.conf.php');
session_start();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication System</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <h1>Hi,Welcome to Authen System.</h1>
   
    <?php

    //request login
    //must register Apps array before use
    //https://oauth.1-to-all.com/AzureOAuth/login/?Apps=csmon
            if (isset($_GET['principal']))
            {
                echo "<p>Principal(user):".$_GET['principal']."</p>";
                echo "<p>Id:".$_GET['id']."</p>";
                echo "<p>JobTitle:".$_GET['jobtitle']."</p>";

                $_SESSION['principal']=$_GET['principal'];
                $_SESSION['id']=$_GET['id'];
                $_SESSION['jobtitle']=$_GET['jobtitle'];
                //var_dump($_SESSION);
                header("location: ".BASE_URL."/modules/dashboard/index.php");
                exit();
            }
            if ($_GET['action'] == 'logout') {
                unset($_SESSION['principal']);
                unset($_SESSION['id']);
                unset($_SESSION['jobtitle']);
                session_destroy();
                header('Location: ' .'https://login.microsoftonline.com/common/wsfederation?wa=wsignout1.0');
                exit;
            }

            if(!isset($_SESSION['principal']) || $_SESSION['principal']==''){
                
                header('Location: ' . 'https://oauth.1-to-all.com/AzureOAuth/login/?Apps=cs');
                exit;
            }else{
                echo "<p>Already logged in.</p>";
                echo "<p>Principal(user):".$_SESSION['principal']."</p>";
                echo "<p>Id:".$_SESSION['id']."</p>";
                echo "<p>JobTitle:".$_SESSION['jobtitle']."</p>";

                header("location: ".BASE_URL."/modules/dashboard/index.php");
                exit();
            }

     ?>
    <!-- Your content here -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>