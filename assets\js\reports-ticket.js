// Add to existing script or create new script block
$(document).ready(function() {
    // Initialize help note
    $("#reportHelpNote").draggable({
        handle: "#helpDragHandle",
        containment: "window",
        snap: "window",
        snapMode: "outer"
    });

    // Minimize/Maximize functionality
    $("#minimizeHelp").click(function() {
        $("#helpContent").slideToggle();
        $(this).find('i').toggleClass('fa-plus fa-minus');
        
        // Save state
        localStorage.setItem('reportHelpMinimized', 
            $("#helpContent").is(':hidden'));
    });

    // Restore last position
    const savedPosition = localStorage.getItem("reportHelpPosition");
    if (savedPosition) {
        const position = JSON.parse(savedPosition);
        $("#reportHelpNote").css(position);
    }

    // Save position when dragged
    $("#reportHelpNote").on("dragstop", function(event, ui) {
        localStorage.setItem("reportHelpPosition", JSON.stringify({
            left: ui.position.left,
            top: ui.position.top
        }));
    });

    // Start minimized
    $("#helpContent").hide();
    $("#minimizeHelp i").addClass('fa-plus').removeClass('fa-minus');
});