/*!
 * tui-image-editor.min.js
 * @version 3.9.0
 * <AUTHOR> FE Development Lab <<EMAIL>>
 * @license MIT
 */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("tui-code-snippet"),require("fabric").fabric,require("tui-color-picker")):"function"==typeof define&&define.amd?define(["tui-code-snippet","fabric","tui-color-picker"],t):"object"==typeof exports?exports.ImageEditor=t(require("tui-code-snippet"),require("fabric").fabric,require("tui-color-picker")):(e.tui=e.tui||{},e.tui.ImageEditor=t(e.tui&&e.tui.util,e.fabric,e.tui&&e.tui.colorPicker))}(window,function(n,i,o){return s={},a.m=r=[function(e,t,n){"use strict";var i,o=n(4),a=(i=o)&&i.__esModule?i:{default:i};e.exports={HELP_MENUS:["undo","redo","reset","delete","deleteAll"],SHAPE_DEFAULT_OPTIONS:{lockSkewingX:!0,lockSkewingY:!0,lockUniScaling:!1,bringForward:!0,isRegular:!1},CROPZONE_DEFAULT_OPTIONS:{hasRotatingPoint:!1,hasBorders:!1,lockScalingFlip:!0,lockRotation:!0,lockSkewingX:!0,lockSkewingY:!0},componentNames:a.default.keyMirror("IMAGE_LOADER","CROPPER","FLIP","ROTATION","FREE_DRAWING","LINE","TEXT","ICON","FILTER","SHAPE"),commandNames:{CLEAR_OBJECTS:"clearObjects",LOAD_IMAGE:"loadImage",FLIP_IMAGE:"flip",ROTATE_IMAGE:"rotate",ADD_OBJECT:"addObject",REMOVE_OBJECT:"removeObject",APPLY_FILTER:"applyFilter",REMOVE_FILTER:"removeFilter",ADD_ICON:"addIcon",CHANGE_ICON_COLOR:"changeIconColor",ADD_SHAPE:"addShape",CHANGE_SHAPE:"changeShape",ADD_TEXT:"addText",CHANGE_TEXT:"changeText",CHANGE_TEXT_STYLE:"changeTextStyle",ADD_IMAGE_OBJECT:"addImageObject",RESIZE_CANVAS_DIMENSION:"resizeCanvasDimension",SET_OBJECT_PROPERTIES:"setObjectProperties",SET_OBJECT_POSITION:"setObjectPosition"},eventNames:{OBJECT_ACTIVATED:"objectActivated",OBJECT_MOVED:"objectMoved",OBJECT_SCALED:"objectScaled",OBJECT_CREATED:"objectCreated",OBJECT_ROTATED:"objectRotated",TEXT_EDITING:"textEditing",TEXT_CHANGED:"textChanged",ICON_CREATE_RESIZE:"iconCreateResize",ICON_CREATE_END:"iconCreateEnd",ADD_TEXT:"addText",ADD_OBJECT:"addObject",ADD_OBJECT_AFTER:"addObjectAfter",MOUSE_DOWN:"mousedown",MOUSE_UP:"mouseup",MOUSE_MOVE:"mousemove",REDO_STACK_CHANGED:"redoStackChanged",UNDO_STACK_CHANGED:"undoStackChanged",SELECTION_CLEARED:"selectionCleared",SELECTION_CREATED:"selectionCreated"},drawingModes:a.default.keyMirror("NORMAL","CROPPER","FREE_DRAWING","LINE_DRAWING","TEXT","SHAPE"),keyCodes:{Z:90,Y:89,C:67,V:86,SHIFT:16,BACKSPACE:8,DEL:46,ARROW_DOWN:40,ARROW_UP:38},fObjectOptions:{SELECTION_STYLE:{borderColor:"red",cornerColor:"green",cornerSize:10,originX:"center",originY:"center",transparentCorners:!1}},rejectMessages:{addedObject:"The object is already added.",flip:"The flipX and flipY setting values are not changed.",invalidDrawingMode:"This operation is not supported in the drawing mode.",invalidParameters:"Invalid parameters.",isLock:"The executing command state is locked.",loadImage:"The background image is empty.",loadingImageFailed:"Invalid image loaded.",noActiveObject:"There is no active object.",noObject:"The object is not in canvas.",redo:"The promise of redo command is reject.",rotation:"The current angle is same the old angle.",undo:"The promise of undo command is reject.",unsupportedOperation:"Unsupported operation.",unsupportedType:"Unsupported object type."},defaultIconPath:{"icon-arrow":"M40 12V0l24 24-24 24V36H0V12h40z","icon-arrow-2":"M49,32 H3 V22 h46 l-18,-18 h12 l23,23 L43,50 h-12 l18,-18  z ","icon-arrow-3":"M43.349998,27 L17.354,53 H1.949999 l25.996,-26 L1.949999,1 h15.404 L43.349998,27  z ","icon-star":"M35,54.557999 l-19.912001,10.468 l3.804,-22.172001 l-16.108,-15.7 l22.26,-3.236 L35,3.746 l9.956,20.172001 l22.26,3.236 l-16.108,15.7 l3.804,22.172001  z ","icon-star-2":"M17,31.212 l-7.194,4.08 l-4.728,-6.83 l-8.234,0.524 l-1.328,-8.226 l-7.644,-3.14 l2.338,-7.992 l-5.54,-6.18 l5.54,-6.176 l-2.338,-7.994 l7.644,-3.138 l1.328,-8.226 l8.234,0.522 l4.728,-6.83 L17,-24.312 l7.194,-4.08 l4.728,6.83 l8.234,-0.522 l1.328,8.226 l7.644,3.14 l-2.338,7.992 l5.54,6.178 l-5.54,6.178 l2.338,7.992 l-7.644,3.14 l-1.328,8.226 l-8.234,-0.524 l-4.728,6.83  z ","icon-polygon":"M3,31 L19,3 h32 l16,28 l-16,28 H19  z ","icon-location":"M24 62C8 45.503 0 32.837 0 24 0 10.745 10.745 0 24 0s24 10.745 24 24c0 8.837-8 21.503-24 38zm0-28c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10z","icon-heart":"M49.994999,91.349998 l-6.96,-6.333 C18.324001,62.606995 2.01,47.829002 2.01,29.690998 C2.01,14.912998 13.619999,3.299999 28.401001,3.299999 c8.349,0 16.362,5.859 21.594,12 c5.229,-6.141 13.242001,-12 21.591,-12 c14.778,0 26.390999,11.61 26.390999,26.390999 c0,18.138 -16.314001,32.916 -41.025002,55.374001 l-6.96,6.285  z ","icon-bubble":"M44 48L34 58V48H12C5.373 48 0 42.627 0 36V12C0 5.373 5.373 0 12 0h40c6.627 0 12 5.373 12 12v24c0 6.627-5.373 12-12 12h-8z"},defaultRotateRangeValus:{realTimeEvent:!0,min:-360,max:360,value:0},defaultDrawRangeValus:{min:5,max:30,value:12},defaultShapeStrokeValus:{realTimeEvent:!0,min:2,max:300,value:3},defaultTextRangeValus:{realTimeEvent:!0,min:10,max:100,value:50},defaultFilterRangeValus:{tintOpacityRange:{realTimeEvent:!0,min:0,max:1,value:.7,useDecimal:!0},removewhiteDistanceRange:{realTimeEvent:!0,min:0,max:1,value:.2,useDecimal:!0},brightnessRange:{realTimeEvent:!0,min:-1,max:1,value:0,useDecimal:!0},noiseRange:{realTimeEvent:!0,min:0,max:1e3,value:100},pixelateRange:{realTimeEvent:!0,min:2,max:20,value:4},colorfilterThresholeRange:{realTimeEvent:!0,min:0,max:1,value:.2,useDecimal:!0}}}},function(e,t){e.exports=n},function(e,t,n){n(46),n(47),n(62),n(66),e.exports=n(12).Promise},function(e,t,n){"use strict";var i,o=n(79),a=(i=o)&&i.__esModule?i:{default:i};var r={};e.exports={create:function(e){var t=r[e];if(t){for(var n=arguments.length,i=Array(1<n?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return new a.default(t,i)}return null},register:function(e){r[e.name]=e}}},function(e,t,n){"use strict";var o=n(1),a="tui-image-editor-",r=Math.min,s=Math.max,i=!1;e.exports={clamp:function(e,t,n){var i=void 0;return n<t&&(i=t,t=n,n=i),s(t,r(e,n))},keyMirror:function(){for(var t={},e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];return(0,o.forEach)(n,function(e){t[e]=e}),t},makeStyleText:function(e){var n="";return(0,o.forEach)(e,function(e,t){n+=t+": "+e+";"}),n},getProperties:function(e,t){var n={},i=t.length,o=0,a=void 0;for(o=0;o<i;o+=1)n[a=t[o]]=e[a];return n},toInteger:function(e){return parseInt(e,10)},toCamelCase:function(e){return e.replace(/-([a-z])/g,function(e,t){return t.toUpperCase()})},isSupportFileApi:function(){return!!(window.File&&window.FileList&&window.FileReader)},getRgb:function(e,t){return 4===e.length&&(e=""+e+e.slice(1,4)),"rgba("+parseInt(e.slice(1,3),16)+", "+parseInt(e.slice(3,5),16)+", "+parseInt(e.slice(5,7),16)+", "+(t||1)+")"},sendHostName:function(){i||(i=!0,(0,o.sendHostname)("image-editor","UA-129999381-1"))},styleLoad:function(e,t){var n=document.getElementsByTagName("head")[0],i=document.createElement("link"),o=encodeURIComponent(e);t&&(i.id=t),i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.setAttribute("href","data:text/css;charset=UTF-8,"+o),n.appendChild(i)},getSelector:function(t){return function(e){return t.querySelector(e)}},base64ToBlob:function(e){var n="",t=void 0,i=void 0,o=void 0;t=e.replace(/data:(image\/.+);base64,/,function(e,t){return n=t,""});var a=(t=atob(t)).length;for(i=new Uint8Array(a),o=0;o<a;o+=1)i[o]=t.charCodeAt(o);return new Blob([i],{type:n})},fixFloatingPoint:function(e){return Number(e.toFixed(2))},assignmentForDestroy:function(n){(0,o.forEach)(n,function(e,t){n[t]=null})},cls:function(e,t){var n=0<arguments.length&&void 0!==e?e:"",i=1<arguments.length&&void 0!==t?t:"";return"."===n.charAt(0)?"."+a+i+n.slice(1):a+i+n}}},function(e,t){e.exports=i},function(e,t,n){var i=n(36)("wks"),o=n(37),a=n(7).Symbol,r="function"==typeof a;(e.exports=function(e){return i[e]||(i[e]=r&&a[e]||(r?a:o)("Symbol."+e))}).store=i},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t,n){"use strict";function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var o=(function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}(a,[{key:"fire",value:function(){for(var e=this.graphics,t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return this.graphics.fire.apply(e,n)}},{key:"setCanvasImage",value:function(e,t){this.graphics.setCanvasImage(e,t)}},{key:"getCanvasElement",value:function(){return this.graphics.getCanvasElement()}},{key:"getCanvas",value:function(){return this.graphics.getCanvas()}},{key:"getCanvasImage",value:function(){return this.graphics.getCanvasImage()}},{key:"getImageName",value:function(){return this.graphics.getImageName()}},{key:"getEditor",value:function(){return this.graphics.getEditor()}},{key:"getName",value:function(){return this.name}},{key:"setImageProperties",value:function(e,t){this.graphics.setImageProperties(e,t)}},{key:"setCanvasCssDimension",value:function(e){this.graphics.setCanvasCssDimension(e)}},{key:"setCanvasBackstoreDimension",value:function(e){this.graphics.setCanvasBackstoreDimension(e)}},{key:"adjustCanvasDimension",value:function(){this.graphics.adjustCanvasDimension()}}]),a);function a(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),this.name=e,this.graphics=t}e.exports=o},function(e,t,n){"use strict";function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(t,"__esModule",{value:!0});var o=(function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}(l,[{key:"selector",value:function(e){return this.subMenuElement.querySelector(e)}},{key:"colorPickerChangeShow",value:function(t){this.colorPickerControls.forEach(function(e){t!==e&&e.hide()})}},{key:"getButtonType",value:function(e,t){return e.className.match(RegExp("("+t.join("|")+")"))[0]}},{key:"changeClass",value:function(e,t,n){e.classList.remove(t),e.classList.add(n)}},{key:"changeStandbyMode",value:function(){}},{key:"changeStartMode",value:function(){}},{key:"_makeSubMenuElement",value:function(e){var t=e.locale,n=e.name,i=e.iconStyle,o=e.makeSvgIcon,a=e.templateHtml,r=document.createElement("div");r.className="tui-image-editor-menu-"+n,r.innerHTML=a({locale:t,iconStyle:i,makeSvgIcon:o}),this.subMenuElement.appendChild(r)}}]),l);function l(e,t){var n=t.locale,i=t.name,o=t.makeSvgIcon,a=t.menuBarPosition,r=t.templateHtml,s=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),this.subMenuElement=e,this.menuBarPosition=a,this.toggleDirection="top"===a?"down":"up",this.colorPickerControls=[],this.usageStatistics=s,this.eventHandler={},this._makeSubMenuElement({locale:n,name:i,makeSvgIcon:o,templateHtml:r})}t.default=o},function(e,t,n){var i=n(19),o=n(34);e.exports=n(13)?function(e,t,n){return i.f(e,t,o(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var i=n(20);e.exports=function(e){if(!i(e))throw TypeError(e+" is not an object!");return e}},function(e,t){var n=e.exports={version:"2.4.0"};"number"==typeof __e&&(__e=n)},function(e,t,n){e.exports=!n(33)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){e.exports={}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(1)),r=s(n(88));function s(e){return e&&e.__esModule?e:{default:e}}var l=["#000000","#2a2a2a","#545454","#7e7e7e","#a8a8a8","#d2d2d2","#ffffff","","#ff4040","#ff6518","#ffbb3b","#03bd9e","#00a9ff","#515ce6","#9e5fff","#ff5583"],c=(i(u,[{key:"destroy",value:function(){var n=this;this._removeEvent(),this.picker.destroy(),this.colorpickerElement.innerHTML="",a.default.forEach(this,function(e,t){n[t]=null})}},{key:"_changeColorElement",value:function(e){e?(this.colorElement.classList.remove("transparent"),this.colorElement.style.backgroundColor=e):(this.colorElement.style.backgroundColor="#fff",this.colorElement.classList.add("transparent"))}},{key:"_makePickerButtonElement",value:function(e){this.colorpickerElement.classList.add("tui-image-editor-button"),this.colorElement=document.createElement("div"),this.colorElement.className="color-picker-value",e?this.colorElement.style.backgroundColor=e:this.colorElement.classList.add("transparent")}},{key:"_makePickerLayerElement",value:function(e,t){var n=document.createElement("label"),i=document.createElement("div");this.pickerControl=document.createElement("div"),this.pickerControl.className="color-picker-control",this.pickerElement=document.createElement("div"),this.pickerElement.className="color-picker",n.innerHTML=t,i.className="triangle",this.pickerControl.appendChild(this.pickerElement),this.pickerControl.appendChild(i),e.appendChild(this.pickerControl),e.appendChild(this.colorElement),e.appendChild(n)}},{key:"_addEvent",value:function(){var t=this;this.picker.on("selectColor",function(e){t._changeColorElement(e.color),t._color=e.color,t.fire("change",e.color)}),this.eventHandler={pickerToggle:this._pickerToggleEventHandler.bind(this),pickerHide:function(){return t.hide()}},this.colorpickerElement.addEventListener("click",this.eventHandler.pickerToggle),document.body.addEventListener("click",this.eventHandler.pickerHide)}},{key:"_removeEvent",value:function(){this.colorpickerElement.removeEventListener("click",this.eventHandler.pickerToggle),document.body.removeEventListener("click",this.eventHandler.pickerHide),this.picker.off()}},{key:"_pickerToggleEventHandler",value:function(e){var t=e.target,n=t&&this._isElementInColorPickerControl(t);(!n||n&&this._isPaletteButton(t))&&(this._show=!this._show,this.pickerControl.style.display=this._show?"block":"none",this._setPickerControlPosition(),this.fire("changeShow",this)),e.stopPropagation()}},{key:"_isPaletteButton",value:function(e){return"tui-colorpicker-palette-button"===e.className}},{key:"_isElementInColorPickerControl",value:function(e){for(var t=e;t!==document.body&&t;){if(t===this.pickerControl)return!0;t=t.parentNode}return!1}},{key:"hide",value:function(){this._show=!1,this.pickerControl.style.display="none"}},{key:"_setPickerControlPosition",value:function(){var e=this.pickerControl.style,t=this._colorpickerElement.clientWidth/2+2,n=this.pickerControl.offsetWidth/2-t,i=-1*(this.pickerControl.offsetHeight+10);"down"===this._toggleDirection&&(i=30),e.top=i+"px",e.left="-"+n+"px"}},{key:"color",get:function(){return this._color},set:function(e){this._color=e,this._changeColorElement(e)}}]),u);function u(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"#7e7e7e",n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"up",i=arguments[3];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),this.colorpickerElement=e,this.usageStatistics=i,this._show=!1,this._colorpickerElement=e,this._toggleDirection=n,this._makePickerButtonElement(t),this._makePickerLayerElement(e,e.getAttribute("title")),this._color=t,this.picker=r.default.create({container:this.pickerElement,preset:l,color:t,usageStatistics:this.usageStatistics}),this._addEvent()}a.default.CustomEvents.mixin(c),t.default=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a,r=n(1),s=(a=r)&&a.__esModule?a:{default:a},l=n(4),c=n(0);var u=/(-?)([0-9]*)[^0-9]*([0-9]*)/g,d=(i(h,[{key:"destroy",value:function(){var n=this;this._removeClickEvent(),this._removeDragEvent(),this._removeInputEvent(),this.rangeElement.innerHTML="",s.default.forEach(this,function(e,t){n[t]=null})}},{key:"trigger",value:function(e){this.fire(e,this._value)}},{key:"_getRangeWidth",value:function(){function e(e){return(0,l.toInteger)(window.getComputedStyle(e,null).width)}return e(this.rangeElement)-e(this.pointer)}},{key:"_drawRangeElement",value:function(){this.rangeElement.classList.add("tui-image-editor-range"),this.bar=document.createElement("div"),this.bar.className="tui-image-editor-virtual-range-bar",this.subbar=document.createElement("div"),this.subbar.className="tui-image-editor-virtual-range-subbar",this.pointer=document.createElement("div"),this.pointer.className="tui-image-editor-virtual-range-pointer",this.bar.appendChild(this.subbar),this.bar.appendChild(this.pointer),this.rangeElement.appendChild(this.bar)}},{key:"_addInputEvent",value:function(){this.rangeInputElement&&(this.rangeInputElement.addEventListener("keydown",this.eventHandler.changeInputWithArrow),this.rangeInputElement.addEventListener("keyup",this.eventHandler.changeInput),this.rangeInputElement.addEventListener("blur",this.eventHandler.changeInputFinally))}},{key:"_removeInputEvent",value:function(){this.rangeInputElement&&(this.rangeInputElement.removeEventListener("keydown",this.eventHandler.changeInputWithArrow),this.rangeInputElement.removeEventListener("keyup",this.eventHandler.changeInput),this.rangeInputElement.removeEventListener("blur",this.eventHandler.changeInputFinally))}},{key:"_changeValueWithInputKeyEvent",value:function(e){var t=e.keyCode,n=e.target;if(!([c.keyCodes.ARROW_UP,c.keyCodes.ARROW_DOWN].indexOf(t)<0)){var i=Number(n.value);if(!((i=this._valueUpDownForKeyEvent(i,t))<this._min||i>this._max)){var o=(0,l.clamp)(i,this._min,this.max);this.value=o,this.fire("change",o,!1)}}}},{key:"_valueUpDownForKeyEvent",value:function(e,t){var n=this._useDecimal?.1:1;return t===c.keyCodes.ARROW_UP?e+=n:t===c.keyCodes.ARROW_DOWN&&(e-=n),e}},{key:"_changeValueWithInput",value:function(e,t){var n=t.keyCode,i=t.target;if(!(0<=[c.keyCodes.ARROW_UP,c.keyCodes.ARROW_DOWN].indexOf(n))){var o=this._filterForInputText(i.value),a=!o||isNaN(o);if(i.value=o,!a){var r=(this._useDecimal?Number:l.toInteger)(o);r=(0,l.clamp)(r,this._min,this.max),this.value=r,this.fire("change",r,e)}}}},{key:"_addClickEvent",value:function(){this.rangeElement.addEventListener("click",this.eventHandler.changeSlideFinally)}},{key:"_removeClickEvent",value:function(){this.rangeElement.removeEventListener("click",this.eventHandler.changeSlideFinally)}},{key:"_addDragEvent",value:function(){this.pointer.addEventListener("mousedown",this.eventHandler.startChangingSlide)}},{key:"_removeDragEvent",value:function(){this.pointer.removeEventListener("mousedown",this.eventHandler.startChangingSlide)}},{key:"_changeSlide",value:function(e){var t=e.screenX-this.firstPosition,n=this.firstLeft+t;n=(n=n>this.rangeWidth?this.rangeWidth:n)<0?0:n,this.pointer.style.left=n+"px",this.subbar.style.right=this.rangeWidth-n+"px";var i=n/this.rangeWidth,o=this._absMax*i+this._min,a=this._useDecimal?o:(0,l.toInteger)(o);this.value!==a&&(this.value=a,this.realTimeEvent&&this.fire("change",this._value,!1))}},{key:"_changeSlideFinally",value:function(e){if(e.stopPropagation(),"tui-image-editor-range"===e.target.className){var t=e.offsetX/this.rangeWidth,n=this._absMax*t+this._min;this.pointer.style.left=t*this.rangeWidth+"px",this.subbar.style.right=(1-t)*this.rangeWidth+"px",this.value=n,this.fire("change",n,!0)}}},{key:"_startChangingSlide",value:function(e){this.firstPosition=e.screenX,this.firstLeft=(0,l.toInteger)(this.pointer.style.left)||0,document.addEventListener("mousemove",this.eventHandler.changeSlide),document.addEventListener("mouseup",this.eventHandler.stopChangingSlide)}},{key:"_stopChangingSlide",value:function(){this.fire("change",this._value,!0),document.removeEventListener("mousemove",this.eventHandler.changeSlide),document.removeEventListener("mouseup",this.eventHandler.stopChangingSlide)}},{key:"_filterForInputText",value:function(e){return e.replace(u,"$1$2$3")}},{key:"max",set:function(e){this._max=e,this._absMax=-1*this._min+this._max,this.value=this._value},get:function(){return this._max}},{key:"value",get:function(){return this._value},set:function(e){var t=((e=this._useDecimal?e:(0,l.toInteger)(e))-this._min)*this.rangeWidth/this._absMax;this.rangeWidth<t&&(t=this.rangeWidth),this.pointer.style.left=t+"px",this.subbar.style.right=this.rangeWidth-t+"px",this._value=e,this.rangeInputElement&&(this.rangeInputElement.value=e)}}]),h);function h(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),this._value=t.value||0,this.rangeElement=e.slider,this.rangeInputElement=e.input,this._drawRangeElement(),this.rangeWidth=this._getRangeWidth(),this._min=t.min||0,this._max=t.max||100,this._useDecimal=t.useDecimal,this._absMax=-1*this._min+this._max,this.realTimeEvent=t.realTimeEvent||!1,this.eventHandler={startChangingSlide:this._startChangingSlide.bind(this),stopChangingSlide:this._stopChangingSlide.bind(this),changeSlide:this._changeSlide.bind(this),changeSlideFinally:this._changeSlideFinally.bind(this),changeInput:this._changeValueWithInput.bind(this,!1),changeInputFinally:this._changeValueWithInput.bind(this,!0),changeInputWithArrow:this._changeValueWithInputKeyEvent.bind(this)},this._addClickEvent(),this._addDragEvent(),this._addInputEvent(),this.value=t.value,this.trigger("change")}s.default.CustomEvents.mixin(d),t.default=d},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a,r=n(42),s=(a=r)&&a.__esModule?a:{default:a};var l=s.default.create,c=s.default.types,u=(i(d,[{key:"getName",value:function(){return this.name}},{key:"start",value:function(){throw new Error(l(c.UN_IMPLEMENTATION,"start"))}},{key:"stop",value:function(){throw new Error(l(c.UN_IMPLEMENTATION,"stop"))}}]),d);function d(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),this.name=e}e.exports=u},function(e,t,n){var a=n(25);e.exports=function(i,o,e){if(a(i),void 0===o)return i;switch(e){case 1:return function(e){return i.call(o,e)};case 2:return function(e,t){return i.call(o,e,t)};case 3:return function(e,t,n){return i.call(o,e,t,n)}}return function(){return i.apply(o,arguments)}}},function(e,t,n){var i=n(11),o=n(49),a=n(50),r=Object.defineProperty;t.f=n(13)?Object.defineProperty:function(e,t,n){if(i(e),t=a(t,!0),i(n),o)try{return r(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t){var n=Math.ceil,i=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(0<e?i:n)(e)}},function(e,t){e.exports=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t,n){var i=n(20),o=n(7).document,a=i(o)&&i(o.createElement);e.exports=function(e){return a?o.createElement(e):{}}},function(e,t,n){var i=n(57),o=n(24);e.exports=function(e){return i(o(e))}},function(e,t,n){var i=n(36)("keys"),o=n(37);e.exports=function(e){return i[e]||(i[e]=o(e))}},function(e,t,n){var i=n(19).f,o=n(21),a=n(6)("toStringTag");e.exports=function(e,t,n){e&&!o(e=n?e:e.prototype,a)&&i(e,a,{configurable:!0,value:t})}},function(e,t,n){"use strict";function y(){return this}var _=n(31),k=n(32),E=n(51),w=n(10),x=n(21),C=n(14),O=n(52),S=n(29),M=n(60),j=n(6)("iterator"),T=!([].keys&&"next"in[].keys()),P="values";e.exports=function(e,t,n,i,o,a,r){O(n,t,i);function s(e){if(!T&&e in v)return v[e];switch(e){case"keys":case P:return function(){return new n(this,e)}}return function(){return new n(this,e)}}var l,c,u,d=t+" Iterator",h=o==P,f=!1,v=e.prototype,p=v[j]||v["@@iterator"]||o&&v[o],g=p||s(o),m=o?h?s("entries"):g:void 0,b="Array"==t&&v.entries||p;if(b&&(u=M(b.call(new e)))!==Object.prototype&&(S(u,d,!0),_||x(u,j)||w(u,j,y)),h&&p&&p.name!==P&&(f=!0,g=function(){return p.call(this)}),_&&!r||!T&&!f&&v[j]||w(v,j,g),C[t]=g,C[d]=y,o)if(l={values:h?g:s(P),keys:a?g:s("keys"),entries:m},r)for(c in l)c in v||E(v,c,l[c]);else k(k.P+k.F*(T||f),t,l);return l}},function(e,t){e.exports=!0},function(e,t,n){var p=n(7),g=n(12),m=n(18),b=n(10),y="prototype",_=function(e,t,n){var i,o,a,r=e&_.F,s=e&_.G,l=e&_.S,c=e&_.P,u=e&_.B,d=e&_.W,h=s?g:g[t]||(g[t]={}),f=h[y],v=s?p:l?p[t]:(p[t]||{})[y];for(i in s&&(n=t),n)(o=!r&&v&&void 0!==v[i])&&i in h||(a=o?v[i]:n[i],h[i]=s&&"function"!=typeof v[i]?n[i]:u&&o?m(a,p):d&&v[i]==a?function(i){function e(e,t,n){if(this instanceof i){switch(arguments.length){case 0:return new i;case 1:return new i(e);case 2:return new i(e,t)}return new i(e,t,n)}return i.apply(this,arguments)}return e[y]=i[y],e}(a):c&&"function"==typeof a?m(Function.call,a):a,c&&((h.virtual||(h.virtual={}))[i]=a,e&_.R&&f&&!f[i]&&b(f,i,a)))};_.F=1,_.G=2,_.S=4,_.P=8,_.B=16,_.W=32,_.U=64,_.R=128,e.exports=_},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,n){var i=n(23),o=Math.min;e.exports=function(e){return 0<e?o(i(e),9007199254740991):0}},function(e,t,n){var i=n(7),o="__core-js_shared__",a=i[o]||(i[o]={});e.exports=function(e){return a[e]||(a[e]={})}},function(e,t){var n=0,i=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+i).toString(36))}},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){e.exports=n(7).document&&document.documentElement},function(e,t,n){var o=n(22),a=n(6)("toStringTag"),r="Arguments"==o(function(){return arguments}());e.exports=function(e){var t,n,i;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),a))?n:r?o(t):"Object"==(i=o(t))&&"function"==typeof t.callee?"Arguments":i}},function(e,t,n){function i(){var e=+this;if(b.hasOwnProperty(e)){var t=b[e];delete b[e],t()}}function o(e){i.call(e.data)}var a,r,s,l=n(18),c=n(73),u=n(39),d=n(26),h=n(7),f=h.process,v=h.setImmediate,p=h.clearImmediate,g=h.MessageChannel,m=0,b={},y="onreadystatechange";v&&p||(v=function(e){for(var t=[],n=1;n<arguments.length;)t.push(arguments[n++]);return b[++m]=function(){c("function"==typeof e?e:Function(e),t)},a(m),m},p=function(e){delete b[e]},"process"==n(22)(f)?a=function(e){f.nextTick(l(i,e,1))}:g?(s=(r=new g).port2,r.port1.onmessage=o,a=l(s.postMessage,s,1)):h.addEventListener&&"function"==typeof postMessage&&!h.importScripts?(a=function(e){h.postMessage(e+"","*")},h.addEventListener("message",o,!1)):a=y in d("script")?function(e){u.appendChild(d("script"))[y]=function(){u.removeChild(this),i.call(e)}}:function(e){setTimeout(l(i,e,1),0)}),e.exports={set:v,clear:p}},function(e,t,n){"use strict";var i,o=n(1),a=(i=o)&&i.__esModule?i:{default:i};var r=(0,n(4).keyMirror)("UN_IMPLEMENTATION","NO_COMPONENT_NAME"),s="Should implement a method: ",l="Should set a component name",c={UN_IMPLEMENTATION:function(e){return s+e},NO_COMPONENT_NAME:function(){return l}};e.exports={types:a.default.extend({},r),create:function(e){e=e.toLowerCase();for(var t=c[e],n=arguments.length,i=Array(1<n?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];return t.apply(void 0,i)}}},function(e,t,n){"use strict";n(44);var i,o=n(45),a=(i=o)&&i.__esModule?i:{default:i};n(132),n(133),n(134),n(135),n(136),n(137),n(138),n(139),n(140),n(141),n(142),n(143),n(144),n(145),n(146),n(147),n(148),n(149),n(150),n(151),e.exports=a.default},function(e,t,n){"use strict";Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;if(!document.documentElement.contains(t))return null;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null})
/*! @source http://purl.eligrey.com/github/classList.js/blob/master/classList.js */,"document"in window.self&&("classList"in document.createElement("_")&&(!document.createElementNS||"classList"in document.createElementNS("http://www.w3.org/2000/svg","g"))||function(e){if("Element"in e){var t="classList",n="prototype",i=e.Element[n],o=Object,a=String[n].trim||function(){return this.replace(/^\s+|\s+$/g,"")},r=Array[n].indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(t in this&&this[t]===e)return t;return-1},s=function(e,t){this.name=e,this.code=DOMException[e],this.message=t},l=function(e,t){if(""===t)throw new s("SYNTAX_ERR","An invalid or illegal string was specified");if(/\s/.test(t))throw new s("INVALID_CHARACTER_ERR","String contains an invalid character");return r.call(e,t)},c=function(e){for(var t=a.call(e.getAttribute("class")||""),n=t?t.split(/\s+/):[],i=0,o=n.length;i<o;i++)this.push(n[i]);this._updateClassName=function(){e.setAttribute("class",this.toString())}},u=c[n]=[],d=function(){return new c(this)};if(s[n]=Error[n],u.item=function(e){return this[e]||null},u.contains=function(e){return-1!==l(this,e+="")},u.add=function(){for(var e,t=arguments,n=0,i=t.length,o=!1;-1===l(this,e=t[n]+"")&&(this.push(e),o=!0),++n<i;);o&&this._updateClassName()},u.remove=function(){var e,t,n=arguments,i=0,o=n.length,a=!1;do{for(t=l(this,e=n[i]+"");-1!==t;)this.splice(t,1),a=!0,t=l(this,e)}while(++i<o);a&&this._updateClassName()},u.toggle=function(e,t){e+="";var n=this.contains(e),i=n?!0!==t&&"remove":!1!==t&&"add";return i&&this[i](e),!0===t||!1===t?t:!n},u.toString=function(){return this.join(" ")},o.defineProperty){var h={get:d,enumerable:!0,configurable:!0};try{o.defineProperty(i,t,h)}catch(e){void 0!==e.number&&-2146823252!==e.number||(h.enumerable=!1,o.defineProperty(i,t,h))}}else o[n].__defineGetter__&&i.__defineGetter__(t,d)}}(window.self),function(){var e=document.createElement("_");if(e.classList.add("c1","c2"),!e.classList.contains("c2")){var t=function(e){var i=DOMTokenList.prototype[e];DOMTokenList.prototype[e]=function(e){var t,n=arguments.length;for(t=0;t<n;t++)e=arguments[t],i.call(this,e)}};t("add"),t("remove")}if(e.classList.toggle("c3",!1),e.classList.contains("c3")){var n=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(e,t){return 1 in arguments&&!this.contains(e)==!t?t:n.call(this,e)}}e=null}()),
/*!
 * @copyright Copyright (c) 2017 IcoMoon.io
 * @license   Licensed under MIT license
 *            See https://github.com/Keyamoon/svgxuse
 * @version   1.2.6
 */
function(){if("undefined"!=typeof window&&window.addEventListener){var e,t,n,y=Object.create(null),_=function(){clearTimeout(t),t=setTimeout(e,100)},k=function(){},E="http://www.w3.org/1999/xlink";e=function(){var e,t,n,i,o,a,r,s,l,c,u,d,h,f=0;function v(){var e;0===--f&&(k(),window.addEventListener("resize",_,!1),window.addEventListener("orientationchange",_,!1),k=window.MutationObserver?((e=new MutationObserver(_)).observe(document.documentElement,{childList:!0,subtree:!0,attributes:!0}),function(){try{e.disconnect(),window.removeEventListener("resize",_,!1),window.removeEventListener("orientationchange",_,!1)}catch(e){}}):(document.documentElement.addEventListener("DOMSubtreeModified",_,!1),function(){document.documentElement.removeEventListener("DOMSubtreeModified",_,!1),window.removeEventListener("resize",_,!1),window.removeEventListener("orientationchange",_,!1)}))}function p(e){return function(){!0!==y[e.base]&&(e.useEl.setAttributeNS(E,"xlink:href","#"+e.hash),e.useEl.hasAttribute("href")&&e.useEl.setAttribute("href","#"+e.hash))}}function g(i){return function(){var e,t=document.body,n=document.createElement("x");i.onload=null,n.innerHTML=i.responseText,(e=n.getElementsByTagName("svg")[0])&&(e.setAttribute("aria-hidden","true"),e.style.position="absolute",e.style.width=0,e.style.height=0,e.style.overflow="hidden",t.insertBefore(e,t.firstChild)),v()}}function m(e){return function(){e.onerror=null,e.ontimeout=null,v()}}for(k(),s=document.getElementsByTagName("use"),o=0;o<s.length;o+=1){try{t=s[o].getBoundingClientRect()}catch(e){t=!1}e=(r=(i=s[o].getAttribute("href")||s[o].getAttributeNS(E,"href")||s[o].getAttribute("xlink:href"))&&i.split?i.split("#"):["",""])[0],n=r[1],a=t&&0===t.left&&0===t.right&&0===t.top&&0===t.bottom,t&&0===t.width&&0===t.height&&!a?(s[o].hasAttribute("href")&&s[o].setAttributeNS(E,"xlink:href",i),e.length&&(!0!==(l=y[e])&&setTimeout(p({useEl:s[o],base:e,hash:n}),0),void 0===l&&(c=e,h=d=u=void 0,window.XMLHttpRequest&&(u=new XMLHttpRequest,d=b(location),h=b(c),u=void 0===u.withCredentials&&""!==h&&h!==d?XDomainRequest||void 0:XMLHttpRequest),void 0!==u&&(l=new u,(y[e]=l).onload=g(l),l.onerror=m(l),l.ontimeout=m(l),l.open("GET",e),l.send(),f+=1)))):a?e.length&&y[e]&&setTimeout(p({useEl:s[o],base:e,hash:n}),0):void 0===y[e]?y[e]=!0:y[e].onload&&(y[e].abort(),delete y[e].onload,y[e]=!0)}function b(e){var t;return void 0!==e.protocol?t=e:(t=document.createElement("a")).href=e,t.protocol.replace(/:/g,"")+t.host}s="",f+=1,v()},n=function(){window.removeEventListener("load",n,!1),t=setTimeout(e,0)},"complete"!==document.readyState?window.addEventListener("load",n,!1):n()}}()},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=v(n(1)),r=v(n(2)),s=v(n(78)),l=v(n(80)),c=v(n(107)),u=v(n(3)),d=v(n(109)),h=v(n(0)),f=n(4);function v(e){return e&&e.__esModule?e:{default:e}}function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var g=h.default.eventNames,m=h.default.commandNames,b=h.default.keyCodes,y=h.default.rejectMessages,_=a.default.isUndefined,k=a.default.forEach,E=a.default.CustomEvents,w=g.MOUSE_DOWN,x=g.OBJECT_MOVED,C=g.OBJECT_SCALED,O=g.OBJECT_ACTIVATED,S=g.OBJECT_ROTATED,M=g.ADD_TEXT,j=g.ADD_OBJECT,T=g.TEXT_EDITING,P=g.TEXT_CHANGED,I=g.ICON_CREATE_RESIZE,A=g.ICON_CREATE_END,R=g.SELECTION_CLEARED,D=g.SELECTION_CREATED,L=g.ADD_OBJECT_AFTER,z=(i(H,[{key:"_setSelectionStyle",value:function(t,e){var n=e.applyCropSelectionStyle,i=e.applyGroupSelectionStyle;t&&this._graphics.setSelectionStyle(t),n&&this._graphics.setCropSelectionStyle(t),i&&this.on("selectionCreated",function(e){"activeSelection"===e.type&&e.set(t)})}},{key:"_attachInvokerEvents",value:function(){var e=g.UNDO_STACK_CHANGED,t=g.REDO_STACK_CHANGED;this._invoker.on(e,this.fire.bind(this,e)),this._invoker.on(t,this.fire.bind(this,t))}},{key:"_attachGraphicsEvents",value:function(){var e;this._graphics.on((p(e={},w,this._handlers.mousedown),p(e,x,this._handlers.objectMoved),p(e,C,this._handlers.objectScaled),p(e,S,this._handlers.objectRotated),p(e,O,this._handlers.objectActivated),p(e,M,this._handlers.addText),p(e,j,this._handlers.addObject),p(e,T,this._handlers.textEditing),p(e,P,this._handlers.textChanged),p(e,I,this._handlers.iconCreateResize),p(e,A,this._handlers.iconCreateEnd),p(e,R,this._handlers.selectionCleared),p(e,D,this._handlers.selectionCreated),p(e,L,this._handlers.addObjectAfter),e))}},{key:"_attachDomEvents",value:function(){document.addEventListener("keydown",this._handlers.keydown)}},{key:"_detachDomEvents",value:function(){document.removeEventListener("keydown",this._handlers.keydown)}},{key:"_onKeyDown",value:function(e){var t=e.ctrlKey,n=e.keyCode,i=e.metaKey;(t||i)&&(n===b.C?this._graphics.resetTargetObjectForCopyPaste():n===b.V?(this._graphics.pasteObject(),this.clearRedoStack()):n===b.Z?this.undo().catch(function(){}):n===b.Y&&this.redo().catch(function(){}));var o=n===b.BACKSPACE||n===b.DEL;this._graphics.isReadyRemoveObject()&&o&&(e.preventDefault(),this.removeActiveObject())}},{key:"removeActiveObject",value:function(){var e=this._graphics.getActiveObjectIdForRemove();this.removeObject(e)}},{key:"_onMouseDown",value:function(e,t){this.fire(g.MOUSE_DOWN,e,t)}},{key:"_pushAddObjectCommand",value:function(e){var t=u.default.create(m.ADD_OBJECT,this._graphics,e);this._invoker.pushUndoStack(t)}},{key:"_onObjectActivated",value:function(e){this.fire(g.OBJECT_ACTIVATED,e)}},{key:"_onObjectMoved",value:function(e){this.fire(g.OBJECT_MOVED,e)}},{key:"_onObjectScaled",value:function(e){this.fire(g.OBJECT_SCALED,e)}},{key:"_onObjectRotated",value:function(e){this.fire(g.OBJECT_ROTATED,e)}},{key:"getDrawingMode",value:function(){return this._graphics.getDrawingMode()}},{key:"clearObjects",value:function(){return this.execute(m.CLEAR_OBJECTS)}},{key:"deactivateAll",value:function(){this._graphics.deactivateAll(),this._graphics.renderAll()}},{key:"discardSelection",value:function(){this._graphics.discardSelection()}},{key:"changeSelectableAll",value:function(e){this._graphics.changeSelectableAll(e)}},{key:"execute",value:function(e){for(var t,n=arguments.length,i=Array(1<n?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];var a=[this._graphics].concat(i);return(t=this._invoker).execute.apply(t,[e].concat(a))}},{key:"executeSilent",value:function(e){for(var t,n=arguments.length,i=Array(1<n?n-1:0),o=1;o<n;o++)i[o-1]=arguments[o];var a=[this._graphics].concat(i);return(t=this._invoker).executeSilent.apply(t,[e].concat(a))}},{key:"undo",value:function(){return this._invoker.undo()}},{key:"redo",value:function(){return this._invoker.redo()}},{key:"loadImageFromFile",value:function(t,e){if(!t)return r.default.reject(y.invalidParameters);var n=URL.createObjectURL(t);return e=e||t.name,this.loadImageFromURL(n,e).then(function(e){return URL.revokeObjectURL(t),e})}},{key:"loadImageFromURL",value:function(e,t){return t&&e?this.execute(m.LOAD_IMAGE,t,e):r.default.reject(y.invalidParameters)}},{key:"addImageObject",value:function(e){return e?this.execute(m.ADD_IMAGE_OBJECT,e):r.default.reject(y.invalidParameters)}},{key:"startDrawingMode",value:function(e,t){return this._graphics.startDrawingMode(e,t)}},{key:"stopDrawingMode",value:function(){this._graphics.stopDrawingMode()}},{key:"crop",value:function(e){var t=this._graphics.getCroppedImageData(e);return t?this.loadImageFromURL(t.url,t.imageName):r.default.reject(y.invalidParameters)}},{key:"getCropzoneRect",value:function(){return this._graphics.getCropzoneRect()}},{key:"setCropzoneRect",value:function(e){this._graphics.setCropzoneRect(e)}},{key:"_flip",value:function(e){return this.execute(m.FLIP_IMAGE,e)}},{key:"flipX",value:function(){return this._flip("flipX")}},{key:"flipY",value:function(){return this._flip("flipY")}},{key:"resetFlip",value:function(){return this._flip("reset")}},{key:"_rotate",value:function(e,t,n){return n?this.executeSilent(m.ROTATE_IMAGE,e,t):this.execute(m.ROTATE_IMAGE,e,t)}},{key:"rotate",value:function(e,t){return this._rotate("rotate",e,t)}},{key:"setAngle",value:function(e,t){return this._rotate("setAngle",e,t)}},{key:"setBrush",value:function(e){this._graphics.setBrush(e)}},{key:"setDrawingShape",value:function(e,t){this._graphics.setDrawingShape(e,t)}},{key:"addShape",value:function(e,t){return t=t||{},this._setPositions(t),this.execute(m.ADD_SHAPE,e,t)}},{key:"changeShape",value:function(e,t,n){return this[n?"executeSilent":"execute"](m.CHANGE_SHAPE,e,t)}},{key:"addText",value:function(e,t){return e=e||"",t=t||{},this.execute(m.ADD_TEXT,e,t)}},{key:"changeText",value:function(e,t){return t=t||"",this.execute(m.CHANGE_TEXT,e,t)}},{key:"changeTextStyle",value:function(e,t,n){return this[n?"executeSilent":"execute"](m.CHANGE_TEXT_STYLE,e,t)}},{key:"_changeActivateMode",value:function(e){"ICON"!==e&&this.getDrawingMode()!==e&&this.startDrawingMode(e)}},{key:"_onTextChanged",value:function(e){this.changeText(e.id,e.text)}},{key:"_onIconCreateResize",value:function(e){this.fire(g.ICON_CREATE_RESIZE,e)}},{key:"_onIconCreateEnd",value:function(e){this.fire(g.ICON_CREATE_END,e)}},{key:"_onTextEditing",value:function(){this.fire(g.TEXT_EDITING)}},{key:"_onAddText",value:function(e){this.fire(g.ADD_TEXT,{originPosition:e.originPosition,clientPosition:e.clientPosition})}},{key:"_onAddObject",value:function(e){var t=this._graphics.getObject(e.id);this._pushAddObjectCommand(t)}},{key:"_onAddObjectAfter",value:function(e){this.fire(g.ADD_OBJECT_AFTER,e)}},{key:"_selectionCleared",value:function(){this.fire(g.SELECTION_CLEARED)}},{key:"_selectionCreated",value:function(e){this.fire(g.SELECTION_CREATED,e)}},{key:"registerIcons",value:function(e){this._graphics.registerPaths(e)}},{key:"changeCursor",value:function(e){this._graphics.changeCursor(e)}},{key:"addIcon",value:function(e,t){return t=t||{},this._setPositions(t),this.execute(m.ADD_ICON,e,t)}},{key:"changeIconColor",value:function(e,t){return this.execute(m.CHANGE_ICON_COLOR,e,t)}},{key:"removeObject",value:function(e){return this.execute(m.REMOVE_OBJECT,e)}},{key:"hasFilter",value:function(e){return this._graphics.hasFilter(e)}},{key:"removeFilter",value:function(e){return this.execute(m.REMOVE_FILTER,e)}},{key:"applyFilter",value:function(e,t,n){return this[n?"executeSilent":"execute"](m.APPLY_FILTER,e,t)}},{key:"toDataURL",value:function(e){return this._graphics.toDataURL(e)}},{key:"getImageName",value:function(){return this._graphics.getImageName()}},{key:"clearUndoStack",value:function(){this._invoker.clearUndoStack()}},{key:"clearRedoStack",value:function(){this._invoker.clearRedoStack()}},{key:"isEmptyUndoStack",value:function(){return this._invoker.isEmptyUndoStack()}},{key:"isEmptyRedoStack",value:function(){return this._invoker.isEmptyRedoStack()}},{key:"resizeCanvasDimension",value:function(e){return e?this.execute(m.RESIZE_CANVAS_DIMENSION,e):r.default.reject(y.invalidParameters)}},{key:"destroy",value:function(){var n=this;this.stopDrawingMode(),this._detachDomEvents(),this._graphics.destroy(),this._graphics=null,this.ui&&this.ui.destroy(),k(this,function(e,t){n[t]=null},this)}},{key:"_setPositions",value:function(e){var t=this._graphics.getCenter();_(e.left)&&(e.left=t.left),_(e.top)&&(e.top=t.top)}},{key:"setObjectProperties",value:function(e,t){return this.execute(m.SET_OBJECT_PROPERTIES,e,t)}},{key:"setObjectPropertiesQuietly",value:function(e,t){this._graphics.setObjectProperties(e,t)}},{key:"getObjectProperties",value:function(e,t){return this._graphics.getObject(e)?this._graphics.getObjectProperties(e,t):null}},{key:"getCanvasSize",value:function(){return this._graphics.getCanvasSize()}},{key:"getObjectPosition",value:function(e,t,n){return this._graphics.getObjectPosition(e,t,n)}},{key:"setObjectPosition",value:function(e,t){return this.execute(m.SET_OBJECT_POSITION,e,t)}}]),H);function H(e,t){if(!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,H),t=a.default.extend({includeUI:!1,usageStatistics:!0},t),this.mode=null,this.activeObjectId=null,t.includeUI){var n=t.includeUI;n.usageStatistics=t.usageStatistics,this.ui=new l.default(e,n,this.getActions()),t=this.ui.setUiDefaultSelectionStyle(t)}this._invoker=new s.default,this._graphics=new d.default(this.ui?this.ui.getEditorArea():e,{cssMaxWidth:t.cssMaxWidth,cssMaxHeight:t.cssMaxHeight,useItext:!!this.ui,useDragAddIcon:!!this.ui}),this._handlers={keydown:this._onKeyDown.bind(this),mousedown:this._onMouseDown.bind(this),objectActivated:this._onObjectActivated.bind(this),objectMoved:this._onObjectMoved.bind(this),objectScaled:this._onObjectScaled.bind(this),objectRotated:this._onObjectRotated.bind(this),createdPath:this._onCreatedPath,addText:this._onAddText.bind(this),addObject:this._onAddObject.bind(this),addObjectAfter:this._onAddObjectAfter.bind(this),textEditing:this._onTextEditing.bind(this),textChanged:this._onTextChanged.bind(this),iconCreateResize:this._onIconCreateResize.bind(this),iconCreateEnd:this._onIconCreateEnd.bind(this),selectionCleared:this._selectionCleared.bind(this),selectionCreated:this._selectionCreated.bind(this)},this._attachInvokerEvents(),this._attachGraphicsEvents(),this._attachDomEvents(),this._setSelectionStyle(t.selectionStyle,{applyCropSelectionStyle:t.applyCropSelectionStyle,applyGroupSelectionStyle:t.applyGroupSelectionStyle}),t.usageStatistics&&(0,f.sendHostName)(),this.ui&&(this.ui.initCanvas(),this.setReAction()),fabric.enableGLFiltering=!1}c.default.mixin(z),E.mixin(z),e.exports=z},function(e,t){},function(e,t,n){"use strict";var i=n(48)(!0);n(30)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=i(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){var l=n(23),c=n(24);e.exports=function(s){return function(e,t){var n,i,o=String(c(e)),a=l(t),r=o.length;return a<0||r<=a?s?"":void 0:(n=o.charCodeAt(a))<55296||56319<n||a+1===r||(i=o.charCodeAt(a+1))<56320||57343<i?s?o.charAt(a):n:s?o.slice(a,a+2):i-56320+(n-55296<<10)+65536}}},function(e,t,n){e.exports=!n(13)&&!n(33)(function(){return 7!=Object.defineProperty(n(26)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){var o=n(20);e.exports=function(e,t){if(!o(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!o(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!o(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!o(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},function(e,t,n){e.exports=n(10)},function(e,t,n){"use strict";var i=n(53),o=n(34),a=n(29),r={};n(10)(r,n(6)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=i(r,{next:o(1,n)}),a(e,t+" Iterator")}},function(e,t,i){function o(){}var a=i(11),r=i(54),s=i(38),l=i(28)("IE_PROTO"),c="prototype",u=function(){var e,t=i(26)("iframe"),n=s.length;for(t.style.display="none",i(39).appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;n--;)delete u[c][s[n]];return u()};e.exports=Object.create||function(e,t){var n;return null!==e?(o[c]=a(e),n=new o,o[c]=null,n[l]=e):n=u(),void 0===t?n:r(n,t)}},function(e,t,n){var r=n(19),s=n(11),l=n(55);e.exports=n(13)?Object.defineProperties:function(e,t){s(e);for(var n,i=l(t),o=i.length,a=0;a<o;)r.f(e,n=i[a++],t[n]);return e}},function(e,t,n){var i=n(56),o=n(38);e.exports=Object.keys||function(e){return i(e,o)}},function(e,t,n){var r=n(21),s=n(27),l=n(58)(!1),c=n(28)("IE_PROTO");e.exports=function(e,t){var n,i=s(e),o=0,a=[];for(n in i)n!=c&&r(i,n)&&a.push(n);for(;t.length>o;)r(i,n=t[o++])&&(~l(a,n)||a.push(n));return a}},function(e,t,n){var i=n(22);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==i(e)?e.split(""):Object(e)}},function(e,t,n){var l=n(27),c=n(35),u=n(59);e.exports=function(s){return function(e,t,n){var i,o=l(e),a=c(o.length),r=u(n,a);if(s&&t!=t){for(;r<a;)if((i=o[r++])!=i)return!0}else for(;r<a;r++)if((s||r in o)&&o[r]===t)return s||r||0;return!s&&-1}}},function(e,t,n){var i=n(23),o=Math.max,a=Math.min;e.exports=function(e,t){return(e=i(e))<0?o(e+t,0):a(e,t)}},function(e,t,n){var i=n(21),o=n(61),a=n(28)("IE_PROTO"),r=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=o(e),i(e,a)?e[a]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?r:null}},function(e,t,n){var i=n(24);e.exports=function(e){return Object(i(e))}},function(e,t,n){n(63);for(var i=n(7),o=n(10),a=n(14),r=n(6)("toStringTag"),s=["NodeList","DOMTokenList","MediaList","StyleSheetList","CSSRuleList"],l=0;l<5;l++){var c=s[l],u=i[c],d=u&&u.prototype;d&&!d[r]&&o(d,r,c),a[c]=a.Array}},function(e,t,n){"use strict";var i=n(64),o=n(65),a=n(14),r=n(27);e.exports=n(30)(Array,"Array",function(e,t){this._t=r(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,o(1)):o(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])},"values"),a.Arguments=a.Array,i("keys"),i("values"),i("entries")},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){"use strict";function i(){}function o(e,t){return e===t||e===M&&t===h}function u(e){var t;return!(!b(e)||"function"!=typeof(t=e.then))&&t}function a(e){return new(o(M,e)?P:d)(e)}function c(e){try{e()}catch(e){return{error:e}}}function r(c,n){if(!c._n){c._n=!0;var i=c._c;x(function(){for(var s=c._v,l=1==c._s,e=0,t=function(e){var t,n,i=l?e.ok:e.fail,o=e.resolve,a=e.reject,r=e.domain;try{i?(l||(2==c._h&&R(c),c._h=1),!0===i?t=s:(r&&r.enter(),t=i(s),r&&r.exit()),t===e.promise?a(O("Promise-chain cycle")):(n=u(t))?n.call(t,o,a):o(t)):a(s)}catch(e){a(e)}};i.length>e;)t(i[e++]);c._c=[],c._n=!1,n&&!c._h&&I(c)})}}function s(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),r(t,!0))}var l,d,h,f=n(31),v=n(7),p=n(18),g=n(40),m=n(32),b=n(20),y=n(25),_=n(67),k=n(68),E=n(72),w=n(41).set,x=n(74)(),C="Promise",O=v.TypeError,S=v.process,M=v[C],j="process"==g(S=v.process),T=!!function(){try{var e=M.resolve(1),t=(e.constructor={})[n(6)("species")]=function(e){e(i,i)};return(j||"function"==typeof PromiseRejectionEvent)&&e.then(i)instanceof t}catch(e){}}(),P=d=function(e){var n,i;this.promise=new e(function(e,t){if(void 0!==n||void 0!==i)throw O("Bad Promise constructor");n=e,i=t}),this.resolve=y(n),this.reject=y(i)},I=function(o){w.call(v,function(){var e,t,n,i=o._v;if(A(o)&&(e=c(function(){j?S.emit("unhandledRejection",i,o):(t=v.onunhandledrejection)?t({promise:o,reason:i}):(n=v.console)&&n.error&&n.error("Unhandled promise rejection",i)}),o._h=j||A(o)?2:1),o._a=void 0,e)throw e.error})},A=function(e){if(1==e._h)return!1;for(var t,n=e._a||e._c,i=0;n.length>i;)if((t=n[i++]).fail||!A(t.promise))return!1;return!0},R=function(t){w.call(v,function(){var e;j?S.emit("rejectionHandled",t):(e=v.onrejectionhandled)&&e({promise:t,reason:t._v})})},D=function(e){var n,i=this;if(!i._d){i._d=!0,i=i._w||i;try{if(i===e)throw O("Promise can't be resolved itself");(n=u(e))?x(function(){var t={_w:i,_d:!1};try{n.call(e,p(D,t,1),p(s,t,1))}catch(e){s.call(t,e)}}):(i._v=e,i._s=1,r(i,!1))}catch(e){s.call({_w:i,_d:!1},e)}}};T||(M=function(e){_(this,M,C,"_h"),y(e),l.call(this);try{e(p(D,this,1),p(s,this,1))}catch(e){s.call(this,e)}},(l=function(){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=n(75)(M.prototype,{then:function(e,t){var n=a(E(this,M));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=j?S.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&r(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),P=function(){var e=new l;this.promise=e,this.resolve=p(D,e,1),this.reject=p(s,e,1)}),m(m.G+m.W+m.F*!T,{Promise:M}),n(29)(M,C),n(76)(C),h=n(12)[C],m(m.S+m.F*!T,C,{reject:function(e){var t=a(this);return(0,t.reject)(e),t.promise}}),m(m.S+m.F*(f||!T),C,{resolve:function(e){if(e instanceof M&&o(e.constructor,this))return e;var t=a(this);return(0,t.resolve)(e),t.promise}}),m(m.S+m.F*!(T&&n(77)(function(e){M.all(e).catch(i)})),C,{all:function(e){var r=this,t=a(r),s=t.resolve,l=t.reject,n=c(function(){var i=[],o=0,a=1;k(e,!1,function(e){var t=o++,n=!1;i.push(void 0),a++,r.resolve(e).then(function(e){n||(n=!0,i[t]=e,--a||s(i))},l)}),--a||s(i)});return n&&l(n.error),t.promise},race:function(e){var t=this,n=a(t),i=n.reject,o=c(function(){k(e,!1,function(e){t.resolve(e).then(n.resolve,i)})});return o&&i(o.error),n.promise}})},function(e,t){e.exports=function(e,t,n,i){if(!(e instanceof t)||void 0!==i&&i in e)throw TypeError(n+": incorrect invocation!");return e}},function(e,t,n){var h=n(18),f=n(69),v=n(70),p=n(11),g=n(35),m=n(71),b={},y={};(t=e.exports=function(e,t,n,i,o){var a,r,s,l,c=o?function(){return e}:m(e),u=h(n,i,t?2:1),d=0;if("function"!=typeof c)throw TypeError(e+" is not iterable!");if(v(c)){for(a=g(e.length);d<a;d++)if((l=t?u(p(r=e[d])[0],r[1]):u(e[d]))===b||l===y)return l}else for(s=c.call(e);!(r=s.next()).done;)if((l=f(s,u,r.value,t))===b||l===y)return l}).BREAK=b,t.RETURN=y},function(e,t,n){var a=n(11);e.exports=function(t,e,n,i){try{return i?e(a(n)[0],n[1]):e(n)}catch(e){var o=t.return;throw void 0!==o&&a(o.call(t)),e}}},function(e,t,n){var i=n(14),o=n(6)("iterator"),a=Array.prototype;e.exports=function(e){return void 0!==e&&(i.Array===e||a[o]===e)}},function(e,t,n){var i=n(40),o=n(6)("iterator"),a=n(14);e.exports=n(12).getIteratorMethod=function(e){if(null!=e)return e[o]||e["@@iterator"]||a[i(e)]}},function(e,t,n){var o=n(11),a=n(25),r=n(6)("species");e.exports=function(e,t){var n,i=o(e).constructor;return void 0===i||null==(n=o(i)[r])?t:a(n)}},function(e,t){e.exports=function(e,t,n){var i=void 0===n;switch(t.length){case 0:return i?e():e.call(n);case 1:return i?e(t[0]):e.call(n,t[0]);case 2:return i?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return i?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return i?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){var s=n(7),l=n(41).set,c=s.MutationObserver||s.WebKitMutationObserver,u=s.process,d=s.Promise,h="process"==n(22)(u);e.exports=function(){function e(){var e,t;for(h&&(e=u.domain)&&e.exit();n;){t=n.fn,n=n.next;try{t()}catch(e){throw n?o():i=void 0,e}}i=void 0,e&&e.enter()}var n,i,o;if(h)o=function(){u.nextTick(e)};else if(c){var t=!0,a=document.createTextNode("");new c(e).observe(a,{characterData:!0}),o=function(){a.data=t=!t}}else if(d&&d.resolve){var r=d.resolve();o=function(){r.then(e)}}else o=function(){l.call(s,e)};return function(e){var t={fn:e,next:void 0};i&&(i.next=t),n||(n=t,o()),i=t}}},function(e,t,n){var o=n(10);e.exports=function(e,t,n){for(var i in t)n&&e[i]?e[i]=t[i]:o(e,i,t[i]);return e}},function(e,t,n){"use strict";var i=n(7),o=n(12),a=n(19),r=n(13),s=n(6)("species");e.exports=function(e){var t="function"==typeof o[e]?o[e]:i[e];r&&t&&!t[s]&&a.f(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,n){var a=n(6)("iterator"),r=!1;try{var i=[7][a]();i.return=function(){r=!0},Array.from(i,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!r)return!1;var n=!1;try{var i=[7],o=i[a]();o.next=function(){return{done:n=!0}},i[a]=function(){return o},e(i)}catch(e){}return n}},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=c(n(1)),r=c(n(2)),s=c(n(3)),l=c(n(0));function c(e){return e&&e.__esModule?e:{default:e}}var u=l.default.eventNames,d=l.default.rejectMessages,h=a.default.isFunction,f=a.default.isString,v=a.default.CustomEvents,p=(i(g,[{key:"_invokeExecution",value:function(t){var n=this;this.lock();var e=t.args;return e=e||[],t.execute.apply(t,e).then(function(e){return n._isSilent||n.pushUndoStack(t),n.unlock(),h(t.executeCallback)&&t.executeCallback(e),e}).catch(function(e){return n.unlock(),r.default.reject(e)})}},{key:"_invokeUndo",value:function(t){var n=this;this.lock();var e=t.args;return e=e||[],t.undo.apply(t,e).then(function(e){return n.pushRedoStack(t),n.unlock(),h(t.undoCallback)&&t.undoCallback(e),e}).catch(function(e){return n.unlock(),r.default.reject(e)})}},{key:"_fireRedoStackChanged",value:function(){this.fire(u.REDO_STACK_CHANGED,this._redoStack.length)}},{key:"_fireUndoStackChanged",value:function(){this.fire(u.UNDO_STACK_CHANGED,this._undoStack.length)}},{key:"lock",value:function(){this._isLocked=!0}},{key:"unlock",value:function(){this._isLocked=!1}},{key:"executeSilent",value:function(){var e=this;this._isSilent=!0;for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return this.execute.apply(this,n.concat([this._isSilent])).then(function(){e._isSilent=!1})}},{key:"execute",value:function(){var t=this;if(this._isLocked)return r.default.reject(d.isLock);for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];var o=n[0];return f(o)&&(o=s.default.create.apply(s.default,n)),this._invokeExecution(o).then(function(e){return t.clearRedoStack(),e})}},{key:"undo",value:function(){var e=this._undoStack.pop(),t="";return e&&this._isLocked&&(this.pushUndoStack(e,!0),e=null),e?(this.isEmptyUndoStack()&&this._fireUndoStackChanged(),this._invokeUndo(e)):(t=d.undo,this._isLocked&&(t=t+" Because "+d.isLock),r.default.reject(t))}},{key:"redo",value:function(){var e=this._redoStack.pop(),t="";return e&&this._isLocked&&(this.pushRedoStack(e,!0),e=null),e?(this.isEmptyRedoStack()&&this._fireRedoStackChanged(),this._invokeExecution(e)):(t=d.redo,this._isLocked&&(t=t+" Because "+d.isLock),r.default.reject(t))}},{key:"pushUndoStack",value:function(e,t){this._undoStack.push(e),t||this._fireUndoStackChanged()}},{key:"pushRedoStack",value:function(e,t){this._redoStack.push(e),t||this._fireRedoStackChanged()}},{key:"isEmptyRedoStack",value:function(){return 0===this._redoStack.length}},{key:"isEmptyUndoStack",value:function(){return 0===this._undoStack.length}},{key:"clearUndoStack",value:function(){this.isEmptyUndoStack()||(this._undoStack=[],this._fireUndoStackChanged())}},{key:"clearRedoStack",value:function(){this.isEmptyRedoStack()||(this._redoStack=[],this._fireRedoStackChanged())}}]),g);function g(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,g),this._undoStack=[],this._redoStack=[],this._isLocked=!1,this._isSilent=!1}v.mixin(p),e.exports=p},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(1)),r=s(n(42));function s(e){return e&&e.__esModule?e:{default:e}}var l=r.default.create,c=r.default.types,u=(i(d,[{key:"execute",value:function(){throw new Error(l(c.UN_IMPLEMENTATION,"execute"))}},{key:"undo",value:function(){throw new Error(l(c.UN_IMPLEMENTATION,"undo"))}},{key:"setUndoData",value:function(e,t,n){return t&&(e=t),t=n?t||e:(a.default.extend(this.undoData,e),null)}},{key:"setExecuteCallback",value:function(e){return this.executeCallback=e,this}},{key:"setUndoCallback",value:function(e){return this.undoCallback=e,this}},{key:"isRedo",get:function(){return Object.keys(this.undoData).length}}]),d);function d(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),this.name=e.name,this.args=t,this.execute=e.execute,this.undo=e.undo,this.executeCallback=e.executeCallback||null,this.undoCallback=e.undoCallback||null,this.undoData={}}e.exports=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=k(n(1)),r=n(0),s=k(n(4)),l=k(n(81)),c=k(n(82)),u=k(n(83)),d=k(n(87)),h=k(n(90)),f=k(n(92)),v=k(n(94)),p=k(n(96)),g=k(n(98)),m=k(n(100)),b=k(n(102)),y=k(n(104)),_=k(n(106));function k(e){return e&&e.__esModule?e:{default:e}}var E={Shape:d.default,Crop:h.default,Flip:f.default,Rotate:v.default,Text:p.default,Mask:g.default,Icon:m.default,Draw:b.default,Filter:y.default},w=(i(x,[{key:"destroy",value:function(){this._removeUiEvent(),this._destroyAllMenu(),this._selectedElement.innerHTML="",s.default.assignmentForDestroy(this)}},{key:"setUiDefaultSelectionStyle",value:function(e){return a.default.extend({applyCropSelectionStyle:!0,applyGroupSelectionStyle:!0,selectionStyle:{cornerStyle:"circle",cornerSize:16,cornerColor:"#fff",cornerStrokeColor:"#fff",transparentCorners:!1,lineWidth:2,borderColor:"#fff"}},e)}},{key:"resizeEditor",value:function(e){var t=0<arguments.length&&void 0!==e?e:{},n=t.uiSize,i=t.imageSize,o=void 0===i?this.imageSize:i;o!==this.imageSize&&(this.imageSize=o),n&&this._setUiSize(n);var a=this._getCanvasMaxDimension(),r=a.width,s=a.height,l=this._editorElement.style,c=this.options.menuBarPosition;l.height=s+"px",l.width=r+"px",this._setEditorPosition(c),this._editorElementWrap.style.bottom="0px",this._editorElementWrap.style.top="0px",this._editorElementWrap.style.left="0px",this._editorElementWrap.style.width="100%";var u=this._selectedElement.classList;"top"===c&&this._selectedElement.offsetWidth<"1300"?u.add("tui-image-editor-top-optimization"):u.remove("tui-image-editor-top-optimization")}},{key:"changeHelpButtonEnabled",value:function(e,t){this._buttonElements[e].classList[t?"add":"remove"]("enabled")}},{key:"_initializeOption",value:function(e){return a.default.extend({loadImage:{path:"",name:""},locale:{},menuIconPath:"",menu:["crop","flip","rotate","draw","shape","icon","text","mask","filter"],initMenu:"",uiSize:{width:"100%",height:"100%"},menuBarPosition:"bottom"},e)}},{key:"_setUiSize",value:function(e){var t=0<arguments.length&&void 0!==e?e:this.options.uiSize,n=this._selectedElement.style;n.width=t.width,n.height=t.height}},{key:"_makeSubMenu",value:function(){var n=this;a.default.forEach(this.options.menu,function(e){var t=E[e.replace(/^[a-z]/,function(e){return e.toUpperCase()})];n._makeMenuElement(e),n._buttonElements[e]=n._menuElement.querySelector(".tie-btn-"+e),n[e]=new t(n._subMenuElement,{locale:n._locale,makeSvgIcon:n.theme.makeMenSvgIconSet.bind(n.theme),menuBarPosition:n.options.menuBarPosition,usageStatistics:n.options.usageStatistics})})}},{key:"_makeUiElement",value:function(e){var t=void 0;window.snippet=a.default,t=e.nodeType?e:document.querySelector(e);var n=s.default.getSelector(t);t.classList.add("tui-image-editor-container"),t.innerHTML=(0,c.default)({locale:this._locale,biImage:this.theme.getStyle("common.bi"),loadButtonStyle:this.theme.getStyle("loadButton"),downloadButtonStyle:this.theme.getStyle("downloadButton")})+(0,l.default)({locale:this._locale,biImage:this.theme.getStyle("common.bi"),commonStyle:this.theme.getStyle("common"),headerStyle:this.theme.getStyle("header"),loadButtonStyle:this.theme.getStyle("loadButton"),downloadButtonStyle:this.theme.getStyle("downloadButton"),submenuStyle:this.theme.getStyle("submenu")}),this._selectedElement=t,this._selectedElement.classList.add(this.options.menuBarPosition),this._mainElement=n(".tui-image-editor-main"),this._editorElementWrap=n(".tui-image-editor-wrap"),this._editorElement=n(".tui-image-editor"),this._menuElement=n(".tui-image-editor-menu"),this._subMenuElement=n(".tui-image-editor-submenu"),this._buttonElements={download:this._selectedElement.querySelectorAll(".tui-image-editor-download-btn"),load:this._selectedElement.querySelectorAll(".tui-image-editor-load-btn")},this._addHelpMenus()}},{key:"_makeHelpMenuWithPartition",value:function(){var e=[].concat(r.HELP_MENUS,[""]);return e.splice(3,0,""),e}},{key:"_addHelpMenus",value:function(){var t=this,e=this._makeHelpMenuWithPartition();a.default.forEach(e,function(e){e?(t._makeMenuElement(e,["normal","disabled","hover"],"help"),e&&(t._buttonElements[e]=t._menuElement.querySelector(".tie-btn-"+e))):t._makeMenuPartitionElement()})}},{key:"_makeMenuPartitionElement",value:function(){var e=document.createElement("li"),t=document.createElement("div");e.className=s.default.cls("item"),t.className=s.default.cls("icpartition"),e.appendChild(t),this._menuElement.appendChild(e)}},{key:"_makeMenuElement",value:function(e,t,n){var i=1<arguments.length&&void 0!==t?t:["normal","active","hover"],o=2<arguments.length&&void 0!==n?n:"normal",a=document.createElement("li"),r=this.theme.makeMenSvgIconSet(i,e);this._addTooltipAttribute(a,e),a.className="tie-btn-"+e+" "+s.default.cls("item")+" "+o,a.innerHTML=r,this._menuElement.appendChild(a)}},{key:"_addHelpActionEvent",value:function(){var t=this;a.default.forEach(r.HELP_MENUS,function(e){t.eventHandler[e]=function(){return t._actions.main[e]()},t._buttonElements[e].addEventListener("click",t.eventHandler[e])})}},{key:"_removeHelpActionEvent",value:function(){var t=this;a.default.forEach(r.HELP_MENUS,function(e){t._buttonElements[e].removeEventListener("click",t.eventHandler[e])})}},{key:"_addTooltipAttribute",value:function(e,t){e.setAttribute("tooltip-content",this._locale.localize(t.replace(/^[a-z]/g,function(e){return e.toUpperCase()})))}},{key:"_addDownloadEvent",value:function(){var t=this;this.eventHandler.download=function(){return t._actions.main.download()},a.default.forEach(this._buttonElements.download,function(e){e.addEventListener("click",t.eventHandler.download)})}},{key:"_removeDownloadEvent",value:function(){var t=this;a.default.forEach(this._buttonElements.download,function(e){e.removeEventListener("click",t.eventHandler.download)})}},{key:"_addLoadEvent",value:function(){var t=this;this.eventHandler.loadImage=function(e){return t._actions.main.load(e.target.files[0])},a.default.forEach(this._buttonElements.load,function(e){e.addEventListener("change",t.eventHandler.loadImage)})}},{key:"_removeLoadEvent",value:function(){var t=this;a.default.forEach(this._buttonElements.load,function(e){e.removeEventListener("change",t.eventHandler.loadImage)})}},{key:"_addMainMenuEvent",value:function(e){var t=this;this.eventHandler[e]=function(){return t.changeMenu(e)},this._buttonElements[e].addEventListener("click",this.eventHandler[e])}},{key:"_addSubMenuEvent",value:function(e){this[e].addEvent(this._actions[e])}},{key:"_addMenuEvent",value:function(){var t=this;a.default.forEach(this.options.menu,function(e){t._addMainMenuEvent(e),t._addSubMenuEvent(e)})}},{key:"_removeMainMenuEvent",value:function(){var t=this;a.default.forEach(this.options.menu,function(e){t._buttonElements[e].removeEventListener("click",t.eventHandler[e])})}},{key:"getEditorArea",value:function(){return this._editorElement}},{key:"activeMenuEvent",value:function(){this._initMenuEvent||(this._addHelpActionEvent(),this._addDownloadEvent(),this._addMenuEvent(),this._initMenu(),this._initMenuEvent=!0)}},{key:"_removeUiEvent",value:function(){this._removeHelpActionEvent(),this._removeDownloadEvent(),this._removeLoadEvent(),this._removeMainMenuEvent()}},{key:"_destroyAllMenu",value:function(){var t=this;a.default.forEach(this.options.menu,function(e){t[e].destroy()})}},{key:"initCanvas",value:function(){var e=this,t=this._getLoadImage();t.path&&this._actions.main.initLoadImage(t.path,t.name).then(function(){e.activeMenuEvent()}),this._addLoadEvent();var n=document.createElement("div");n.className=s.default.cls("grid-visual"),n.innerHTML='<table>\n           <tr><td class="dot left-top"></td><td></td><td class="dot right-top"></td></tr>\n           <tr><td></td><td></td><td></td></tr>\n           <tr><td class="dot left-bottom"></td><td></td><td class="dot right-bottom"></td></tr>\n         </table>',this._editorContainerElement=this._editorElement.querySelector(".tui-image-editor-canvas-container"),this._editorContainerElement.appendChild(n)}},{key:"_getLoadImage",value:function(){return this.options.loadImage}},{key:"changeMenu",value:function(e,t,n){var i=!(1<arguments.length&&void 0!==t)||t,o=!(2<arguments.length&&void 0!==n)||n;this._submenuChangeTransection||(this._submenuChangeTransection=!0,this._changeMenu(e,i,o),this._submenuChangeTransection=!1)}},{key:"_changeMenu",value:function(e,t,n){this.submenu&&(this._buttonElements[this.submenu].classList.remove("active"),this._mainElement.classList.remove("tui-image-editor-menu-"+this.submenu),n&&this._actions.main.discardSelection(),this._actions.main.changeSelectableAll(!0),this[this.submenu].changeStandbyMode()),this.submenu===e&&t?this.submenu=null:(this._buttonElements[e].classList.add("active"),this._mainElement.classList.add("tui-image-editor-menu-"+e),this.submenu=e,this[this.submenu].changeStartMode()),this.resizeEditor()}},{key:"_initMenu",value:function(){if(this.options.initMenu){var e=document.createEvent("MouseEvents");e.initEvent("click",!0,!1),this._buttonElements[this.options.initMenu].dispatchEvent(e)}this.icon&&this.icon.registDefaultIcon()}},{key:"_getCanvasMaxDimension",value:function(){var e=this._editorContainerElement.style,t=e.maxWidth,n=e.maxHeight;return{width:parseFloat(t),height:parseFloat(n)}}},{key:"_setEditorPosition",value:function(e){var t=this._getCanvasMaxDimension(),n=t.width,i=t.height,o=this._editorElement.style,a=0,r=0;this.submenu&&("bottom"===e?a=i>this._editorElementWrap.scrollHeight-150?(i-this._editorElementWrap.scrollHeight)/2:-75:"top"===e?a=i>this._editorElementWrap.offsetHeight-150?75-(i-(this._editorElementWrap.offsetHeight-150))/2:75:"left"===e?r=n>this._editorElementWrap.offsetWidth-248?124-(n-(this._editorElementWrap.offsetWidth-248))/2:124:"right"===e&&(r=n>this._editorElementWrap.scrollWidth-248?(n-this._editorElementWrap.scrollWidth)/2:-124)),o.top=a+"px",o.left=r+"px"}}]),x);function x(e,t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,x),this.options=this._initializeOption(t),this._actions=n,this.submenu=!1,this.imageSize={},this.uiSize={},this._locale=new _.default(this.options.locale),this.theme=new u.default(this.options.theme),this.eventHandler={},this._submenuChangeTransection=!1,this._selectedElement=null,this._mainElement=null,this._editorElementWrap=null,this._editorElement=null,this._menuElement=null,this._subMenuElement=null,this._makeUiElement(e),this._setUiSize(),this._initMenuEvent=!1,this._makeSubMenu()}t.default=w},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.biImage,i=e.commonStyle,o=e.headerStyle,a=e.loadButtonStyle,r=e.downloadButtonStyle,s=e.submenuStyle;return'\n    <div class="tui-image-editor-main-container" style="'+i+'">\n        <div class="tui-image-editor-header" style="'+o+'">\n            <div class="tui-image-editor-header-logo">\n                <img src="'+n+'" />\n            </div>\n            <div class="tui-image-editor-header-buttons">\n                <div style="'+a+'">\n                    '+t.localize("Load")+'\n                    <input type="file" class="tui-image-editor-load-btn" />\n                </div>\n                <button class="tui-image-editor-download-btn" style="'+r+'">\n                    '+t.localize("Download")+'\n                </button>\n            </div>\n        </div>\n        <div class="tui-image-editor-main">\n            <div class="tui-image-editor-submenu">\n                <div class="tui-image-editor-submenu-style" style="'+s+'"></div>\n            </div>\n            <div class="tui-image-editor-wrap">\n                <div class="tui-image-editor-size-wrap">\n                    <div class="tui-image-editor-align-wrap">\n                        <div class="tui-image-editor"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n'}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.biImage,i=e.loadButtonStyle,o=e.downloadButtonStyle;return'\n    <div class="tui-image-editor-controls">\n        <div class="tui-image-editor-controls-logo">\n            <img src="'+n+'" />\n        </div>\n        <ul class="tui-image-editor-menu"></ul>\n\n        <div class="tui-image-editor-controls-buttons">\n            <div style="'+i+'">\n                '+t.localize("Load")+'\n                <input type="file" class="tui-image-editor-load-btn" />\n            </div>\n            <button class="tui-image-editor-download-btn" style="'+o+'">\n                '+t.localize("Download")+"\n            </button>\n        </div>\n    </div>\n"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var r=n(1),a=n(4),s=u(n(84)),l=u(n(85)),c=u(n(86));function u(e){return e&&e.__esModule?e:{default:e}}var d=(i(h,[{key:"getStyle",value:function(e){var t=null,n=e.replace(/\..+$/,""),i=this.styles[e];switch(e){case"common.bi":t=this.styles[e].image;break;case"menu.icon":t={active:this.styles[n+".activeIcon"],normal:this.styles[n+".normalIcon"],hover:this.styles[n+".hoverIcon"],disabled:this.styles[n+".disabledIcon"]};break;case"submenu.icon":t={active:this.styles[n+".activeIcon"],normal:this.styles[n+".normalIcon"]};break;case"submenu.label":t={active:this._makeCssText(this.styles[n+".activeLabel"]),normal:this._makeCssText(this.styles[n+".normalLabel"])};break;case"submenu.partition":t={vertical:this._makeCssText((0,r.extend)({},i,{borderLeft:"1px solid "+i.color})),horizontal:this._makeCssText((0,r.extend)({},i,{borderBottom:"1px solid "+i.color}))};break;case"range.disabledPointer":case"range.disabledBar":case"range.disabledSubbar":case"range.pointer":case"range.bar":case"range.subbar":i.backgroundColor=i.color,t=this._makeCssText(i);break;default:t=this._makeCssText(i)}return t}},{key:"_styleMaker",value:function(){var e=this.getStyle("submenu.label"),t=this.getStyle("submenu.partition");return(0,s.default)({subMenuLabelActive:e.active,subMenuLabelNormal:e.normal,submenuPartitionVertical:t.vertical,submenuPartitionHorizontal:t.horizontal,biSize:this.getStyle("common.bisize"),subMenuRangeTitle:this.getStyle("range.title"),submenuRangePointer:this.getStyle("range.pointer"),submenuRangeBar:this.getStyle("range.bar"),submenuRangeSubbar:this.getStyle("range.subbar"),submenuDisabledRangePointer:this.getStyle("range.disabledPointer"),submenuDisabledRangeBar:this.getStyle("range.disabledBar"),submenuDisabledRangeSubbar:this.getStyle("range.disabledSubbar"),submenuRangeValue:this.getStyle("range.value"),submenuColorpickerTitle:this.getStyle("colorpicker.title"),submenuColorpickerButton:this.getStyle("colorpicker.button"),submenuCheckbox:this.getStyle("checkbox"),menuIconSize:this.getStyle("menu.iconSize"),submenuIconSize:this.getStyle("submenu.iconSize"),menuIconStyle:this.getStyle("menu.icon"),submenuIconStyle:this.getStyle("submenu.icon")})}},{key:"_changeToObject",value:function(e){var a={};return(0,r.forEach)(e,function(e,t){var n=t.match(/^(.+)\.([a-z]+)$/i),i=n[1],o=n[2];a[i]||(a[i]={}),a[i][o]=e}),a}},{key:"_makeCssText",value:function(e){var n=this,i=[];return(0,r.forEach)(e,function(e,t){-1<["backgroundImage"].indexOf(t)&&"none"!==e&&(e="url("+e+")"),i.push(n._toUnderScore(t)+": "+e)}),i.join(";")}},{key:"_toUnderScore",value:function(e){return e.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()})}},{key:"_loadDefaultSvgIcon",value:function(){if(!document.getElementById("tui-image-editor-svg-default-icons")){var e=(new DOMParser).parseFromString(c.default,"text/xml");document.body.appendChild(e.documentElement)}}},{key:"_makeIconClassName",value:function(e,t){var n=(t?this.getStyle("submenu.icon"):this.getStyle("menu.icon"))[e],i=n.path,o=n.name;return i&&o?e:e+" use-default"}},{key:"_makeSvgIconPrefix",value:function(e,t){var n=(t?this.getStyle("submenu.icon"):this.getStyle("menu.icon"))[e],i=n.path,o=n.name;return i&&o?i+"#"+o+"-":"#"}},{key:"_makeSvgItem",value:function(e,t,n){var i=this;return(0,r.map)(e,function(e){return'<use xlink:href="'+i._makeSvgIconPrefix(e,n)+"ic-"+i._toUnderScore(t)+'" class="'+i._makeIconClassName(e,n)+'"/>'}).join("")}},{key:"makeMenSvgIconSet",value:function(e,t,n){var i=2<arguments.length&&void 0!==n&&n;return'<svg class="svg_ic-'+(i?"submenu":"menu")+'">'+this._makeSvgItem(e,t,i)+"</svg>"}}]),h);function h(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h),this.styles=this._changeToObject((0,r.extend)({},l.default,e)),(0,a.styleLoad)(this._styleMaker()),this._loadDefaultSvgIcon()}t.default=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.subMenuLabelActive,n=e.subMenuLabelNormal,i=e.subMenuRangeTitle,o=e.submenuPartitionVertical,a=e.submenuPartitionHorizontal,r=e.submenuCheckbox,s=e.submenuRangePointer,l=e.submenuRangeValue,c=e.submenuColorpickerTitle,u=e.submenuColorpickerButton,d=e.submenuRangeBar,h=e.submenuRangeSubbar,f=e.submenuDisabledRangePointer,v=e.submenuDisabledRangeBar,p=e.submenuDisabledRangeSubbar,g=e.submenuIconSize,m=e.menuIconSize,b=e.biSize,y=e.menuIconStyle,_=e.submenuIconStyle;return'\n    .tie-icon-add-button.icon-bubble .tui-image-editor-button[data-icontype="icon-bubble"] label,\n    .tie-icon-add-button.icon-heart .tui-image-editor-button[data-icontype="icon-heart"] label,\n    .tie-icon-add-button.icon-location .tui-image-editor-button[data-icontype="icon-location"] label,\n    .tie-icon-add-button.icon-polygon .tui-image-editor-button[data-icontype="icon-polygon"] label,\n    .tie-icon-add-button.icon-star .tui-image-editor-button[data-icontype="icon-star"] label,\n    .tie-icon-add-button.icon-star-2 .tui-image-editor-button[data-icontype="icon-star-2"] label,\n    .tie-icon-add-button.icon-arrow-3 .tui-image-editor-button[data-icontype="icon-arrow-3"] label,\n    .tie-icon-add-button.icon-arrow-2 .tui-image-editor-button[data-icontype="icon-arrow-2"] label,\n    .tie-icon-add-button.icon-arrow .tui-image-editor-button[data-icontype="icon-arrow"] label,\n    .tie-icon-add-button.icon-bubble .tui-image-editor-button[data-icontype="icon-bubble"] label,\n    .tie-draw-line-select-button.line .tui-image-editor-button.line label,\n    .tie-draw-line-select-button.free .tui-image-editor-button.free label,\n    .tie-flip-button.flipX .tui-image-editor-button.flipX label,\n    .tie-flip-button.flipY .tui-image-editor-button.flipY label,\n    .tie-flip-button.resetFlip .tui-image-editor-button.resetFlip label,\n    .tie-crop-button .tui-image-editor-button.apply.active label,\n    .tie-crop-preset-button .tui-image-editor-button.preset.active label,\n    .tie-shape-button.rect .tui-image-editor-button.rect label,\n    .tie-shape-button.circle .tui-image-editor-button.circle label,\n    .tie-shape-button.triangle .tui-image-editor-button.triangle label,\n    .tie-text-effect-button .tui-image-editor-button.active label,\n    .tie-text-align-button.left .tui-image-editor-button.left label,\n    .tie-text-align-button.center .tui-image-editor-button.center label,\n    .tie-text-align-button.right .tui-image-editor-button.right label,\n    .tie-mask-apply.apply.active .tui-image-editor-button.apply label,\n    .tui-image-editor-container .tui-image-editor-submenu .tui-image-editor-button:hover > label,\n    .tui-image-editor-container .tui-image-editor-checkbox label > span {\n        '+t+"\n    }\n    .tui-image-editor-container .tui-image-editor-submenu .tui-image-editor-button > label,\n    .tui-image-editor-container .tui-image-editor-range-wrap.tui-image-editor-newline.short label,\n    .tui-image-editor-container .tui-image-editor-range-wrap.tui-image-editor-newline.short label > span {\n        "+n+"\n    }\n    .tui-image-editor-container .tui-image-editor-range-wrap label > span {\n        "+i+"\n    }\n    .tui-image-editor-container .tui-image-editor-partition > div {\n        "+o+"\n    }\n    .tui-image-editor-container.left .tui-image-editor-submenu .tui-image-editor-partition > div,\n    .tui-image-editor-container.right .tui-image-editor-submenu .tui-image-editor-partition > div {\n        "+a+"\n    }\n    .tui-image-editor-container .tui-image-editor-checkbox label > span:before {\n        "+r+"\n    }\n    .tui-image-editor-container .tui-image-editor-checkbox label > input:checked + span:before {\n        border: 0;\n    }\n    .tui-image-editor-container .tui-image-editor-virtual-range-pointer {\n        "+s+"\n    }\n    .tui-image-editor-container .tui-image-editor-virtual-range-bar {\n        "+d+"\n    }\n    .tui-image-editor-container .tui-image-editor-virtual-range-subbar {\n        "+h+"\n    }\n    .tui-image-editor-container .tui-image-editor-disabled .tui-image-editor-virtual-range-pointer {\n        "+f+"\n    }\n    .tui-image-editor-container .tui-image-editor-disabled .tui-image-editor-virtual-range-subbar {\n        "+p+"\n    }\n    .tui-image-editor-container .tui-image-editor-disabled .tui-image-editor-virtual-range-bar {\n        "+v+"\n    }\n    .tui-image-editor-container .tui-image-editor-range-value {\n        "+l+"\n    }\n    .tui-image-editor-container .tui-image-editor-submenu .tui-image-editor-button .color-picker-value + label {\n        "+c+"\n    }\n    .tui-image-editor-container .tui-image-editor-submenu .tui-image-editor-button .color-picker-value {\n        "+u+"\n    }\n    .tui-image-editor-container .svg_ic-menu {\n        "+m+"\n    }\n    .tui-image-editor-container .svg_ic-submenu {\n        "+g+"\n    }\n    .tui-image-editor-container .tui-image-editor-controls-logo > img,\n    .tui-image-editor-container .tui-image-editor-header-logo > img {\n        "+b+"\n    }\n    .tui-image-editor-menu use.normal.use-default {\n        fill-rule: evenodd;\n        fill: "+y.normal.color+";\n        stroke: "+y.normal.color+";\n    }\n    .tui-image-editor-menu use.active.use-default {\n        fill-rule: evenodd;\n        fill: "+y.active.color+";\n        stroke: "+y.active.color+";\n    }\n    .tui-image-editor-menu use.hover.use-default {\n        fill-rule: evenodd;\n        fill: "+y.hover.color+";\n        stroke: "+y.hover.color+";\n    }\n    .tui-image-editor-menu use.disabled.use-default {\n        fill-rule: evenodd;\n        fill: "+y.disabled.color+";\n        stroke: "+y.disabled.color+";\n    }\n    .tui-image-editor-submenu use.normal.use-default {\n        fill-rule: evenodd;\n        fill: "+_.normal.color+";\n        stroke: "+_.normal.color+";\n    }\n    .tui-image-editor-submenu use.active.use-default {\n        fill-rule: evenodd;\n        fill: "+_.active.color+";\n        stroke: "+_.active.color+";\n    }\n"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={"common.bi.image":"https://uicdn.toast.com/toastui/img/tui-image-editor-bi.png","common.bisize.width":"251px","common.bisize.height":"21px","common.backgroundImage":"none","common.backgroundColor":"#1e1e1e","common.border":"0px","header.backgroundImage":"none","header.backgroundColor":"transparent","header.border":"0px","loadButton.backgroundColor":"#fff","loadButton.border":"1px solid #ddd","loadButton.color":"#222","loadButton.fontFamily":"'Noto Sans', sans-serif","loadButton.fontSize":"12px","downloadButton.backgroundColor":"#fdba3b","downloadButton.border":"1px solid #fdba3b","downloadButton.color":"#fff","downloadButton.fontFamily":"'Noto Sans', sans-serif","downloadButton.fontSize":"12px","menu.normalIcon.color":"#8a8a8a","menu.activeIcon.color":"#555555","menu.disabledIcon.color":"#434343","menu.hoverIcon.color":"#e9e9e9","submenu.normalIcon.color":"#8a8a8a","submenu.activeIcon.color":"#e9e9e9","menu.iconSize.width":"24px","menu.iconSize.height":"24px","submenu.iconSize.width":"32px","submenu.iconSize.height":"32px","submenu.backgroundColor":"#1e1e1e","submenu.partition.color":"#3c3c3c","submenu.normalLabel.color":"#8a8a8a","submenu.normalLabel.fontWeight":"lighter","submenu.activeLabel.color":"#fff","submenu.activeLabel.fontWeight":"lighter","checkbox.border":"0px","checkbox.backgroundColor":"#fff","range.pointer.color":"#fff","range.bar.color":"#666","range.subbar.color":"#d1d1d1","range.disabledPointer.color":"#414141","range.disabledBar.color":"#282828","range.disabledSubbar.color":"#414141","range.value.color":"#fff","range.value.fontWeight":"lighter","range.value.fontSize":"11px","range.value.border":"1px solid #353535","range.value.backgroundColor":"#151515","range.title.color":"#fff","range.title.fontWeight":"lighter","colorpicker.button.border":"1px solid #1e1e1e","colorpicker.title.color":"#fff"}},function(e,t){e.exports='<svg display="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs id="tui-image-editor-svg-default-icons"><symbol id="ic-apply" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" stroke="none" fill="none"></path><path fill="none" stroke="inherit" d="M4 12.011l5 5L20.011 6"></path></symbol><symbol id="ic-cancel" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none" stroke="none"></path><path fill="none" stroke="inherit" d="M6 6l12 12M18 6L6 18"></path></symbol><symbol id="ic-crop" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" stroke="none" fill="none"></path><path stroke="none" fill="inherit" d="M4 0h1v20a1 1 0 0 1-1-1V0zM20 17h-1V5h1v12zm0 2v5h-1v-5h1z"></path><path stroke="none" fill="inherit" d="M5 19h19v1H5zM4.762 4v1H0V4h4.762zM7 4h12a1 1 0 0 1 1 1H7V4z"></path></symbol><symbol id="ic-delete-all" viewBox="0 0 24 24"><path stroke="none" fill="inherit" d="M5 23H3a1 1 0 0 1-1-1V6h1v16h2v1zm16-10h-1V6h1v7zM9 13H8v-3h1v3zm3 0h-1v-3h1v3zm3 0h-1v-3h1v3zM14.794 3.794L13 2h-3L8.206 3.794A.963.963 0 0 1 8 2.5l.703-1.055A1 1 0 0 1 9.535 1h3.93a1 1 0 0 1 .832.445L15 2.5a.965.965 0 0 1-.206 1.294zM14.197 4H8.803h5.394z"></path><path stroke="none" fill="inherit" d="M0 3h23v1H0zM11.286 21H8.714L8 23H7l1-2.8V20h.071L9.5 16h1l1.429 4H12v.2l1 2.8h-1l-.714-2zm-.357-1L10 17.4 9.071 20h1.858zM20 22h3v1h-4v-7h1v6zm-5 0h3v1h-4v-7h1v6z"></path></symbol><symbol id="ic-delete" viewBox="0 0 24 24"><path stroke="none" fill="inherit" d="M3 6v16h17V6h1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V6h1zM14.794 3.794L13 2h-3L8.206 3.794A.963.963 0 0 1 8 2.5l.703-1.055A1 1 0 0 1 9.535 1h3.93a1 1 0 0 1 .832.445L15 2.5a.965.965 0 0 1-.206 1.294zM14.197 4H8.803h5.394z"></path><path stroke="none" fill="inherit" d="M0 3h23v1H0zM8 10h1v6H8v-6zm3 0h1v6h-1v-6zm3 0h1v6h-1v-6z"></path></symbol><symbol id="ic-draw-free" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M2.5 20.929C2.594 10.976 4.323 6 7.686 6c5.872 0 2.524 19 7.697 19s1.89-14.929 6.414-14.929 1.357 10.858 5.13 10.858c1.802 0 2.657-2.262 2.566-6.786"></path></symbol><symbol id="ic-draw-line" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M2 15.5h28"></path></symbol><symbol id="ic-draw" viewBox="0 0 24 24"><path fill="none" stroke="inherit" d="M2.5 21.5H5c.245 0 .48-.058.691-.168l.124-.065.14.01c.429.028.85-.127 1.16-.437L22.55 5.405a.5.5 0 0 0 0-.707l-3.246-3.245a.5.5 0 0 0-.707 0L3.162 16.888a1.495 1.495 0 0 0-.437 1.155l.01.14-.065.123c-.111.212-.17.448-.17.694v2.5z"></path><path stroke="none" fill="inherit" d="M16.414 3.707l3.89 3.89-.708.706-3.889-3.889z"></path></symbol><symbol id="ic-filter" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none" stroke="none"></path><path stroke="none" fill="inherit" d="M12 7v1H2V7h10zm6 0h4v1h-4V7zM12 16v1h10v-1H12zm-6 0H2v1h4v-1z"></path><path stroke="none" fill="inherit" d="M8.5 20a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7zm0-1a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5zM15.5 11a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7zm0-1a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"></path></symbol><symbol id="ic-flip-reset" viewBox="0 0 31 32"><path fill="none" stroke="none" d="M31 0H0v32h31z"></path><path stroke="none" fill="inherit" d="M28 16a8 8 0 0 1-8 8H3v-1h1v-7H3a8 8 0 0 1 8-8h17v1h-1v7h1zM11 9a7 7 0 0 0-7 7v7h16a7 7 0 0 0 7-7V9H11z"></path><path fill="none" stroke="inherit" stroke-linecap="square" d="M24 5l3.5 3.5L24 12M7 20l-3.5 3.5L7 27"></path></symbol><symbol id="ic-flip-x" viewBox="0 0 32 32"><path fill="none" stroke="none" d="M32 32H0V0h32z"></path><path stroke="none" fill="inherit" d="M17 32h-1V0h1zM27.167 11l.5 3h-1.03l-.546-3h1.076zm-.5-3h-1.122L25 5h-5V4h5.153a1 1 0 0 1 .986.836L26.667 8zm1.5 9l.5 3h-.94l-.545-3h.985zm1 6l.639 3.836A1 1 0 0 1 28.819 28H26v-1h3l-.726-4h.894zM23 28h-3v-1h3v1zM13 4v1H7L3 27h10v1H3.18a1 1 0 0 1-.986-1.164l3.666-22A1 1 0 0 1 6.847 4H13z"></path></symbol><symbol id="ic-flip-y" viewBox="0 0 32 32"><path fill="none" stroke="none" d="M0 0v32h32V0z"></path><path stroke="none" fill="inherit" d="M0 16v1h32v-1zM11 27.167l3 .5v-1.03l-3-.546v1.076zm-3-.5v-1.122L5 25v-5H4v5.153a1 1 0 0 0 .836.986L8 26.667zm9 1.5l3 .5v-.94l-3-.545v.985zm6 1l3.836.639A1 1 0 0 0 28 28.82V26h-1v3l-4-.727v.894zM28 23v-3h-1v3h1zM4 13h1V7l22-4v10h1V3.18a1 1 0 0 0-1.164-.986l-22 3.667A1 1 0 0 0 4 6.847V13z"></path></symbol><symbol id="ic-flip" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none" stroke="none"></path><path fill="inherit" stroke="none" d="M11 0h1v24h-1zM19 21v-1h2v-2h1v2a1 1 0 0 1-1 1h-2zm-2 0h-3v-1h3v1zm5-5h-1v-3h1v3zm0-5h-1V8h1v3zm0-5h-1V4h-2V3h2a1 1 0 0 1 1 1v2zm-5-3v1h-3V3h3zM9 3v1H2v16h7v1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h7z"></path></symbol><symbol id="ic-icon-arrow-2" viewBox="0 0 32 32"><path fill="none" stroke="inherit" stroke-linecap="round" stroke-linejoin="round" d="M21.793 18.5H2.5v-5h18.935l-7.6-8h5.872l10.5 10.5-10.5 10.5h-5.914l8-8z"></path></symbol><symbol id="ic-icon-arrow-3" viewBox="0 0 32 32"><path fill="none" stroke="inherit" stroke-linecap="round" stroke-linejoin="round" d="M25.288 16.42L14.208 27.5H6.792l11.291-11.291L6.826 4.5h7.381l11.661 11.661-.58.258z"></path></symbol><symbol id="ic-icon-arrow" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M2.5 11.5v9h18v5.293L30.293 16 20.5 6.207V11.5h-18z"></path></symbol><symbol id="ic-icon-bubble" viewBox="0 0 32 32"><path fill="none" stroke="inherit" stroke-linecap="round" stroke-linejoin="round" d="M22.207 24.5L16.5 30.207V24.5H8A6.5 6.5 0 0 1 1.5 18V9A6.5 6.5 0 0 1 8 2.5h16A6.5 6.5 0 0 1 30.5 9v9a6.5 6.5 0 0 1-6.5 6.5h-1.793z"></path></symbol><symbol id="ic-icon-heart" viewBox="0 0 32 32"><path fill-rule="nonzero" fill="none" stroke="inherit" d="M15.996 30.675l1.981-1.79c7.898-7.177 10.365-9.718 12.135-13.012.922-1.716 1.377-3.37 1.377-5.076 0-4.65-3.647-8.297-8.297-8.297-2.33 0-4.86 1.527-6.817 3.824l-.38.447-.381-.447C13.658 4.027 11.126 2.5 8.797 2.5 4.147 2.5.5 6.147.5 10.797c0 1.714.46 3.375 1.389 5.098 1.775 3.288 4.26 5.843 12.123 12.974l1.984 1.806z"></path></symbol><symbol id="ic-icon-load" viewBox="0 0 32 32"><path fill="none" stroke="inherit" stroke-linecap="round" stroke-linejoin="round" d="M17.314 18.867l1.951-2.53 4 5.184h-17l6.5-8.84 4.549 6.186z"></path><path stroke="none" fill="inherit" d="M18.01 4a11.798 11.798 0 0 0 0 1H3v24h24V14.986a8.738 8.738 0 0 0 1 0V29a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h15.01z"></path><path stroke="none" fill="inherit" d="M25 3h1v9h-1z"></path><path fill="none" stroke="inherit" d="M22 6l3.5-3.5L29 6"></path></symbol><symbol id="ic-icon-location" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M16 31.28C23.675 23.302 27.5 17.181 27.5 13c0-6.351-5.149-11.5-11.5-11.5S4.5 6.649 4.5 13c0 4.181 3.825 10.302 11.5 18.28z"></path><circle fill="none" stroke="inherit" cx="16" cy="13" r="4.5"></circle></symbol><symbol id="ic-icon-polygon" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M.576 16L8.29 29.5h15.42L31.424 16 23.71 2.5H8.29L.576 16z"></path></symbol><symbol id="ic-icon-star-2" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M19.446 31.592l2.265-3.272 3.946.25.636-3.94 3.665-1.505-1.12-3.832 2.655-2.962-2.656-2.962 1.12-3.832-3.664-1.505-.636-3.941-3.946.25-2.265-3.271L16 3.024 12.554 1.07 10.289 4.34l-3.946-.25-.636 3.941-3.665 1.505 1.12 3.832L.508 16.33l2.656 2.962-1.12 3.832 3.664 1.504.636 3.942 3.946-.25 2.265 3.27L16 29.638l3.446 1.955z"></path></symbol><symbol id="ic-icon-star" viewBox="0 0 32 32"><path fill="none" stroke="inherit" d="M25.292 29.878l-1.775-10.346 7.517-7.327-10.388-1.51L16 1.282l-4.646 9.413-10.388 1.51 7.517 7.327-1.775 10.346L16 24.993l9.292 4.885z"></path></symbol><symbol id="ic-icon" viewBox="0 0 24 24"><path fill="none" stroke="inherit" stroke-linecap="round" stroke-linejoin="round" d="M11.923 19.136L5.424 22l.715-7.065-4.731-5.296 6.94-1.503L11.923 2l3.574 6.136 6.94 1.503-4.731 5.296L18.42 22z"></path></symbol><symbol id="ic-mask-load" viewBox="0 0 32 32"><path stroke="none" fill="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M18.01 4a11.798 11.798 0 0 0 0 1H3v24h24V14.986a8.738 8.738 0 0 0 1 0V29a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h15.01zM15 23a6 6 0 1 1 0-12 6 6 0 0 1 0 12zm0-1a5 5 0 1 0 0-10 5 5 0 0 0 0 10z"></path><path stroke="none" fill="inherit" d="M25 3h1v9h-1z"></path><path fill="none" stroke="inherit" d="M22 6l3.5-3.5L29 6"></path></symbol><symbol id="ic-mask" viewBox="0 0 24 24"><circle cx="12" cy="12" r="4.5" stroke="inherit" fill="none"></circle><path stroke="none" fill="inherit" d="M2 1h20a1 1 0 0 1 1 1v20a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zm0 1v20h20V2H2z"></path></symbol><symbol id="ic-redo" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" opacity=".5" fill="none" stroke="none"></path><path stroke="none" fill="inherit" d="M21 6H9a6 6 0 1 0 0 12h12v1H9A7 7 0 0 1 9 5h12v1z"></path><path fill="none" stroke="inherit" stroke-linecap="square" d="M19 3l2.5 2.5L19 8"></path></symbol><symbol id="ic-reset" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" opacity=".5" stroke="none" fill="none"></path><path stroke="none" fill="inherit" d="M2 13v-1a7 7 0 0 1 7-7h13v1h-1v5h1v1a7 7 0 0 1-7 7H2v-1h1v-5H2zm7-7a6 6 0 0 0-6 6v6h12a6 6 0 0 0 6-6V6H9z"></path><path fill="none" stroke="inherit" stroke-linecap="square" d="M19 3l2.5 2.5L19 8M5 16l-2.5 2.5L5 21"></path></symbol><symbol id="ic-rotate-clockwise" viewBox="0 0 32 32"><path stroke="none" fill="inherit" d="M29 17h-.924c0 6.627-5.373 12-12 12-6.628 0-12-5.373-12-12C4.076 10.398 9.407 5.041 16 5V4C8.82 4 3 9.82 3 17s5.82 13 13 13 13-5.82 13-13z"></path><path fill="none" stroke="inherit" stroke-linecap="square" d="M16 1.5l4 3-4 3"></path><path stroke="none" fill="inherit" fill-rule="nonzero" d="M16 4h4v1h-4z"></path></symbol><symbol id="ic-rotate-counterclockwise" viewBox="0 0 32 32"><path stroke="none" d="M3 17h.924c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.602-5.331-11.96-11.924-12V4c7.18 0 13 5.82 13 13s-5.82 13-13 13S3 24.18 3 17z"></path><path stroke="none" fill="inherit" fill-rule="nonzero" d="M12 4h4v1h-4z"></path><path fill="none" stroke="inherit" stroke-linecap="square" d="M16 1.5l-4 3 4 3"></path></symbol><symbol id="ic-rotate" viewBox="0 0 24 24"><path d="M0 0h24v24H0z" fill="none" stroke="none"></path><path fill="inherit" stroke="none" d="M8.349 22.254a10.002 10.002 0 0 1-2.778-1.719l.65-.76a9.002 9.002 0 0 0 2.495 1.548l-.367.931zm2.873.704l.078-.997a9 9 0 1 0-.557-17.852l-.14-.99A10.076 10.076 0 0 1 12.145 3c5.523 0 10 4.477 10 10s-4.477 10-10 10c-.312 0-.62-.014-.924-.042zm-7.556-4.655a9.942 9.942 0 0 1-1.253-2.996l.973-.234a8.948 8.948 0 0 0 1.124 2.693l-.844.537zm-1.502-5.91A9.949 9.949 0 0 1 2.88 9.23l.925.382a8.954 8.954 0 0 0-.644 2.844l-.998-.062zm2.21-5.686c.687-.848 1.51-1.58 2.436-2.166l.523.852a9.048 9.048 0 0 0-2.188 1.95l-.771-.636z"></path><path stroke="inherit" fill="none" stroke-linecap="square" d="M13 1l-2.5 2.5L13 6"></path></symbol><symbol id="ic-shape-circle" viewBox="0 0 32 32"><circle cx="16" cy="16" r="14.5" fill="none" stroke="inherit"></circle></symbol><symbol id="ic-shape-rectangle" viewBox="0 0 32 32"><rect width="27" height="27" x="2.5" y="2.5" fill="none" stroke="inherit" rx="1"></rect></symbol><symbol id="ic-shape-triangle" viewBox="0 0 32 32"><path fill="none" stroke-linecap="round" stroke-linejoin="round" d="M16 2.5l15.5 27H.5z"></path></symbol><symbol id="ic-shape" viewBox="0 0 24 24"><path stroke="none" fill="inherit" d="M14.706 8H21a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H9a1 1 0 0 1-1-1v-4h1v4h12V9h-5.706l-.588-1z"></path><path fill="none" stroke="inherit" stroke-linecap="round" stroke-linejoin="round" d="M8.5 1.5l7.5 13H1z"></path></symbol><symbol id="ic-text-align-center" viewBox="0 0 32 32"><path stroke="none" fill="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M2 5h28v1H2zM8 12h16v1H8zM2 19h28v1H2zM8 26h16v1H8z"></path></symbol><symbol id="ic-text-align-left" viewBox="0 0 32 32"><path stroke="none" fill="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M2 5h28v1H2zM2 12h16v1H2zM2 19h28v1H2zM2 26h16v1H2z"></path></symbol><symbol id="ic-text-align-right" viewBox="0 0 32 32"><path stroke="none" fill="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M2 5h28v1H2zM14 12h16v1H14zM2 19h28v1H2zM14 26h16v1H14z"></path></symbol><symbol id="ic-text-bold" viewBox="0 0 32 32"><path fill="none" stroke="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M7 2h2v2H7zM7 28h2v2H7z"></path><path fill="none" stroke="inherit" stroke-width="2" d="M9 3v12h9a6 6 0 1 0 0-12H9zM9 15v14h10a7 7 0 0 0 0-14H9z"></path></symbol><symbol id="ic-text-italic" viewBox="0 0 32 32"><path fill="none" stroke="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M15 2h5v1h-5zM11 29h5v1h-5zM17 3h1l-4 26h-1z"></path></symbol><symbol id="ic-text-underline" viewBox="0 0 32 32"><path stroke="none" fill="none" d="M0 0h32v32H0z"></path><path stroke="none" fill="inherit" d="M8 2v14a8 8 0 1 0 16 0V2h1v14a9 9 0 0 1-18 0V2h1zM3 29h26v1H3z"></path><path stroke="none" fill="inherit" d="M5 2h5v1H5zM22 2h5v1h-5z"></path></symbol><symbol id="ic-text" viewBox="0 0 24 24"><path stroke="none" fill="inherit" d="M4 3h15a1 1 0 0 1 1 1H3a1 1 0 0 1 1-1zM3 4h1v1H3zM19 4h1v1h-1z"></path><path stroke="none" fill="inherit" d="M11 3h1v18h-1z"></path><path stroke="none" fill="inherit" d="M10 20h3v1h-3z"></path></symbol><symbol id="ic-undo" viewBox="0 0 24 24"><path d="M24 0H0v24h24z" opacity=".5" fill="none" stroke="none"></path><path stroke="none" fill="inherit" d="M3 6h12a6 6 0 1 1 0 12H3v1h12a7 7 0 0 0 0-14H3v1z"></path><path fill="none" stroke="inherit" stroke-linecap="square" d="M5 3L2.5 5.5 5 8"></path></symbol></defs></svg>'},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var s=d(n(15)),l=d(n(16)),a=d(n(9)),c=d(n(89)),r=n(4),u=n(0);function d(e){return e&&e.__esModule?e:{default:e}}var h={stroke:"#ffbb3b",fill:"",strokeWidth:3},f=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(v,a.default),i(v,[{key:"destroy",value:function(){this._removeEvent(),this._els.strokeRange.destroy(),this._els.fillColorpicker.destroy(),this._els.strokeColorpicker.destroy(),(0,r.assignmentForDestroy)(this)}},{key:"addEvent",value:function(e){this.eventHandler.shapeTypeSelected=this._changeShapeHandler.bind(this),this.actions=e,this._els.shapeSelectButton.addEventListener("click",this.eventHandler.shapeTypeSelected),this._els.strokeRange.on("change",this._changeStrokeRangeHandler.bind(this)),this._els.fillColorpicker.on("change",this._changeFillColorHandler.bind(this)),this._els.strokeColorpicker.on("change",this._changeStrokeColorHandler.bind(this)),this._els.fillColorpicker.on("changeShow",this.colorPickerChangeShow.bind(this)),this._els.strokeColorpicker.on("changeShow",this.colorPickerChangeShow.bind(this))}},{key:"_removeEvent",value:function(){this._els.shapeSelectButton.removeEventListener("click",this.eventHandler.shapeTypeSelected),this._els.strokeRange.off(),this._els.fillColorpicker.off(),this._els.strokeColorpicker.off()}},{key:"setShapeStatus",value:function(e){var t=e.strokeWidth,n=e.strokeColor,i=e.fillColor;this._els.strokeRange.value=t,this._els.strokeColorpicker.color=n,this._els.fillColorpicker.color=i,this.options.stroke=n,this.options.fill=i,this.options.strokeWidth=t,this.actions.setDrawingShape(this.type,{strokeWidth:t})}},{key:"changeStartMode",value:function(){this.actions.stopDrawingMode()}},{key:"changeStandbyMode",value:function(){this.type=null,this.actions.changeSelectableAll(!0),this._els.shapeSelectButton.classList.remove("circle"),this._els.shapeSelectButton.classList.remove("triangle"),this._els.shapeSelectButton.classList.remove("rect")}},{key:"setMaxStrokeValue",value:function(e){var t=e;t<=0&&(t=u.defaultShapeStrokeValus.max),this._els.strokeRange.max=t}},{key:"setStrokeValue",value:function(e){this._els.strokeRange.value=e,this._els.strokeRange.trigger("change")}},{key:"getStrokeValue",value:function(){return this._els.strokeRange.value}},{key:"_changeShapeHandler",value:function(e){var t=e.target.closest(".tui-image-editor-button");if(t){this.actions.stopDrawingMode(),this.actions.discardSelection();var n=this.getButtonType(t,["circle","triangle","rect"]);if(this.type===n)return void this.changeStandbyMode();this.changeStandbyMode(),this.type=n,e.currentTarget.classList.add(n),this.actions.changeSelectableAll(!1),this.actions.modeChange("shape")}}},{key:"_changeStrokeRangeHandler",value:function(e,t){this.options.strokeWidth=(0,r.toInteger)(e),this.actions.changeShape({strokeWidth:e},!t),this.actions.setDrawingShape(this.type,this.options)}},{key:"_changeFillColorHandler",value:function(e){e=e||"transparent",this.options.fill=e,this.actions.changeShape({fill:e})}},{key:"_changeStrokeColorHandler",value:function(e){e=e||"transparent",this.options.stroke=e,this.actions.changeShape({stroke:e})}}]),v);function v(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,v);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,e,{locale:n,name:"shape",makeSvgIcon:i,menuBarPosition:o,templateHtml:c.default,usageStatistics:a}));return r.type=null,r.options=h,r._els={shapeSelectButton:r.selector(".tie-shape-button"),shapeColorButton:r.selector(".tie-shape-color-button"),strokeRange:new l.default({slider:r.selector(".tie-stroke-range"),input:r.selector(".tie-stroke-range-value")},u.defaultShapeStrokeValus),fillColorpicker:new s.default(r.selector(".tie-color-fill"),"",r.toggleDirection,r.usageStatistics),strokeColorpicker:new s.default(r.selector(".tie-color-stroke"),"#ffbb3b",r.toggleDirection,r.usageStatistics)},r.colorPickerControls.push(r._els.fillColorpicker),r.colorPickerControls.push(r._els.strokeColorpicker),r}t.default=f},function(e,t){e.exports=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tie-shape-button">\n            <div class="tui-image-editor-button rect">\n                <div>\n                    '+n(["normal","active"],"shape-rectangle",!0)+"\n                </div>\n                <label> "+t.localize("Rectangle")+' </label>\n            </div>\n            <div class="tui-image-editor-button circle">\n                <div>\n                    '+n(["normal","active"],"shape-circle",!0)+"\n                </div>\n                <label> "+t.localize("Circle")+' </label>\n            </div>\n            <div class="tui-image-editor-button triangle">\n                <div>\n                    '+n(["normal","active"],"shape-triangle",!0)+"\n                </div>\n                <label> "+t.localize("Triangle")+' </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li class="tie-shape-color-button">\n            <div class="tie-color-fill" title="'+t.localize("Fill")+'"></div>\n            <div class="tie-color-stroke" title="'+t.localize("Stroke")+'"></div>\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tui-image-editor-newline tui-image-editor-range-wrap">\n            <label class="range">'+t.localize("Stroke")+'</label>\n            <div class="tie-stroke-range"></div>\n            <input class="tie-stroke-range-value tui-image-editor-range-value" value="0" />\n        </li>\n    </ul>\n'}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=c(n(1)),r=c(n(9)),s=c(n(4)),l=c(n(91));function c(e){return e&&e.__esModule?e:{default:e}}var u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,r.default),i(d,[{key:"destroy",value:function(){this._removeEvent(),s.default.assignmentForDestroy(this)}},{key:"addEvent",value:function(e){var t=this._applyEventHandler.bind(this),n=this._cancelEventHandler.bind(this),i=this._cropzonePresetEventHandler.bind(this);this.eventHandler={apply:t,cancel:n,cropzonePreset:i},this.actions=e,this._els.apply.addEventListener("click",t),this._els.cancel.addEventListener("click",n),this._els.preset.addEventListener("click",i)}},{key:"_removeEvent",value:function(){this._els.apply.removeEventListener("click",this.eventHandler.apply),this._els.cancel.removeEventListener("click",this.eventHandler.cancel),this._els.preset.removeEventListener("click",this.eventHandler.cropzonePreset)}},{key:"_applyEventHandler",value:function(){this.actions.crop(),this._els.apply.classList.remove("active")}},{key:"_cancelEventHandler",value:function(){this.actions.cancel(),this._els.apply.classList.remove("active")}},{key:"_cropzonePresetEventHandler",value:function(e){var t=e.target.closest(".tui-image-editor-button.preset");if(t){var n=t.className.match(/preset-[^\s]+/)[0];this._setPresetButtonActive(t),this.actions.preset(n)}}},{key:"changeStartMode",value:function(){this.actions.modeChange("crop")}},{key:"changeStandbyMode",value:function(){this.actions.stopDrawingMode(),this._setPresetButtonActive()}},{key:"changeApplyButtonStatus",value:function(e){e?this._els.apply.classList.add("active"):this._els.apply.classList.remove("active")}},{key:"_setPresetButtonActive",value:function(e){var t=0<arguments.length&&void 0!==e?e:this.defaultPresetButton;a.default.forEach([].slice.call(this._els.preset.querySelectorAll(".preset")),function(e){e.classList.remove("active")}),t&&t.classList.add("active")}}]),d);function d(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,e,{locale:n,name:"crop",makeSvgIcon:i,menuBarPosition:o,templateHtml:l.default,usageStatistics:a}));return r.status="active",r._els={apply:r.selector(".tie-crop-button .apply"),cancel:r.selector(".tie-crop-button .cancel"),preset:r.selector(".tie-crop-preset-button")},r.defaultPresetButton=r._els.preset.querySelector(".preset-none"),r}t.default=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tie-crop-preset-button">\n            <div class="tui-image-editor-button preset preset-none active">\n                <div>\n                    '+n(["normal","active"],"shape-rectangle",!0)+"\n                </div>\n                <label> "+t.localize("Custom")+' </label>\n            </div>\n            <div class="tui-image-editor-button preset preset-square">\n                <div>\n                    '+n(["normal","active"],"crop",!0)+"\n                </div>\n                <label> "+t.localize("Square")+' </label>\n            </div>\n            <div class="tui-image-editor-button preset preset-3-2">\n                <div>\n                    '+n(["normal","active"],"crop",!0)+"\n                </div>\n                <label> "+t.localize("3:2")+' </label>\n            </div>\n            <div class="tui-image-editor-button preset preset-4-3">\n                <div>\n                    '+n(["normal","active"],"crop",!0)+"\n                </div>\n                <label> "+t.localize("4:3")+' </label>\n            </div>\n            <div class="tui-image-editor-button preset preset-5-4">\n                <div>\n                    '+n(["normal","active"],"crop",!0)+"\n                </div>\n                <label> "+t.localize("5:4")+' </label>\n            </div>\n            <div class="tui-image-editor-button preset preset-7-5">\n                <div>\n                    '+n(["normal","active"],"crop",!0)+"\n                </div>\n                <label> "+t.localize("7:5")+' </label>\n            </div>\n            <div class="tui-image-editor-button preset preset-16-9">\n                <div>\n                    '+n(["normal","active"],"crop",!0)+"\n                </div>\n                <label> "+t.localize("16:9")+' </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition tui-image-editor-newline">\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tie-crop-button action">\n            <div class="tui-image-editor-button apply">\n                '+n(["normal","active"],"apply")+"\n                <label>\n                    "+t.localize("Apply")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button cancel">\n                '+n(["normal","active"],"cancel")+"\n                <label>\n                    "+t.localize("Cancel")+"\n                </label>\n            </div>\n        </li>\n    </ul>\n"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=c(n(1)),r=c(n(4)),s=c(n(9)),l=c(n(93));function c(e){return e&&e.__esModule?e:{default:e}}var u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,s.default),i(d,[{key:"destroy",value:function(){this._removeEvent(),r.default.assignmentForDestroy(this)}},{key:"addEvent",value:function(e){this.eventHandler.changeFlip=this._changeFlip.bind(this),this._actions=e,this._els.flipButton.addEventListener("click",this.eventHandler.changeFlip)}},{key:"_removeEvent",value:function(){this._els.flipButton.removeEventListener("click",this.eventHandler.changeFlip)}},{key:"_changeFlip",value:function(e){var i=this,t=e.target.closest(".tui-image-editor-button");if(t){var n=this.getButtonType(t,["flipX","flipY","resetFlip"]);if(!this.flipStatus&&"resetFlip"===n)return;this._actions.flip(n).then(function(t){var n=i._els.flipButton.classList;i.flipStatus=!1,n.remove("resetFlip"),a.default.forEach(["flipX","flipY"],function(e){n.remove(e),t[e]&&(n.add(e),n.add("resetFlip"),i.flipStatus=!0)})})}}}]),d);function d(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,e,{locale:n,name:"flip",makeSvgIcon:i,menuBarPosition:o,templateHtml:l.default,usageStatistics:a}));return r.flipStatus=!1,r._els={flipButton:r.selector(".tie-flip-button")},r}t.default=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tie-flip-button tui-image-editor-submenu-item">\n        <li>\n            <div class="tui-image-editor-button flipX">\n                <div>\n                    '+n(["normal","active"],"flip-x",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Flip X")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button flipY">\n                <div>\n                    '+n(["normal","active"],"flip-y",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Flip Y")+'\n                </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li>\n            <div class="tui-image-editor-button resetFlip">\n                <div>\n                    '+n(["normal","active"],"flip-reset",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Reset")+"\n                </label>\n            </div>\n        </li>\n    </ul>\n"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var s=u(n(16)),a=u(n(9)),l=u(n(95)),r=n(4),c=n(0);function u(e){return e&&e.__esModule?e:{default:e}}var d=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(h,a.default),i(h,[{key:"destroy",value:function(){this._removeEvent(),this._els.rotateRange.destroy(),(0,r.assignmentForDestroy)(this)}},{key:"setRangeBarAngle",value:function(e,t){var n=t;"rotate"===e&&(n=parseInt(this._els.rotateRange.value,10)+t),this._setRangeBarRatio(n)}},{key:"_setRangeBarRatio",value:function(e){this._els.rotateRange.value=e}},{key:"addEvent",value:function(e){this.eventHandler.rotationAngleChanged=this._changeRotateForButton.bind(this),this.actions=e,this._els.rotateButton.addEventListener("click",this.eventHandler.rotationAngleChanged),this._els.rotateRange.on("change",this._changeRotateForRange.bind(this))}},{key:"_removeEvent",value:function(){this._els.rotateButton.removeEventListener("click",this.eventHandler.rotationAngleChanged),this._els.rotateRange.off()}},{key:"_changeRotateForRange",value:function(e,t){var n=(0,r.toInteger)(e);this.actions.setAngle(n,!t),this._value=n}},{key:"_changeRotateForButton",value:function(e){var t=e.target.closest(".tui-image-editor-button"),n=this._els.rotateRange.value;if(t){var i={clockwise:30,counterclockwise:-30}[this.getButtonType(t,["counterclockwise","clockwise"])],o=parseInt(n,10)+i;-360<=o&&o<=360&&this.actions.rotate(i)}}}]),h);function h(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,h);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,e,{locale:n,name:"rotate",makeSvgIcon:i,menuBarPosition:o,templateHtml:l.default,usageStatistics:a}));return r._value=0,r._els={rotateButton:r.selector(".tie-retate-button"),rotateRange:new s.default({slider:r.selector(".tie-rotate-range"),input:r.selector(".tie-ratate-range-value")},c.defaultRotateRangeValus)},r}t.default=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tie-retate-button">\n            <div class="tui-image-editor-button clockwise">\n                <div>\n                    '+n(["normal","active"],"rotate-clockwise",!0)+'\n                </div>\n                <label> 30 </label>\n            </div>\n            <div class="tui-image-editor-button counterclockwise">\n                <div>\n                    '+n(["normal","active"],"rotate-counterclockwise",!0)+'\n                </div>\n                <label> -30 </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tui-image-editor-newline tui-image-editor-range-wrap">\n            <label class="range">'+t.localize("Range")+'</label>\n            <div class="tie-rotate-range"></div>\n            <input class="tie-ratate-range-value tui-image-editor-range-value" value="0" />\n        </li>\n    </ul>\n'}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(n(4)),s=d(n(16)),l=d(n(15)),r=d(n(9)),c=d(n(97)),u=n(0);function d(e){return e&&e.__esModule?e:{default:e}}var h=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,r.default),i(f,[{key:"destroy",value:function(){this._removeEvent(),this._els.textColorpicker.destroy(),this._els.textRange.destroy(),a.default.assignmentForDestroy(this)}},{key:"addEvent",value:function(e){var t=this._setTextEffectHandler.bind(this),n=this._setTextAlignHandler.bind(this);this.eventHandler={setTextEffect:t,setTextAlign:n},this.actions=e,this._els.textEffectButton.addEventListener("click",t),this._els.textAlignButton.addEventListener("click",n),this._els.textRange.on("change",this._changeTextRnageHandler.bind(this)),this._els.textColorpicker.on("change",this._changeColorHandler.bind(this))}},{key:"_removeEvent",value:function(){var e=this.eventHandler,t=e.setTextEffect,n=e.setTextAlign;this._els.textEffectButton.removeEventListener("click",t),this._els.textAlignButton.removeEventListener("click",n),this._els.textRange.off(),this._els.textColorpicker.off()}},{key:"changeStandbyMode",value:function(){this.actions.stopDrawingMode()}},{key:"changeStartMode",value:function(){this.actions.modeChange("text")}},{key:"setTextStyleStateOnAction",value:function(e){var t=0<arguments.length&&void 0!==e?e:{},n=t.fill,i=t.fontSize,o=t.fontStyle,a=t.fontWeight,r=t.textDecoration,s=t.textAlign;this.textColor=n,this.fontSize=i,this.setEffactState("italic",o),this.setEffactState("bold",a),this.setEffactState("underline",r),this.setAlignState(s)}},{key:"setEffactState",value:function(e,t){var n="italic"===t||"bold"===t||"underline"===t,i=this._els.textEffectButton.querySelector(".tui-image-editor-button."+e);this.effect[e]=n,i.classList[n?"add":"remove"]("active")}},{key:"setAlignState",value:function(e){var t=this._els.textAlignButton;t.classList.remove(this.align),t.classList.add(e),this.align=e}},{key:"_setTextEffectHandler",value:function(e){var t=e.target.closest(".tui-image-editor-button"),n=t.className.match(/(bold|italic|underline)/)[0],i={bold:{fontWeight:"bold"},italic:{fontStyle:"italic"},underline:{textDecoration:"underline"}}[n];this.effect[n]=!this.effect[n],t.classList.toggle("active"),this.actions.changeTextStyle(i)}},{key:"_setTextAlignHandler",value:function(e){var t=e.target.closest(".tui-image-editor-button");if(t){var n=this.getButtonType(t,["left","center","right"]);e.currentTarget.classList.remove(this.align),this.align!==n&&e.currentTarget.classList.add(n),this.actions.changeTextStyle({textAlign:n}),this.align=n}}},{key:"_changeTextRnageHandler",value:function(e,t){this.actions.changeTextStyle({fontSize:e},!t)}},{key:"_changeColorHandler",value:function(e){e=e||"transparent",this.actions.changeTextStyle({fill:e})}},{key:"textColor",set:function(e){this._els.textColorpicker.color=e},get:function(){return this._els.textColorpicker.color}},{key:"fontSize",get:function(){return this._els.textRange.value},set:function(e){this._els.textRange.value=e}},{key:"fontStyle",get:function(){return this.effect.italic?"italic":"normal"}},{key:"fontWeight",get:function(){return this.effect.bold?"bold":"normal"}},{key:"underline",get:function(){return this.effect.underline}}]),f);function f(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,{locale:n,name:"text",makeSvgIcon:i,menuBarPosition:o,templateHtml:c.default,usageStatistics:a}));return r.effect={bold:!1,italic:!1,underline:!1},r.align="left",r._els={textEffectButton:r.selector(".tie-text-effect-button"),textAlignButton:r.selector(".tie-text-align-button"),textColorpicker:new l.default(r.selector(".tie-text-color"),"#ffbb3b",r.toggleDirection,r.usageStatistics),textRange:new s.default({slider:r.selector(".tie-text-range"),input:r.selector(".tie-text-range-value")},u.defaultTextRangeValus)},r}t.default=h},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tie-text-effect-button">\n            <div class="tui-image-editor-button bold">\n                <div>\n                    '+n(["normal","active"],"text-bold",!0)+"\n                </div>\n                <label> "+t.localize("Bold")+' </label>\n            </div>\n            <div class="tui-image-editor-button italic">\n                <div>\n                    '+n(["normal","active"],"text-italic",!0)+"\n                </div>\n                <label> "+t.localize("Italic")+' </label>\n            </div>\n            <div class="tui-image-editor-button underline">\n                <div>\n                    '+n(["normal","active"],"text-underline",!0)+"\n                </div>\n                <label> "+t.localize("Underline")+' </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li class="tie-text-align-button">\n            <div class="tui-image-editor-button left">\n                <div>\n                    '+n(["normal","active"],"text-align-left",!0)+"\n                </div>\n                <label> "+t.localize("Left")+' </label>\n            </div>\n            <div class="tui-image-editor-button center">\n                <div>\n                    '+n(["normal","active"],"text-align-center",!0)+"\n                </div>\n                <label> "+t.localize("Center")+' </label>\n            </div>\n            <div class="tui-image-editor-button right">\n                <div>\n                    '+n(["normal","active"],"text-align-right",!0)+"\n                </div>\n                <label> "+t.localize("Right")+' </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li>\n            <div class="tie-text-color" title="'+t.localize("Color")+'"></div>\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tui-image-editor-newline tui-image-editor-range-wrap">\n            <label class="range">'+t.localize("Text size")+'</label>\n            <div class="tie-text-range"></div>\n            <input class="tie-text-range-value tui-image-editor-range-value" value="0" />\n        </li>\n    </ul>\n'}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(n(9)),r=l(n(4)),s=l(n(99));function l(e){return e&&e.__esModule?e:{default:e}}var c=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,a.default),i(u,[{key:"destroy",value:function(){this._removeEvent(),r.default.assignmentForDestroy(this)}},{key:"addEvent",value:function(e){var t=this._loadMaskFile.bind(this),n=this._applyMask.bind(this);this.eventHandler={loadMaskFile:t,applyMask:n},this.actions=e,this._els.maskImageButton.addEventListener("change",t),this._els.applyButton.addEventListener("click",n)}},{key:"_removeEvent",value:function(){this._els.maskImageButton.removeEventListener("change",this.eventHandler.loadMaskFile),this._els.applyButton.removeEventListener("click",this.eventHandler.applyMask)}},{key:"_applyMask",value:function(){this.actions.applyFilter(),this._els.applyButton.classList.remove("active")}},{key:"_loadMaskFile",value:function(e){var t=void 0;r.default.isSupportFileApi()||alert("This browser does not support file-api");var n=e.target.files[0];n&&(t=URL.createObjectURL(n),this.actions.loadImageFromURL(t,n),this._els.applyButton.classList.add("active"))}}]),u);function u(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(u.__proto__||Object.getPrototypeOf(u)).call(this,e,{locale:n,name:"mask",makeSvgIcon:i,menuBarPosition:o,templateHtml:s.default,usageStatistics:a}));return r._els={applyButton:r.selector(".tie-mask-apply"),maskImageButton:r.selector(".tie-mask-image-file")},r}t.default=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li>\n            <div class="tui-image-editor-button">\n                <div>\n                    <input type="file" accept="image/*" class="tie-mask-image-file">\n                    '+n(["normal","active"],"mask-load",!0)+"\n                </div>\n                <label> "+t.localize("Load Mask Image")+' </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tie-mask-apply tui-image-editor-newline apply" style="margin-top: 22px;margin-bottom: 5px">\n            <div class="tui-image-editor-button apply">\n                '+n(["normal","active"],"apply")+"\n                <label>\n                    "+t.localize("Apply")+"\n                </label>\n            </div>\n        </li>\n    </ul>\n"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(n(1)),s=d(n(15)),r=d(n(9)),l=d(n(101)),c=n(4),u=n(0);function d(e){return e&&e.__esModule?e:{default:e}}var h=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,r.default),i(f,[{key:"destroy",value:function(){this._removeEvent(),this._els.iconColorpicker.destroy(),(0,c.assignmentForDestroy)(this)}},{key:"addEvent",value:function(e){var t=this._registerIconHandler.bind(this),n=this._addIconHandler.bind(this);this.eventHandler={registerIcon:t,addIcon:n},this.actions=e,this._els.iconColorpicker.on("change",this._changeColorHandler.bind(this)),this._els.registrIconButton.addEventListener("change",t),this._els.addIconButton.addEventListener("click",n)}},{key:"_removeEvent",value:function(){this._els.iconColorpicker.off(),this._els.registrIconButton.removeEventListener("change",this.eventHandler.registerIcon),this._els.addIconButton.removeEventListener("click",this.eventHandler.addIcon)}},{key:"clearIconType",value:function(){this._els.addIconButton.classList.remove(this.iconType),this.iconType=null}},{key:"registDefaultIcon",value:function(){var n=this;a.default.forEach(u.defaultIconPath,function(e,t){n.actions.registDefalutIcons(t,e)})}},{key:"setIconPickerColor",value:function(e){this._els.iconColorpicker.color=e}},{key:"changeStandbyMode",value:function(){this.clearIconType(),this.actions.cancelAddIcon()}},{key:"_changeColorHandler",value:function(e){e=e||"transparent",this.actions.changeColor(e)}},{key:"_addIconHandler",value:function(e){var t=e.target.closest(".tui-image-editor-button");if(t){var n=t.getAttribute("data-icontype"),i=this._els.iconColorpicker.color;this.actions.discardSelection(),this.actions.changeSelectableAll(!1),this._els.addIconButton.classList.remove(this.iconType),this._els.addIconButton.classList.add(n),this.iconType===n?this.changeStandbyMode():(this.actions.addIcon(n,i),this.iconType=n)}}},{key:"_registerIconHandler",value:function(e){var t=void 0;c.isSupportFileApi||alert("This browser does not support file-api");var n=e.target.files[0];n&&(t=URL.createObjectURL(n),this.actions.registCustomIcon(t,n))}}]),f);function f(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,{locale:n,name:"icon",makeSvgIcon:i,menuBarPosition:o,templateHtml:l.default,usageStatistics:a}));return r.iconType=null,r._iconMap={},r._els={registrIconButton:r.selector(".tie-icon-image-file"),addIconButton:r.selector(".tie-icon-add-button"),iconColorpicker:new s.default(r.selector(".tie-icon-color"),"#ffbb3b",r.toggleDirection,r.usageStatistics)},r}t.default=h},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tie-icon-add-button">\n            <div class="tui-image-editor-button" data-icontype="icon-arrow">\n                <div>\n                    '+n(["normal","active"],"icon-arrow",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Arrow")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button" data-icontype="icon-arrow-2">\n                <div>\n                    '+n(["normal","active"],"icon-arrow-2",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Arrow-2")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button" data-icontype="icon-arrow-3">\n                <div>\n                    '+n(["normal","active"],"icon-arrow-3",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Arrow-3")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button" data-icontype="icon-star">\n                <div>\n                    '+n(["normal","active"],"icon-star",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Star-1")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button" data-icontype="icon-star-2">\n                <div>\n                    '+n(["normal","active"],"icon-star-2",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Star-2")+'\n                </label>\n            </div>\n\n            <div class="tui-image-editor-button" data-icontype="icon-polygon">\n                <div>\n                    '+n(["normal","active"],"icon-polygon",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Polygon")+'\n                </label>\n            </div>\n\n            <div class="tui-image-editor-button" data-icontype="icon-location">\n                <div>\n                    '+n(["normal","active"],"icon-location",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Location")+'\n                </label>\n            </div>\n\n            <div class="tui-image-editor-button" data-icontype="icon-heart">\n                <div>\n                    '+n(["normal","active"],"icon-heart",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Heart")+'\n                </label>\n            </div>\n\n            <div class="tui-image-editor-button" data-icontype="icon-bubble">\n                <div>\n                    '+n(["normal","active"],"icon-bubble",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Bubble")+'\n                </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li class="tie-icon-add-button">\n            <div class="tui-image-editor-button" style="margin:0">\n                <div>\n                    <input type="file" accept="image/*" class="tie-icon-image-file">\n                    '+n(["normal","active"],"icon-load",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Custom icon")+'\n                </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li>\n            <div class="tie-icon-color" title="'+t.localize("Color")+'"></div>\n        </li>\n    </ul>\n'}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(n(4)),s=d(n(15)),l=d(n(16)),r=d(n(9)),c=d(n(103)),u=n(0);function d(e){return e&&e.__esModule?e:{default:e}}var h=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,r.default),i(f,[{key:"destroy",value:function(){this._removeEvent(),this._els.drawColorPicker.destroy(),this._els.drawRange.destroy(),a.default.assignmentForDestroy(this)}},{key:"addEvent",value:function(e){this.eventHandler.changeDrawType=this._changeDrawType.bind(this),this.actions=e,this._els.lineSelectButton.addEventListener("click",this.eventHandler.changeDrawType),this._els.drawColorPicker.on("change",this._changeDrawColor.bind(this)),this._els.drawRange.on("change",this._changeDrawRange.bind(this))}},{key:"_removeEvent",value:function(){this._els.lineSelectButton.removeEventListener("click",this.eventHandler.changeDrawType),this._els.drawColorPicker.off(),this._els.drawRange.off()}},{key:"setDrawMode",value:function(){this.actions.setDrawMode(this.type,{width:this.width,color:a.default.getRgb(this.color,.7)})}},{key:"changeStandbyMode",value:function(){this.type=null,this.actions.stopDrawingMode(),this.actions.changeSelectableAll(!0),this._els.lineSelectButton.classList.remove("free"),this._els.lineSelectButton.classList.remove("line")}},{key:"changeStartMode",value:function(){this.type="free",this._els.lineSelectButton.classList.add("free"),this.setDrawMode()}},{key:"_changeDrawType",value:function(e){var t=e.target.closest(".tui-image-editor-button");if(t){var n=this.getButtonType(t,["free","line"]);if(this.actions.discardSelection(),this.type===n)return void this.changeStandbyMode();this.changeStandbyMode(),this.type=n,this._els.lineSelectButton.classList.add(n),this.setDrawMode()}}},{key:"_changeDrawColor",value:function(e){this.color=e||"transparent",this.type?this.setDrawMode():this.changeStartMode()}},{key:"_changeDrawRange",value:function(e){this.width=e,this.type?this.setDrawMode():this.changeStartMode()}}]),f);function f(e,t){var n=t.locale,i=t.makeSvgIcon,o=t.menuBarPosition,a=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,e,{locale:n,name:"draw",makeSvgIcon:i,menuBarPosition:o,templateHtml:c.default,usageStatistics:a}));return r._els={lineSelectButton:r.selector(".tie-draw-line-select-button"),drawColorPicker:new s.default(r.selector(".tie-draw-color"),"#00a9ff",r.toggleDirection,r.usageStatistics),drawRange:new l.default({slider:r.selector(".tie-draw-range"),input:r.selector(".tie-draw-range-value")},u.defaultDrawRangeValus)},r.type=null,r.color=r._els.drawColorPicker.color,r.width=r._els.drawRange.value,r}t.default=h},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale,n=e.makeSvgIcon;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tie-draw-line-select-button">\n            <div class="tui-image-editor-button free">\n                <div>\n                    '+n(["normal","active"],"draw-free",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Free")+'\n                </label>\n            </div>\n            <div class="tui-image-editor-button line">\n                <div>\n                    '+n(["normal","active"],"draw-line",!0)+"\n                </div>\n                <label>\n                    "+t.localize("Straight")+'\n                </label>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li>\n            <div class="tie-draw-color" title="'+t.localize("Color")+'"></div>\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tui-image-editor-newline tui-image-editor-range-wrap">\n            <label class="range">'+t.localize("Range")+'</label>\n            <div class="tie-draw-range"></div>\n            <input class="tie-draw-range-value tui-image-editor-range-value" value="0" />\n        </li>\n    </ul>\n'}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var r=h(n(1)),a=h(n(15)),s=h(n(16)),l=h(n(9)),c=h(n(105)),u=n(4),d=n(0);function h(e){return e&&e.__esModule?e:{default:e}}var f=["add","diff","subtract","multiply","screen","lighten","darken"],v=["grayscale","invert","sepia","vintage","blur","sharpen","emboss","remove-white","brightness","noise","pixelate","color-filter","tint","multiply","blend"],p={grayscale:"grayscale",invert:"invert",sepia:"sepia",blur:"blur",sharpen:"sharpen",emboss:"emboss",removeWhite:"removeColor",brightness:"brightness",contrast:"contrast",saturation:"saturation",vintage:"vintage",polaroid:"polaroid",noise:"noise",pixelate:"pixelate",colorFilter:"removeColor",tint:"blendColor",multiply:"blendColor",blend:"blendColor",hue:"hue",gamma:"gamma"},g=["removewhiteDistanceRange","colorfilterThresholeRange","pixelateRange","noiseRange","brightnessRange","tintOpacity"],m=["filterBlendColor","filterMultiplyColor","filterTintColor"],b=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(y,l.default),i(y,[{key:"destroy",value:function(){this._removeEvent(),this._destroyToolInstance(),(0,u.assignmentForDestroy)(this)}},{key:"_removeEvent",value:function(){var i=this;r.default.forEach(v,function(e){var t=i.selector(".tie-"+e),n=(0,u.toCamelCase)(e);t.removeEventListener("change",i.eventHandler[n])}),r.default.forEach([].concat(g,m),function(e){i._els[e].off()}),this._els.blendType.removeEventListener("change",this.eventHandler.changeBlendFilter),this._els.blendType.removeEventListener("click",this.eventHandler.changeBlendFilter)}},{key:"_destroyToolInstance",value:function(){var t=this;r.default.forEach([].concat(g,m),function(e){t._els[e].destroy()})}},{key:"addEvent",value:function(e){function i(e){return o._changeFilterState.bind(o,a,e)}function t(n){return function(e,t){return o._changeFilterState(a,n,t)}}var o=this,a=e.applyFilter;this.eventHandler={changeBlendFilter:i("blend"),blandTypeClick:function(e){return e.stopPropagation()}},r.default.forEach(v,function(e){var t=o.selector(".tie-"+e),n=(0,u.toCamelCase)(e);o.checkedMap[n]=t,o.eventHandler[n]=i(n),t.addEventListener("change",o.eventHandler[n])}),this._els.removewhiteDistanceRange.on("change",t("removeWhite")),this._els.colorfilterThresholeRange.on("change",t("colorFilter")),this._els.pixelateRange.on("change",t("pixelate")),this._els.noiseRange.on("change",t("noise")),this._els.brightnessRange.on("change",t("brightness")),this._els.filterBlendColor.on("change",this.eventHandler.changeBlendFilter),this._els.filterMultiplyColor.on("change",i("multiply")),this._els.filterTintColor.on("change",i("tint")),this._els.tintOpacity.on("change",t("tint")),this._els.filterMultiplyColor.on("changeShow",this.colorPickerChangeShow.bind(this)),this._els.filterTintColor.on("changeShow",this.colorPickerChangeShow.bind(this)),this._els.filterBlendColor.on("changeShow",this.colorPickerChangeShow.bind(this)),this._els.blendType.addEventListener("change",this.eventHandler.changeBlendFilter),this._els.blendType.addEventListener("click",this.eventHandler.blandTypeClick)}},{key:"setFilterState",value:function(e){var t=e.type,n=e.options,i=e.action,o=this._getFilterNameFromOptions(t,n),a="remove"===i;a||this._setFilterState(o,n),this.checkedMap[o].checked=!a}},{key:"_setFilterState",value:function(e,t){"colorFilter"===e?this._els.colorfilterThresholeRange.value=t.distance:"removeWhite"===e?this._els.removewhiteDistanceRange.value=t.distance:"pixelate"===e?this._els.pixelateRange.value=t.blocksize:"brightness"===e?this._els.brightnessRange.value=t.brightness:"noise"===e?this._els.noiseRange.value=t.noise:"tint"===e?(this._els.tintOpacity.value=t.alpha,this._els.filterTintColor.color=t.color):"blend"===e?this._els.filterBlendColor.color=t.color:"multiply"===e&&(this._els.filterMultiplyColor.color=t.color)}},{key:"_getFilterNameFromOptions",value:function(e,t){var n=e;return"removeColor"===e?n=r.default.isExisty(t.useAlpha)?"removeWhite":"colorFilter":"blendColor"===e&&(n={add:"blend",multiply:"multiply",tint:"tint"}[t.mode]),n}},{key:"_changeFilterState",value:function(e,t,n){var i=!(2<arguments.length&&void 0!==n)||n,o=this.checkedMap[t].checked,a=p[t],r=this.checkedMap[t].closest(".tui-image-editor-checkbox-group");r&&(o?r.classList.remove("tui-image-editor-disabled"):r.classList.add("tui-image-editor-disabled")),e(o,a,this._getFilterOption(t),!i)}},{key:"_getFilterOption",value:function(e){var t={};switch(e){case"removeWhite":t.color="#FFFFFF",t.useAlpha=!1,t.distance=parseFloat(this._els.removewhiteDistanceRange.value);break;case"colorFilter":t.color="#FFFFFF",t.distance=parseFloat(this._els.colorfilterThresholeRange.value);break;case"pixelate":t.blocksize=(0,u.toInteger)(this._els.pixelateRange.value);break;case"noise":t.noise=(0,u.toInteger)(this._els.noiseRange.value);break;case"brightness":t.brightness=parseFloat(this._els.brightnessRange.value);break;case"blend":t.mode="add",t.color=this._els.filterBlendColor.color,t.mode=this._els.blendType.value;break;case"multiply":t.mode="multiply",t.color=this._els.filterMultiplyColor.color;break;case"tint":t.mode="tint",t.color=this._els.filterTintColor.color,t.alpha=this._els.tintOpacity.value}return t}},{key:"_makeControlElement",value:function(){this._els={removewhiteDistanceRange:new s.default({slider:this.selector(".tie-removewhite-distance-range")},d.defaultFilterRangeValus.removewhiteDistanceRange),brightnessRange:new s.default({slider:this.selector(".tie-brightness-range")},d.defaultFilterRangeValus.brightnessRange),noiseRange:new s.default({slider:this.selector(".tie-noise-range")},d.defaultFilterRangeValus.noiseRange),pixelateRange:new s.default({slider:this.selector(".tie-pixelate-range")},d.defaultFilterRangeValus.pixelateRange),colorfilterThresholeRange:new s.default({slider:this.selector(".tie-colorfilter-threshole-range")},d.defaultFilterRangeValus.colorfilterThresholeRange),filterTintColor:new a.default(this.selector(".tie-filter-tint-color"),"#03bd9e",this.toggleDirection,this.usageStatistics),filterMultiplyColor:new a.default(this.selector(".tie-filter-multiply-color"),"#515ce6",this.toggleDirection,this.usageStatistics),filterBlendColor:new a.default(this.selector(".tie-filter-blend-color"),"#ffbb3b",this.toggleDirection,this.usageStatistics)},this._els.tintOpacity=this._pickerWithRange(this._els.filterTintColor.pickerControl),this._els.blendType=this._pickerWithSelectbox(this._els.filterBlendColor.pickerControl),this.colorPickerControls.push(this._els.filterTintColor),this.colorPickerControls.push(this._els.filterMultiplyColor),this.colorPickerControls.push(this._els.filterBlendColor)}},{key:"_pickerWithRange",value:function(e){var t=document.createElement("div"),n=document.createElement("label"),i=document.createElement("div");return i.id="tie-filter-tint-opacity",n.innerHTML="Opacity",t.appendChild(n),t.appendChild(i),e.appendChild(t),e.style.height="130px",new s.default({slider:i},d.defaultFilterRangeValus.tintOpacityRange)}},{key:"_pickerWithSelectbox",value:function(e){var t=document.createElement("div"),n=document.createElement("select"),i=document.createElement("ul");return t.className="tui-image-editor-selectlist-wrap",i.className="tui-image-editor-selectlist",t.appendChild(n),t.appendChild(i),this._makeSelectOptionList(n),e.appendChild(t),e.style.height="130px",this._drawSelectOptionList(n,i),this._pickerWithSelectboxForAddEvent(n,i),n}},{key:"_drawSelectOptionList",value:function(e,n){var t=e.querySelectorAll("option");r.default.forEach(t,function(e){var t=document.createElement("li");t.innerHTML=e.innerHTML,t.setAttribute("data-item",e.value),n.appendChild(t)})}},{key:"_pickerWithSelectboxForAddEvent",value:function(i,o){var a=this;o.addEventListener("click",function(e){var t=e.target.getAttribute("data-item"),n=document.createEvent("HTMLEvents");i.querySelector('[value="'+t+'"]').selected=!0,n.initEvent("change",!0,!0),i.dispatchEvent(n),a.selectBoxShow=!1,o.style.display="none"}),i.addEventListener("mousedown",function(e){e.preventDefault(),a.selectBoxShow=!a.selectBoxShow,o.style.display=a.selectBoxShow?"block":"none",o.setAttribute("data-selectitem",i.value),o.querySelector("[data-item='"+i.value+"']").classList.add("active")})}},{key:"_makeSelectOptionList",value:function(n){r.default.forEach(f,function(e){var t=document.createElement("option");t.setAttribute("value",e),t.innerHTML=e.replace(/^[a-z]/,function(e){return e.toUpperCase()}),n.appendChild(t)})}}]),y);function y(e,t){var n=t.locale,i=t.menuBarPosition,o=t.usageStatistics;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,y);var a=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(y.__proto__||Object.getPrototypeOf(y)).call(this,e,{locale:n,name:"filter",menuBarPosition:i,templateHtml:c.default,usageStatistics:o}));return a.selectBoxShow=!1,a.checkedMap={},a._makeControlElement(),a}t.default=b},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=e.locale;return'\n    <ul class="tui-image-editor-submenu-item">\n        <li class="tui-image-editor-submenu-align">\n            <div class="tui-image-editor-checkbox-wrap fixed-width">\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-grayscale">\n                        <span>'+t.localize("Grayscale")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-invert">\n                        <span>'+t.localize("Invert")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-sepia">\n                        <span>'+t.localize("Sepia")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-vintage">\n                        <span>'+t.localize("Sepia2")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-blur">\n                        <span>'+t.localize("Blur")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-sharpen">\n                        <span>'+t.localize("Sharpen")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-emboss">\n                        <span>'+t.localize("Emboss")+'</span>\n                    </label>\n                </div>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li class="tui-image-editor-submenu-align">\n            <div class="tui-image-editor-checkbox-group tui-image-editor-disabled" style="margin-bottom: 7px;">\n                <div class="tui-image-editor-checkbox-wrap">\n                    <div class="tui-image-editor-checkbox">\n                        <label>\n                            <input type="checkbox" class="tie-remove-white">\n                            <span>'+t.localize("Remove White")+'</span>\n                        </label>\n                    </div>\n                </div>\n                <div class="tui-image-editor-newline tui-image-editor-range-wrap short">\n                    <label>'+t.localize("Distance")+'</label>\n                    <div class="tie-removewhite-distance-range"></div>\n                </div>\n            </div>\n            <div class="tui-image-editor-checkbox-group tui-image-editor-disabled">\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-brightness">\n                        <span>'+t.localize("Brightness")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-range-wrap short">\n                    <div class="tie-brightness-range"></div>\n                </div>\n            </div>\n            <div class="tui-image-editor-checkbox-group tui-image-editor-disabled">\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-noise">\n                        <span>'+t.localize("Noise")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-range-wrap short">\n                    <div class="tie-noise-range"></div>\n                </div>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition only-left-right">\n            <div></div>\n        </li>\n        <li class="tui-image-editor-submenu-align">\n            <div class="tui-image-editor-checkbox-group tui-image-editor-disabled">\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-pixelate">\n                        <span>'+t.localize("Pixelate")+'</span>\n                    </label>\n                </div>\n                <div class="tui-image-editor-range-wrap short">\n                    <div class="tie-pixelate-range"></div>\n                </div>\n            </div>\n            <div class="tui-image-editor-checkbox-group tui-image-editor-disabled">\n                <div class="tui-image-editor-newline tui-image-editor-checkbox-wrap">\n                    <div class="tui-image-editor-checkbox">\n                        <label>\n                            <input type="checkbox" class="tie-color-filter">\n                            <span>'+t.localize("Color Filter")+'</span>\n                        </label>\n                    </div>\n                </div>\n                <div class="tui-image-editor-newline tui-image-editor-range-wrap short">\n                    <label>'+t.localize("Threshold")+'</label>\n                    <div class="tie-colorfilter-threshole-range"></div>\n                </div>\n            </div>\n        </li>\n        <li class="tui-image-editor-partition">\n            <div></div>\n        </li>\n        <li>\n            <div class="filter-color-item">\n                <div class="tie-filter-tint-color" title="'+t.localize("Tint")+'"></div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-tint">\n                        <span></span>\n                    </label>\n                </div>\n            </div>\n            <div class="filter-color-item">\n                <div class="tie-filter-multiply-color" title="'+t.localize("Multiply")+'"></div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-multiply">\n                        <span></span>\n                    </label>\n                </div>\n            </div>\n            <div class="filter-color-item">\n                <div class="tie-filter-blend-color" title="'+t.localize("Blend")+'"></div>\n                <div class="tui-image-editor-checkbox">\n                    <label>\n                        <input type="checkbox" class="tie-blend">\n                        <span></span>\n                    </label>\n                </div>\n            </div>\n        </li>\n    </ul>\n'}},function(e,t,n){"use strict";function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}Object.defineProperty(t,"__esModule",{value:!0});var o=(function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}(a,[{key:"localize",value:function(e){return this._locale[e]||e}}]),a);function a(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a),this._locale=e}t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var u=n(1),a=i(n(4)),d=i(n(108));function i(e){return e&&e.__esModule?e:{default:e}}t.default={getActions:function(){return{main:this._mainAction(),shape:this._shapeAction(),crop:this._cropAction(),flip:this._flipAction(),rotate:this._rotateAction(),text:this._textAction(),mask:this._maskAction(),draw:this._drawAction(),icon:this._iconAction(),filter:this._filterAction()}},_mainAction:function(){function n(){"crop"===o.ui.submenu&&(o.stopDrawingMode(),o.ui.changeMenu("crop"))}function e(e){var t,n;return t=e,"rotate"===o.ui.submenu&&o.ui.rotate.setRangeBarAngle("setAngle",t),n=e,"filter"===o.ui.submenu&&o.ui.filter.setFilterState(n),e}var o=this;return(0,u.extend)({initLoadImage:function(t,e){return o.loadImageFromURL(t,e).then(function(e){n(),o.ui.initializeImgUrl=t,o.ui.resizeEditor({imageSize:e}),o.clearUndoStack()})},undo:function(){o.isEmptyUndoStack()||(n(),o.deactivateAll(),o.undo().then(e))},redo:function(){o.isEmptyRedoStack()||(n(),o.deactivateAll(),o.redo().then(e))},reset:function(){n(),o.loadImageFromURL(o.ui.initializeImgUrl,"resetImage").then(function(e){n(),o.ui.resizeEditor({imageSize:e}),o.clearUndoStack()})},delete:function(){o.ui.changeHelpButtonEnabled("delete",!1),n(),o.removeActiveObject(),o.activeObjectId=null},deleteAll:function(){n(),o.clearObjects(),o.ui.changeHelpButtonEnabled("delete",!1),o.ui.changeHelpButtonEnabled("deleteAll",!1)},load:function(e){a.default.isSupportFileApi()||alert("This browser does not support file-api"),o.ui.initializeImgUrl=URL.createObjectURL(e),o.loadImageFromFile(e).then(function(e){n(),o.clearUndoStack(),o.ui.activeMenuEvent(),o.ui.resizeEditor({imageSize:e})}).catch(function(e){return Promise.reject(e)})},download:function(){var e=o.toDataURL(),t=o.getImageName(),n=void 0,i=void 0;a.default.isSupportFileApi()&&window.saveAs?(i=(n=a.default.base64ToBlob(e)).type.split("/")[1],t.split(".").pop()!==i&&(t+="."+i),saveAs(n,t)):window.open().document.body.innerHTML="<img src='"+e+"'>"}},this._commonAction())},_iconAction:function(){var o=this,n=void 0,i=void 0,a=void 0,r=void 0,s=void 0,l=void 0,c=void 0;this.on({iconCreateResize:function(e){var t=e.moveOriginPointer,n=(t.x-a)/s,i=(t.y-r)/l;o.setObjectPropertiesQuietly(c,{scaleX:Math.abs(2*n),scaleY:Math.abs(2*i)})},iconCreateEnd:function(){o.ui.icon.clearIconType(),o.changeSelectableAll(!0)}});return(0,u.extend)({changeColor:function(e){o.activeObjectId&&o.changeIconColor(o.activeObjectId,e)},addIcon:function(e,t){n=e,i=t,o.changeCursor("crosshair"),o.off("mousedown"),o.once("mousedown",function(e,t){a=t.x,r=t.y,o.addIcon(n,{left:t.x,top:t.y,fill:i}).then(function(e){c=e.id,s=e.width,l=e.height})}.bind(o))},cancelAddIcon:function(){o.off("mousedown"),o.ui.icon.clearIconType(),o.changeSelectableAll(!0),o.changeCursor("default")},registDefalutIcons:function(e,t){var n={};n[e]=t,o.registerIcons(n)},registCustomIcon:function(e,i){(new d.default).imageToSVG(e,function(e){var t=e.match(/path[^>]*d="([^"]*)"/)[1],n={};n[i.name]=t,o.registerIcons(n),o.addIcon(i.name,{left:100,top:100})},d.default.tracerDefaultOption())}},this._commonAction())},_drawAction:function(){var n=this;return(0,u.extend)({setDrawMode:function(e,t){n.stopDrawingMode(),"free"===e?n.startDrawingMode("FREE_DRAWING",t):n.startDrawingMode("LINE_DRAWING",t)},setColor:function(e){n.setBrush({color:e})}},this._commonAction())},_maskAction:function(){var n=this;return(0,u.extend)({loadImageFromURL:function(e,t){return n.loadImageFromURL(n.toDataURL(),"FilterImage").then(function(){n.addImageObject(e).then(function(){URL.revokeObjectURL(t)})})},applyFilter:function(){n.applyFilter("mask",{maskObjId:n.activeObjectId})}},this._commonAction())},_textAction:function(){var n=this;return(0,u.extend)({changeTextStyle:function(e,t){n.activeObjectId&&n.changeTextStyle(n.activeObjectId,e,t)}},this._commonAction())},_rotateAction:function(){var n=this;return(0,u.extend)({rotate:function(e,t){n.rotate(e,t),n.ui.resizeEditor(),n.ui.rotate.setRangeBarAngle("rotate",e)},setAngle:function(e,t){n.setAngle(e,t),n.ui.resizeEditor(),n.ui.rotate.setRangeBarAngle("setAngle",e)}},this._commonAction())},_shapeAction:function(){var n=this;return(0,u.extend)({changeShape:function(e,t){n.activeObjectId&&n.changeShape(n.activeObjectId,e,t)},setDrawingShape:function(e){n.setDrawingShape(e)}},this._commonAction())},_cropAction:function(){var t=this;return(0,u.extend)({crop:function(){var e=t.getCropzoneRect();e&&t.crop(e).then(function(){t.stopDrawingMode(),t.ui.resizeEditor(),t.ui.changeMenu("crop")}).catch(function(e){return Promise.reject(e)})},cancel:function(){t.stopDrawingMode(),t.ui.changeMenu("crop")},preset:function(e){switch(e){case"preset-square":t.setCropzoneRect(1);break;case"preset-3-2":t.setCropzoneRect(1.5);break;case"preset-4-3":t.setCropzoneRect(4/3);break;case"preset-5-4":t.setCropzoneRect(5/4);break;case"preset-7-5":t.setCropzoneRect(1.4);break;case"preset-16-9":t.setCropzoneRect(16/9);break;default:t.setCropzoneRect(),t.ui.crop.changeApplyButtonStatus(!1)}}},this._commonAction())},_flipAction:function(){var t=this;return(0,u.extend)({flip:function(e){return t[e]()}},this._commonAction())},_filterAction:function(){var o=this;return(0,u.extend)({applyFilter:function(e,t,n,i){e?o.applyFilter(t,n,i):o.hasFilter(t)&&o.removeFilter(t)}},this._commonAction())},setReAction:function(){var s=this;this.on({undoStackChanged:function(e){e?(s.ui.changeHelpButtonEnabled("undo",!0),s.ui.changeHelpButtonEnabled("reset",!0)):(s.ui.changeHelpButtonEnabled("undo",!1),s.ui.changeHelpButtonEnabled("reset",!1)),s.ui.resizeEditor()},redoStackChanged:function(e){e?s.ui.changeHelpButtonEnabled("redo",!0):s.ui.changeHelpButtonEnabled("redo",!1),s.ui.resizeEditor()},objectActivated:function(e){s.activeObjectId=e.id,s.ui.changeHelpButtonEnabled("delete",!0),s.ui.changeHelpButtonEnabled("deleteAll",!0),"cropzone"===e.type?s.ui.crop.changeApplyButtonStatus(!0):-1<["rect","circle","triangle"].indexOf(e.type)?(s.stopDrawingMode(),"shape"!==s.ui.submenu&&s.ui.changeMenu("shape",!1,!1),s.ui.shape.setShapeStatus({strokeColor:e.stroke,strokeWidth:e.strokeWidth,fillColor:e.fill}),s.ui.shape.setMaxStrokeValue(Math.min(e.width,e.height))):"path"===e.type||"line"===e.type?"draw"!==s.ui.submenu&&(s.ui.changeMenu("draw",!1,!1),s.ui.draw.changeStandbyMode()):-1<["i-text","text"].indexOf(e.type)?("text"!==s.ui.submenu&&s.ui.changeMenu("text",!1,!1),s.ui.text.setTextStyleStateOnAction(e)):"icon"===e.type&&(s.stopDrawingMode(),"icon"!==s.ui.submenu&&s.ui.changeMenu("icon",!1,!1),s.ui.icon.setIconPickerColor(e.fill))},addText:function(e){var t=s.ui.text,n=t.textColor,i=t.fontSize,o=t.fontStyle,a=t.fontWeight,r=t.underline;s.addText("Double Click",{position:e.originPosition,styles:{fill:n,fontSize:i,fontFamily:"Noto Sans",fontStyle:o,fontWeight:a,underline:r}}).then(function(){s.changeCursor("default")})},addObjectAfter:function(e){-1<["rect","circle","triangle"].indexOf(e.type)&&(s.ui.shape.setMaxStrokeValue(Math.min(e.width,e.height)),s.ui.shape.changeStandbyMode())},objectScaled:function(e){if(-1<["i-text","text"].indexOf(e.type))s.ui.text.fontSize=a.default.toInteger(e.fontSize);else if(0<=["rect","circle","triangle"].indexOf(e.type)){var t=e.width,n=e.height,i=s.ui.shape.getStrokeValue();t<i&&s.ui.shape.setStrokeValue(t),n<i&&s.ui.shape.setStrokeValue(n)}},selectionCleared:function(){s.activeObjectId=null,"text"===s.ui.submenu?s.changeCursor("text"):"draw"!==s.ui.submenu&&"crop"!==s.ui.submenu&&s.stopDrawingMode()}})},_commonAction:function(){var t=this;return{modeChange:function(e){switch(e){case"text":t._changeActivateMode("TEXT");break;case"crop":t.startDrawingMode("CROPPER");break;case"shape":t._changeActivateMode("SHAPE"),t.setDrawingShape(t.ui.shape.type,t.ui.shape.options)}},deactivateAll:this.deactivateAll.bind(this),changeSelectableAll:this.changeSelectableAll.bind(this),discardSelection:this.discardSelection.bind(this),stopDrawingMode:this.stopDrawingMode.bind(this)}},mixin:function(e){(0,u.extend)(e.prototype,this)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=(i(r,null,[{key:"tracerDefaultOption",value:function(){return{pathomit:100,ltres:.1,qtres:1,scale:1,strokewidth:5,viewbox:!1,linefilter:!0,desc:!1,rightangleenhance:!1,pal:[{r:0,g:0,b:0,a:255},{r:255,g:255,b:255,a:255}]}}}]),i(r,[{key:"imageToSVG",value:function(e,t,n){var i=this;n=this.checkoptions(n),this.loadImage(e,function(e){t(i.imagedataToSVG(i.getImgdata(e),n))},n)}},{key:"imagedataToSVG",value:function(e,t){t=this.checkoptions(t);var n=this.imagedataToTracedata(e,t);return this.getsvgstring(n,t)}},{key:"imageToTracedata",value:function(e,t,n){var i=this;n=this.checkoptions(n),this.loadImage(e,function(e){t(i.imagedataToTracedata(i.getImgdata(e),n))},n)}},{key:"imagedataToTracedata",value:function(e,t){t=this.checkoptions(t);var n=this.colorquantization(e,t),i=void 0;if(0===t.layering){i={layers:[],palette:n.palette,width:n.array[0].length-2,height:n.array.length-2};for(var o=0;o<n.palette.length;o+=1){var a=this.batchtracepaths(this.internodes(this.pathscan(this.layeringstep(n,o),t.pathomit),t),t.ltres,t.qtres);i.layers.push(a)}}else{var r=this.layering(n);t.layercontainerid&&this.drawLayers(r,this.specpalette,t.scale,t.layercontainerid);var s=this.batchpathscan(r,t.pathomit),l=this.batchinternodes(s,t);i={layers:this.batchtracelayers(l,t.ltres,t.qtres),palette:n.palette,width:e.width,height:e.height}}return i}},{key:"checkoptions",value:function(e){"string"==typeof(e=e||{})&&(e=e.toLowerCase(),e=this.optionpresets[e]?this.optionpresets[e]:{});for(var t=Object.keys(this.optionpresets.default),n=0;n<t.length;n+=1)e.hasOwnProperty(t[n])||(e[t[n]]=this.optionpresets.default[t[n]]);return e}},{key:"colorquantization",value:function(e,t){var n=[],i=0,o=void 0,a=void 0,r=void 0,s=[],l=e.width*e.height,c=void 0,u=void 0,d=void 0,h=void 0,f=void 0;for(u=0;u<e.height+2;u+=1)for(n[u]=[],c=0;c<e.width+2;c+=1)n[u][c]=-1;for(f=t.pal?t.pal:0===t.colorsampling?this.generatepalette(t.numberofcolors):1===t.colorsampling?this.samplepalette(t.numberofcolors,e):this.samplepalette2(t.numberofcolors,e),0<t.blurradius&&(e=this.blur(e,t.blurradius,t.blurdelta)),h=0;h<t.colorquantcycles;h+=1){if(0<h)for(d=0;d<f.length;d+=1)0<s[d].n&&(f[d]={r:Math.floor(s[d].r/s[d].n),g:Math.floor(s[d].g/s[d].n),b:Math.floor(s[d].b/s[d].n),a:Math.floor(s[d].a/s[d].n)}),s[d].n/l<t.mincolorratio&&h<t.colorquantcycles-1&&(f[d]={r:Math.floor(255*Math.random()),g:Math.floor(255*Math.random()),b:Math.floor(255*Math.random()),a:Math.floor(255*Math.random())});for(c=0;c<f.length;c+=1)s[c]={r:0,g:0,b:0,a:0,n:0};for(u=0;u<e.height;u+=1)for(c=0;c<e.width;c+=1){for(i=4*(u*e.width+c),a=1024,d=r=0;d<f.length;d+=1)(o=Math.abs(f[d].r-e.data[i])+Math.abs(f[d].g-e.data[i+1])+Math.abs(f[d].b-e.data[i+2])+Math.abs(f[d].a-e.data[i+3]))<a&&(a=o,r=d);s[r].r+=e.data[i],s[r].g+=e.data[i+1],s[r].b+=e.data[i+2],s[r].a+=e.data[i+3],s[r].n+=1,n[u+1][c+1]=r}}return{array:n,palette:f}}},{key:"samplepalette",value:function(e,t){for(var n=void 0,i=[],o=0;o<e;o+=1)n=4*Math.floor(Math.random()*t.data.length/4),i.push({r:t.data[n],g:t.data[n+1],b:t.data[n+2],a:t.data[n+3]});return i}},{key:"samplepalette2",value:function(e,t){for(var n=void 0,i=[],o=Math.ceil(Math.sqrt(e)),a=Math.ceil(e/o),r=t.width/(o+1),s=t.height/(a+1),l=0;l<a;l+=1)for(var c=0;c<o&&i.length!==e;c+=1)n=4*Math.floor((l+1)*s*t.width+(c+1)*r),i.push({r:t.data[n],g:t.data[n+1],b:t.data[n+2],a:t.data[n+3]});return i}},{key:"generatepalette",value:function(e){var t=[],n=void 0,i=void 0,o=void 0;if(e<8)for(var a=Math.floor(255/(e-1)),r=0;r<e;r+=1)t.push({r:r*a,g:r*a,b:r*a,a:255});else{var s=Math.floor(Math.pow(e,1/3)),l=Math.floor(255/(s-1)),c=e-s*s*s;for(n=0;n<s;n+=1)for(i=0;i<s;i+=1)for(o=0;o<s;o+=1)t.push({r:n*l,g:i*l,b:o*l,a:255});for(n=0;n<c;n+=1)t.push({r:Math.floor(255*Math.random()),g:Math.floor(255*Math.random()),b:Math.floor(255*Math.random()),a:Math.floor(255*Math.random())})}return t}},{key:"layering",value:function(e){var t=[],n=0,i=e.array.length,o=e.array[0].length,a=void 0,r=void 0,s=void 0,l=void 0,c=void 0,u=void 0,d=void 0,h=void 0,f=void 0,v=void 0,p=void 0;for(p=0;p<e.palette.length;p+=1)for(t[p]=[],v=0;v<i;v+=1)for(t[p][v]=[],f=0;f<o;f+=1)t[p][v][f]=0;for(v=1;v<i-1;v+=1)for(f=1;f<o-1;f+=1)n=e.array[v][f],a=e.array[v-1][f-1]===n?1:0,r=e.array[v-1][f]===n?1:0,s=e.array[v-1][f+1]===n?1:0,l=e.array[v][f-1]===n?1:0,c=e.array[v][f+1]===n?1:0,u=e.array[v+1][f-1]===n?1:0,d=e.array[v+1][f]===n?1:0,h=e.array[v+1][f+1]===n?1:0,t[n][v+1][f+1]=1+2*c+4*h+8*d,l||(t[n][v+1][f]=2+4*d+8*u),r||(t[n][v][f+1]=0+2*s+4*c+8),a||(t[n][v][f]=0+2*r+4+8*l);return t}},{key:"layeringstep",value:function(e,t){var n=[],i=e.array.length,o=e.array[0].length,a=void 0,r=void 0;for(r=0;r<i;r+=1)for(n[r]=[],a=0;a<o;a+=1)n[r][a]=0;for(r=1;r<i;r+=1)for(a=1;a<o;a+=1)n[r][a]=(e.array[r-1][a-1]===t?1:0)+(e.array[r-1][a]===t?2:0)+(e.array[r][a-1]===t?8:0)+(e.array[r][a]===t?4:0);return n}},{key:"pathscan",value:function(e,t){for(var n=[],i=0,o=0,a=0,r=0,s=e[0].length,l=e.length,c=0,u=!0,d=!1,h=void 0,f=0;f<l;f+=1)for(var v=0;v<s;v+=1)if(4===e[f][v]||11===e[f][v])for(a=v,r=f,n[i]={},n[i].points=[],n[i].boundingbox=[a,r,a,r],n[i].holechildren=[],u=!1,o=0,d=11===e[f][v],c=1;!u;){if(n[i].points[o]={},n[i].points[o].x=a-1,n[i].points[o].y=r-1,n[i].points[o].t=e[r][a],a-1<n[i].boundingbox[0]&&(n[i].boundingbox[0]=a-1),a-1>n[i].boundingbox[2]&&(n[i].boundingbox[2]=a-1),r-1<n[i].boundingbox[1]&&(n[i].boundingbox[1]=r-1),r-1>n[i].boundingbox[3]&&(n[i].boundingbox[3]=r-1),h=this.pathscan_combined_lookup[e[r][a]][c],e[r][a]=h[0],c=h[1],a+=h[2],r+=h[3],a-1===n[i].points[0].x&&r-1===n[i].points[0].y)if(u=!0,n[i].points.length<t)n.pop();else{if(n[i].isholepath=!!d,d){for(var p=0,g=[-1,-1,s+1,l+1],m=0;m<i;m++)!n[m].isholepath&&this.boundingboxincludes(n[m].boundingbox,n[i].boundingbox)&&this.boundingboxincludes(g,n[m].boundingbox)&&(g=n[p=m].boundingbox);n[p].holechildren.push(i)}i+=1}o+=1}return n}},{key:"boundingboxincludes",value:function(e,t){return e[0]<t[0]&&e[1]<t[1]&&e[2]>t[2]&&e[3]>t[3]}},{key:"batchpathscan",value:function(e,t){var n=[];for(var i in e)e.hasOwnProperty(i)&&(n[i]=this.pathscan(e[i],t));return n}},{key:"internodes",value:function(e,t){var n=[],i=0,o=0,a=0,r=0,s=0,l=void 0,c=void 0;for(l=0;l<e.length;l+=1)for(n[l]={},n[l].points=[],n[l].boundingbox=e[l].boundingbox,n[l].holechildren=e[l].holechildren,n[l].isholepath=e[l].isholepath,i=e[l].points.length,c=0;c<i;c+=1)o=(c+1)%i,a=(c+2)%i,r=(c-1+i)%i,s=(c-2+i)%i,t.rightangleenhance&&this.testrightangle(e[l],s,r,c,o,a)&&(0<n[l].points.length&&(n[l].points[n[l].points.length-1].linesegment=this.getdirection(n[l].points[n[l].points.length-1].x,n[l].points[n[l].points.length-1].y,e[l].points[c].x,e[l].points[c].y)),n[l].points.push({x:e[l].points[c].x,y:e[l].points[c].y,linesegment:this.getdirection(e[l].points[c].x,e[l].points[c].y,(e[l].points[c].x+e[l].points[o].x)/2,(e[l].points[c].y+e[l].points[o].y)/2)})),n[l].points.push({x:(e[l].points[c].x+e[l].points[o].x)/2,y:(e[l].points[c].y+e[l].points[o].y)/2,linesegment:this.getdirection((e[l].points[c].x+e[l].points[o].x)/2,(e[l].points[c].y+e[l].points[o].y)/2,(e[l].points[o].x+e[l].points[a].x)/2,(e[l].points[o].y+e[l].points[a].y)/2)});return n}},{key:"testrightangle",value:function(e,t,n,i,o,a){return e.points[i].x===e.points[t].x&&e.points[i].x===e.points[n].x&&e.points[i].y===e.points[o].y&&e.points[i].y===e.points[a].y||e.points[i].y===e.points[t].y&&e.points[i].y===e.points[n].y&&e.points[i].x===e.points[o].x&&e.points[i].x===e.points[a].x}},{key:"getdirection",value:function(e,t,n,i){return e<n?t<i?1:i<t?7:0:n<e?t<i?3:i<t?5:4:t<i?2:i<t?6:8}},{key:"batchinternodes",value:function(e,t){var n=[];for(var i in e)e.hasOwnProperty(i)&&(n[i]=this.internodes(e[i],t));return n}},{key:"tracepath",value:function(e,t,n){var i=0,o=void 0,a=void 0,r=void 0,s={segments:[]};for(s.boundingbox=e.boundingbox,s.holechildren=e.holechildren,s.isholepath=e.isholepath;i<e.points.length;){for(o=e.points[i].linesegment,a=-1,r=i+1;(e.points[r].linesegment===o||e.points[r].linesegment===a||-1===a)&&r<e.points.length-1;)e.points[r].linesegment!==o&&-1===a&&(a=e.points[r].linesegment),r+=1;r===e.points.length-1&&(r=0),s.segments=s.segments.concat(this.fitseq(e,t,n,i,r)),i=0<r?r:e.points.length}return s}},{key:"fitseq",value:function(e,t,n,i,o){if(o>e.points.length||o<0)return[];var a=i,r=0,s=!0,l=void 0,c=void 0,u=void 0,d=o-i;d<0&&(d+=e.points.length);for(var h=(e.points[o].x-e.points[i].x)/d,f=(e.points[o].y-e.points[i].y)/d,v=(i+1)%e.points.length,p=void 0;v!=o;)(p=v-i)<0&&(p+=e.points.length),l=e.points[i].x+h*p,c=e.points[i].y+f*p,t<(u=(e.points[v].x-l)*(e.points[v].x-l)+(e.points[v].y-c)*(e.points[v].y-c))&&(s=!1),r<u&&(a=v,r=u),v=(v+1)%e.points.length;if(s)return[{type:"L",x1:e.points[i].x,y1:e.points[i].y,x2:e.points[o].x,y2:e.points[o].y}];var g=a;s=!0,r=0;var m=(g-i)/d,b=(1-m)*(1-m),y=2*(1-m)*m,_=m*m,k=(b*e.points[i].x+_*e.points[o].x-e.points[g].x)/-y,E=(b*e.points[i].y+_*e.points[o].y-e.points[g].y)/-y;for(v=i+1;v!=o;)y=2*(1-(m=(v-i)/d))*m,_=m*m,l=(b=(1-m)*(1-m))*e.points[i].x+y*k+_*e.points[o].x,c=b*e.points[i].y+y*E+_*e.points[o].y,n<(u=(e.points[v].x-l)*(e.points[v].x-l)+(e.points[v].y-c)*(e.points[v].y-c))&&(s=!1),r<u&&(a=v,r=u),v=(v+1)%e.points.length;if(s)return[{type:"Q",x1:e.points[i].x,y1:e.points[i].y,x2:k,y2:E,x3:e.points[o].x,y3:e.points[o].y}];var w=g;return this.fitseq(e,t,n,i,w).concat(this.fitseq(e,t,n,w,o))}},{key:"batchtracepaths",value:function(e,t,n){var i=[];for(var o in e)e.hasOwnProperty(o)&&i.push(this.tracepath(e[o],t,n));return i}},{key:"batchtracelayers",value:function(e,t,n){var i=[];for(var o in e)e.hasOwnProperty(o)&&(i[o]=this.batchtracepaths(e[o],t,n));return i}},{key:"roundtodec",value:function(e,t){return Number(e.toFixed(t))}},{key:"svgpathstring",value:function(e,t,n,i){var o=e.layers[t],a=o[n],r="",s=void 0;if(i.linefilter&&a.segments.length<3)return r;if(r="<path "+(i.desc?'desc="l '+t+" p "+n+'" ':"")+this.tosvgcolorstr(e.palette[t],i)+'d="',-1===i.roundcoords){for(r+="M "+a.segments[0].x1*i.scale+" "+a.segments[0].y1*i.scale+" ",s=0;s<a.segments.length;s++)r+=a.segments[s].type+" "+a.segments[s].x2*i.scale+" "+a.segments[s].y2*i.scale+" ",a.segments[s].hasOwnProperty("x3")&&(r+=a.segments[s].x3*i.scale+" "+a.segments[s].y3*i.scale+" ");r+="Z "}else{for(r+="M "+this.roundtodec(a.segments[0].x1*i.scale,i.roundcoords)+" "+this.roundtodec(a.segments[0].y1*i.scale,i.roundcoords)+" ",s=0;s<a.segments.length;s++)r+=a.segments[s].type+" "+this.roundtodec(a.segments[s].x2*i.scale,i.roundcoords)+" "+this.roundtodec(a.segments[s].y2*i.scale,i.roundcoords)+" ",a.segments[s].hasOwnProperty("x3")&&(r+=this.roundtodec(a.segments[s].x3*i.scale,i.roundcoords)+" "+this.roundtodec(a.segments[s].y3*i.scale,i.roundcoords)+" ");r+="Z "}for(var l=0;l<a.holechildren.length;l++){var c=o[a.holechildren[l]];if(-1===i.roundcoords)for(c.segments[c.segments.length-1].hasOwnProperty("x3")?r+="M "+c.segments[c.segments.length-1].x3*i.scale+" "+c.segments[c.segments.length-1].y3*i.scale+" ":r+="M "+c.segments[c.segments.length-1].x2*i.scale+" "+c.segments[c.segments.length-1].y2*i.scale+" ",s=c.segments.length-1;0<=s;s--)r+=c.segments[s].type+" ",c.segments[s].hasOwnProperty("x3")&&(r+=c.segments[s].x2*i.scale+" "+c.segments[s].y2*i.scale+" "),r+=c.segments[s].x1*i.scale+" "+c.segments[s].y1*i.scale+" ";else for(c.segments[c.segments.length-1].hasOwnProperty("x3")?r+="M "+this.roundtodec(c.segments[c.segments.length-1].x3*i.scale)+" "+this.roundtodec(c.segments[c.segments.length-1].y3*i.scale)+" ":r+="M "+this.roundtodec(c.segments[c.segments.length-1].x2*i.scale)+" "+this.roundtodec(c.segments[c.segments.length-1].y2*i.scale)+" ",s=c.segments.length-1;0<=s;s--)r+=c.segments[s].type+" ",c.segments[s].hasOwnProperty("x3")&&(r+=this.roundtodec(c.segments[s].x2*i.scale)+" "+this.roundtodec(c.segments[s].y2*i.scale)+" "),r+=this.roundtodec(c.segments[s].x1*i.scale)+" "+this.roundtodec(c.segments[s].y1*i.scale)+" ";r+="Z "}if(r+='" />',i.lcpr||i.qcpr){for(s=0;s<a.segments.length;s++)a.segments[s].hasOwnProperty("x3")&&i.qcpr&&(r+='<circle cx="'+a.segments[s].x2*i.scale+'" cy="'+a.segments[s].y2*i.scale+'" r="'+i.qcpr+'" fill="cyan" stroke-width="'+.2*i.qcpr+'" stroke="black" />',r+='<circle cx="'+a.segments[s].x3*i.scale+'" cy="'+a.segments[s].y3*i.scale+'" r="'+i.qcpr+'" fill="white" stroke-width="'+.2*i.qcpr+'" stroke="black" />',r+='<line x1="'+a.segments[s].x1*i.scale+'" y1="'+a.segments[s].y1*i.scale+'" x2="'+a.segments[s].x2*i.scale+'" y2="'+a.segments[s].y2*i.scale+'" stroke-width="'+.2*i.qcpr+'" stroke="cyan" />',r+='<line x1="'+a.segments[s].x2*i.scale+'" y1="'+a.segments[s].y2*i.scale+'" x2="'+a.segments[s].x3*i.scale+'" y2="'+a.segments[s].y3*i.scale+'" stroke-width="'+.2*i.qcpr+'" stroke="cyan" />'),!a.segments[s].hasOwnProperty("x3")&&i.lcpr&&(r+='<circle cx="'+a.segments[s].x2*i.scale+'" cy="'+a.segments[s].y2*i.scale+'" r="'+i.lcpr+'" fill="white" stroke-width="'+.2*i.lcpr+'" stroke="black" />');for(l=0;l<a.holechildren.length;l++)for(c=o[a.holechildren[l]],s=0;s<c.segments.length;s++)c.segments[s].hasOwnProperty("x3")&&i.qcpr&&(r+='<circle cx="'+c.segments[s].x2*i.scale+'" cy="'+c.segments[s].y2*i.scale+'" r="'+i.qcpr+'" fill="cyan" stroke-width="'+.2*i.qcpr+'" stroke="black" />',r+='<circle cx="'+c.segments[s].x3*i.scale+'" cy="'+c.segments[s].y3*i.scale+'" r="'+i.qcpr+'" fill="white" stroke-width="'+.2*i.qcpr+'" stroke="black" />',r+='<line x1="'+c.segments[s].x1*i.scale+'" y1="'+c.segments[s].y1*i.scale+'" x2="'+c.segments[s].x2*i.scale+'" y2="'+c.segments[s].y2*i.scale+'" stroke-width="'+.2*i.qcpr+'" stroke="cyan" />',r+='<line x1="'+c.segments[s].x2*i.scale+'" y1="'+c.segments[s].y2*i.scale+'" x2="'+c.segments[s].x3*i.scale+'" y2="'+c.segments[s].y3*i.scale+'" stroke-width="'+.2*i.qcpr+'" stroke="cyan" />'),!c.segments[s].hasOwnProperty("x3")&&i.lcpr&&(r+='<circle cx="'+c.segments[s].x2*i.scale+'" cy="'+c.segments[s].y2*i.scale+'" r="'+i.lcpr+'" fill="white" stroke-width="'+.2*i.lcpr+'" stroke="black" />')}return r}},{key:"getsvgstring",value:function(e,t){t=this.checkoptions(t);for(var n=e.width*t.scale,i=e.height*t.scale,o="<svg "+(t.viewbox?'viewBox="0 0 '+n+" "+i+'" ':'width="'+n+'" height="'+i+'" ')+'version="1.1" xmlns="http://www.w3.org/2000/svg" desc="Created with imagetracer.js version '+this.versionnumber+'" >',a=0;a<e.layers.length;a+=1)for(var r=0;r<e.layers[a].length;r+=1)e.layers[a][r].isholepath||(o+=this.svgpathstring(e,a,r,t));return o+="</svg>"}},{key:"compareNumbers",value:function(e,t){return e-t}},{key:"torgbastr",value:function(e){return"rgba("+e.r+","+e.g+","+e.b+","+e.a+")"}},{key:"tosvgcolorstr",value:function(e,t){return'fill="rgb('+e.r+","+e.g+","+e.b+')" stroke="rgb('+e.r+","+e.g+","+e.b+')" stroke-width="'+t.strokewidth+'" opacity="'+e.a/255+'" '}},{key:"appendSVGString",value:function(e,t){var n=void 0;t?(n=document.getElementById(t))||((n=document.createElement("div")).id=t,document.body.appendChild(n)):(n=document.createElement("div"),document.body.appendChild(n)),n.innerHTML+=e}},{key:"blur",value:function(e,t,n){var i=void 0,o=void 0,a=void 0,r=void 0,s=void 0,l=void 0,c=void 0,u=void 0,d=void 0,h={width:e.width,height:e.height,data:[]};if((t=Math.floor(t))<1)return e;5<t&&(t=5),1024<(n=Math.abs(n))&&(n=1024);var f=this.gks[t-1];for(o=0;o<e.height;o++)for(i=0;i<e.width;i++){for(d=u=c=l=s=0,a=-t;a<t+1;a++)0<i+a&&i+a<e.width&&(r=4*(o*e.width+i+a),s+=e.data[r]*f[a+t],l+=e.data[r+1]*f[a+t],c+=e.data[r+2]*f[a+t],u+=e.data[r+3]*f[a+t],d+=f[a+t]);r=4*(o*e.width+i),h.data[r]=Math.floor(s/d),h.data[r+1]=Math.floor(l/d),h.data[r+2]=Math.floor(c/d),h.data[r+3]=Math.floor(u/d)}var v=new Uint8ClampedArray(h.data);for(o=0;o<e.height;o++)for(i=0;i<e.width;i++){for(d=u=c=l=s=0,a=-t;a<t+1;a++)0<o+a&&o+a<e.height&&(s+=v[r=4*((o+a)*e.width+i)]*f[a+t],l+=v[r+1]*f[a+t],c+=v[r+2]*f[a+t],u+=v[r+3]*f[a+t],d+=f[a+t]);r=4*(o*e.width+i),h.data[r]=Math.floor(s/d),h.data[r+1]=Math.floor(l/d),h.data[r+2]=Math.floor(c/d),h.data[r+3]=Math.floor(u/d)}for(o=0;o<e.height;o++)for(i=0;i<e.width;i++)r=4*(o*e.width+i),n<Math.abs(h.data[r]-e.data[r])+Math.abs(h.data[r+1]-e.data[r+1])+Math.abs(h.data[r+2]-e.data[r+2])+Math.abs(h.data[r+3]-e.data[r+3])&&(h.data[r]=e.data[r],h.data[r+1]=e.data[r+1],h.data[r+2]=e.data[r+2],h.data[r+3]=e.data[r+3]);return h}},{key:"loadImage",value:function(e,t,n){var i=new Image;n&&n.corsenabled&&(i.crossOrigin="Anonymous"),i.src=e,i.onload=function(){var e=document.createElement("canvas");e.width=i.width,e.height=i.height,e.getContext("2d").drawImage(i,0,0),t(e)}}},{key:"getImgdata",value:function(e){return e.getContext("2d").getImageData(0,0,e.width,e.height)}},{key:"drawLayers",value:function(e,t,n,i){n=n||1;var o=void 0,a=void 0,r=void 0,s=void 0,l=void 0,c=void 0;for(l in i?(c=document.getElementById(i))||((c=document.createElement("div")).id=i,document.body.appendChild(c)):(c=document.createElement("div"),document.body.appendChild(c)),e)if(e.hasOwnProperty(l)){o=e[l][0].length,a=e[l].length;var u=document.createElement("canvas");u.width=o*n,u.height=a*n;var d=u.getContext("2d");for(s=0;s<a;s+=1)for(r=0;r<o;r+=1)d.fillStyle=this.torgbastr(t[e[l][s][r]%t.length]),d.fillRect(r*n,s*n,n,n);c.appendChild(u)}}}]),r);function r(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),this.versionnumber="1.2.4",this.optionpresets={default:{corsenabled:!1,ltres:1,qtres:1,pathomit:8,rightangleenhance:!0,colorsampling:2,numberofcolors:16,mincolorratio:0,colorquantcycles:3,layering:0,strokewidth:1,linefilter:!1,scale:1,roundcoords:1,viewbox:!1,desc:!1,lcpr:0,qcpr:0,blurradius:0,blurdelta:20},posterized1:{colorsampling:0,numberofcolors:2},posterized2:{numberofcolors:4,blurradius:5},curvy:{ltres:.01,linefilter:!0,rightangleenhance:!1},sharp:{qtres:.01,linefilter:!1},detailed:{pathomit:0,roundcoords:2,ltres:.5,qtres:.5,numberofcolors:64},smoothed:{blurradius:5,blurdelta:64},grayscale:{colorsampling:0,colorquantcycles:1,numberofcolors:7},fixedpalette:{colorsampling:0,colorquantcycles:1,numberofcolors:27},randomsampling1:{colorsampling:1,numberofcolors:8},randomsampling2:{colorsampling:1,numberofcolors:64},artistic1:{colorsampling:0,colorquantcycles:1,pathomit:0,blurradius:5,blurdelta:64,ltres:.01,linefilter:!0,numberofcolors:16,strokewidth:2},artistic2:{qtres:.01,colorsampling:0,colorquantcycles:1,numberofcolors:4,strokewidth:0},artistic3:{qtres:10,ltres:10,numberofcolors:8},artistic4:{qtres:10,ltres:10,numberofcolors:64,blurradius:5,blurdelta:256,strokewidth:2},posterized3:{ltres:1,qtres:1,pathomit:20,rightangleenhance:!0,colorsampling:0,numberofcolors:3,mincolorratio:0,colorquantcycles:3,blurradius:3,blurdelta:20,strokewidth:0,linefilter:!1,roundcoords:1,pal:[{r:0,g:0,b:100,a:255},{r:255,g:255,b:255,a:255}]}},this.pathscan_combined_lookup=[[[-1,-1,-1,-1],[-1,-1,-1,-1],[-1,-1,-1,-1],[-1,-1,-1,-1]],[[0,1,0,-1],[-1,-1,-1,-1],[-1,-1,-1,-1],[0,2,-1,0]],[[-1,-1,-1,-1],[-1,-1,-1,-1],[0,1,0,-1],[0,0,1,0]],[[0,0,1,0],[-1,-1,-1,-1],[0,2,-1,0],[-1,-1,-1,-1]],[[-1,-1,-1,-1],[0,0,1,0],[0,3,0,1],[-1,-1,-1,-1]],[[13,3,0,1],[13,2,-1,0],[7,1,0,-1],[7,0,1,0]],[[-1,-1,-1,-1],[0,1,0,-1],[-1,-1,-1,-1],[0,3,0,1]],[[0,3,0,1],[0,2,-1,0],[-1,-1,-1,-1],[-1,-1,-1,-1]],[[0,3,0,1],[0,2,-1,0],[-1,-1,-1,-1],[-1,-1,-1,-1]],[[-1,-1,-1,-1],[0,1,0,-1],[-1,-1,-1,-1],[0,3,0,1]],[[11,1,0,-1],[14,0,1,0],[14,3,0,1],[11,2,-1,0]],[[-1,-1,-1,-1],[0,0,1,0],[0,3,0,1],[-1,-1,-1,-1]],[[0,0,1,0],[-1,-1,-1,-1],[0,2,-1,0],[-1,-1,-1,-1]],[[-1,-1,-1,-1],[-1,-1,-1,-1],[0,1,0,-1],[0,0,1,0]],[[0,1,0,-1],[-1,-1,-1,-1],[-1,-1,-1,-1],[0,2,-1,0]],[[-1,-1,-1,-1],[-1,-1,-1,-1],[-1,-1,-1,-1],[-1,-1,-1,-1]]],this.gks=[[.27901,.44198,.27901],[.135336,.228569,.272192,.228569,.135336],[.086776,.136394,.178908,.195843,.178908,.136394,.086776],[.063327,.093095,.122589,.144599,.152781,.144599,.122589,.093095,.063327],[.049692,.069304,.089767,.107988,.120651,.125194,.120651,.107988,.089767,.069304,.049692]],this.specpalette=[{r:0,g:0,b:0,a:255},{r:128,g:128,b:128,a:255},{r:0,g:0,b:128,a:255},{r:64,g:64,b:128,a:255},{r:192,g:192,b:192,a:255},{r:255,g:255,b:255,a:255},{r:128,g:128,b:192,a:255},{r:0,g:0,b:192,a:255},{r:128,g:0,b:0,a:255},{r:128,g:64,b:64,a:255},{r:128,g:0,b:128,a:255},{r:168,g:168,b:168,a:255},{r:192,g:128,b:128,a:255},{r:192,g:0,b:0,a:255},{r:255,g:255,b:255,a:255},{r:0,g:128,b:0,a:255}]}t.default=a},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var h=C(n(1)),a=C(n(2)),r=C(n(5)),s=C(n(110)),l=C(n(111)),c=C(n(113)),u=C(n(114)),d=C(n(115)),f=C(n(116)),v=C(n(117)),p=C(n(118)),g=C(n(119)),m=C(n(125)),b=C(n(127)),y=C(n(128)),_=C(n(129)),k=C(n(130)),E=C(n(131)),w=C(n(0)),x=C(n(4));function C(e){return e&&e.__esModule?e:{default:e}}var O=w.default.componentNames,S=w.default.eventNames,M=w.default.drawingModes,j=w.default.fObjectOptions,T=h.default.extend,P=h.default.stamp,I=h.default.isArray,A=h.default.isString,R=h.default.forEachArray,D=h.default.forEachOwnProperties,L=h.default.CustomEvents,z={cssOnly:!0},H={backstoreOnly:!0},B=(i(F,[{key:"destroy",value:function(){var e=this._canvas.wrapperEl;this._canvas.clear(),e.parentNode.removeChild(e)}},{key:"deactivateAll",value:function(){return this._canvas.discardActiveObject(),this}},{key:"renderAll",value:function(){return this._canvas.renderAll(),this}},{key:"add",value:function(e){var t,n=[];I(e)?n=e:n.push(e),(t=this._canvas).add.apply(t,n)}},{key:"contains",value:function(e){return this._canvas.contains(e)}},{key:"getObjects",value:function(){return this._canvas.getObjects().slice()}},{key:"getObject",value:function(e){return this._objects[e]}},{key:"remove",value:function(e){this._canvas.remove(e)}},{key:"removeAll",value:function(e){var t=this._canvas,n=t.getObjects().slice();return t.remove.apply(t,this._canvas.getObjects()),e&&t.clear(),n}},{key:"removeObjectById",value:function(e){var t=[],n=this._canvas,i=this.getObject(e);return i&&i.isType("group")&&!i.isEmpty()?(n.discardActiveObject(),i.forEachObject(function(e){t.push(e),n.remove(e)})):n.contains(i)&&(t.push(i),n.remove(i)),t}},{key:"getObjectId",value:function(e){var t=null;for(t in this._objects)if(this._objects.hasOwnProperty(t)&&e===this._objects[t])return t;return null}},{key:"getActiveObject",value:function(){return this._canvas._activeObject}},{key:"getActiveObjectIdForRemove",value:function(){var e=this.getActiveObject();if("activeSelection"!==e.type)return this.getObjectId(e);var t=new r.default.Group;return t.add.apply(t,e.getObjects()),this._addFabricObject(t)}},{key:"isReadyRemoveObject",value:function(){var e=this.getActiveObject();return e&&!e.isEditing}},{key:"getActiveObjects",value:function(){var e=this._canvas._activeObject;return e&&"activeSelection"===e.type?e:null}},{key:"getActiveSelectionFromObjects",value:function(e){var t=this.getCanvas();return new r.default.ActiveSelection(e,{canvas:t})}},{key:"setActiveObject",value:function(e){this._canvas.setActiveObject(e)}},{key:"setCropSelectionStyle",value:function(e){this.cropSelectionStyle=e}},{key:"getComponent",value:function(e){return this._componentMap[e]}},{key:"getDrawingMode",value:function(){return this._drawingMode}},{key:"startDrawingMode",value:function(e,t){if(this._isSameDrawingMode(e))return!0;this.stopDrawingMode();var n=this._getDrawingModeInstance(e);return n&&n.start&&(n.start(this,t),this._drawingMode=e),!!n}},{key:"stopDrawingMode",value:function(){if(!this._isSameDrawingMode(M.NORMAL)){var e=this._getDrawingModeInstance(this.getDrawingMode());e&&e.end&&e.end(this),this._drawingMode=M.NORMAL}}},{key:"toDataURL",value:function(e){var t=this.getComponent(O.CROPPER);t.changeVisibility(!1);var n=this._canvas&&this._canvas.toDataURL(e);return t.changeVisibility(!0),n}},{key:"setCanvasImage",value:function(e,t){t&&P(t),this.imageName=e,this.canvasImage=t}},{key:"setCssMaxDimension",value:function(e){this.cssMaxWidth=e.width||this.cssMaxWidth,this.cssMaxHeight=e.height||this.cssMaxHeight}},{key:"adjustCanvasDimension",value:function(){var e=this.canvasImage.scale(1),t=e.getBoundingRect(),n=t.width,i=t.height,o=this._calcMaxDimension(n,i);this.setCanvasCssDimension({width:"100%",height:"100%","max-width":o.width+"px","max-height":o.height+"px"}),this.setCanvasBackstoreDimension({width:n,height:i}),this._canvas.centerObject(e)}},{key:"setCanvasCssDimension",value:function(e){this._canvas.setDimensions(e,z)}},{key:"setCanvasBackstoreDimension",value:function(e){this._canvas.setDimensions(e,H)}},{key:"setImageProperties",value:function(e,t){var n=this.canvasImage;n&&(n.set(e).setCoords(),t&&this._canvas.renderAll())}},{key:"getCanvasElement",value:function(){return this._canvas.getElement()}},{key:"getCanvas",value:function(){return this._canvas}},{key:"getCanvasImage",value:function(){return this.canvasImage}},{key:"getImageName",value:function(){return this.imageName}},{key:"addImageObject",value:function(e){var n=this,i=this._callbackAfterLoadingImageObject.bind(this);return new a.default(function(t){r.default.Image.fromURL(e,function(e){i(e),t(n.createObjectProperties(e))},{crossOrigin:"Anonymous"})})}},{key:"getCenter",value:function(){return this._canvas.getCenter()}},{key:"getCropzoneRect",value:function(){return this.getComponent(O.CROPPER).getCropzoneRect()}},{key:"setCropzoneRect",value:function(e){this.getComponent(O.CROPPER).setCropzoneRect(e)}},{key:"getCroppedImageData",value:function(e){return this.getComponent(O.CROPPER).getCroppedImageData(e)}},{key:"setBrush",value:function(e){var t=this._drawingMode,n=O.FREE_DRAWING;t===M.LINE&&(n=M.LINE),this.getComponent(n).setBrush(e)}},{key:"setDrawingShape",value:function(e,t){this.getComponent(O.SHAPE).setStates(e,t)}},{key:"registerPaths",value:function(e){this.getComponent(O.ICON).registerPaths(e)}},{key:"changeCursor",value:function(e){var t=this.getCanvas();t.defaultCursor=e,t.renderAll()}},{key:"hasFilter",value:function(e){return this.getComponent(O.FILTER).hasFilter(e)}},{key:"setSelectionStyle",value:function(e){T(j.SELECTION_STYLE,e)}},{key:"setObjectProperties",value:function(e,t){var n=this.getObject(e),i=T({},t);return n.set(i),n.setCoords(),this.getCanvas().renderAll(),i}},{key:"getObjectProperties",value:function(e,t){var n=this.getObject(e),i={};return A(t)?i[t]=n[t]:I(t)?R(t,function(e){i[e]=n[e]}):D(t,function(e,t){i[t]=n[t]}),i}},{key:"getObjectPosition",value:function(e,t,n){var i=this.getObject(e);return i?i.getPointByOrigin(t,n):null}},{key:"setObjectPosition",value:function(e,t){var n=this.getObject(e),i=t.x,o=t.y,a=t.originX,r=t.originY;if(!n)return!1;var s=n.getPointByOrigin(a,r),l=n.getPointByOrigin("center","center"),c=l.x-s.x,u=l.y-s.y;return n.set({left:i+c,top:o+u}),n.setCoords(),!0}},{key:"getCanvasSize",value:function(){var e=this.getCanvasImage();return{width:e?e.width:0,height:e?e.height:0}}},{key:"_getDrawingModeInstance",value:function(e){return this._drawingModeMap[e]}},{key:"_setObjectCachingToFalse",value:function(){r.default.Object.prototype.objectCaching=!1}},{key:"_setCanvasElement",value:function(e){var t=void 0,n=void 0;"CANVAS"!==(t=e.nodeType?e:document.querySelector(e)).nodeName.toUpperCase()&&(n=document.createElement("canvas"),t.appendChild(n)),this._canvas=new r.default.Canvas(n,{containerClass:"tui-image-editor-canvas-container",enableRetinaScaling:!1})}},{key:"_createDrawingModeInstances",value:function(){this._register(this._drawingModeMap,new b.default),this._register(this._drawingModeMap,new y.default),this._register(this._drawingModeMap,new _.default),this._register(this._drawingModeMap,new k.default),this._register(this._drawingModeMap,new E.default)}},{key:"_createComponents",value:function(){this._register(this._componentMap,new s.default(this)),this._register(this._componentMap,new l.default(this)),this._register(this._componentMap,new c.default(this)),this._register(this._componentMap,new u.default(this)),this._register(this._componentMap,new d.default(this)),this._register(this._componentMap,new f.default(this)),this._register(this._componentMap,new v.default(this)),this._register(this._componentMap,new p.default(this)),this._register(this._componentMap,new g.default(this)),this._register(this._componentMap,new m.default(this))}},{key:"_register",value:function(e,t){e[t.getName()]=t}},{key:"_isSameDrawingMode",value:function(e){return this.getDrawingMode()===e}},{key:"_calcMaxDimension",value:function(e,t){var n=this.cssMaxWidth/e,i=this.cssMaxHeight/t,o=Math.min(e,this.cssMaxWidth),a=Math.min(t,this.cssMaxHeight);return n<1&&n<i?(o=e*n,a=t*n):i<1&&i<n&&(o=e*i,a=t*i),{width:Math.floor(o),height:Math.floor(a)}}},{key:"_callbackAfterLoadingImageObject",value:function(e){var t=this.getCanvasImage().getCenterPoint();e.set(w.default.fObjectOptions.SELECTION_STYLE),e.set({left:t.x,top:t.y,crossOrigin:"Anonymous"}),this.getCanvas().add(e).setActiveObject(e)}},{key:"_attachCanvasEvents",value:function(){var e=this._canvas,t=this._handler;e.on({"mouse:down":t.onMouseDown,"object:added":t.onObjectAdded,"object:removed":t.onObjectRemoved,"object:moving":t.onObjectMoved,"object:scaling":t.onObjectScaled,"object:rotating":t.onObjectRotated,"object:selected":t.onObjectSelected,"path:created":t.onPathCreated,"selection:cleared":t.onSelectionCleared,"selection:created":t.onSelectionCreated,"selection:updated":t.onObjectSelected})}},{key:"_onMouseDown",value:function(e){var t=this._canvas.getPointer(e.e);this.fire(S.MOUSE_DOWN,e.e,t)}},{key:"_onObjectAdded",value:function(e){var t=e.target;t.isType("cropzone")||this._addFabricObject(t)}},{key:"_onObjectRemoved",value:function(e){var t=e.target;this._removeFabricObject(P(t))}},{key:"_onObjectMoved",value:function(e){var t=this;this._lazyFire(S.OBJECT_MOVED,function(e){return t.createObjectProperties(e)},e.target)}},{key:"_onObjectScaled",value:function(e){var t=this;this._lazyFire(S.OBJECT_SCALED,function(e){return t.createObjectProperties(e)},e.target)}},{key:"_onObjectRotated",value:function(e){var t=this;this._lazyFire(S.OBJECT_ROTATED,function(e){return t.createObjectProperties(e)},e.target)}},{key:"_lazyFire",value:function(t,n,e){var i=this,o=e&&e.canvasEventDelegation?e.canvasEventDelegation(t):"none";"unregisted"===o&&e.canvasEventRegister(t,function(e){i.fire(t,n(e))}),"none"===o&&this.fire(t,n(e))}},{key:"_onObjectSelected",value:function(e){var t=e.target,n=this.createObjectProperties(t);this.fire(S.OBJECT_ACTIVATED,n)}},{key:"_onPathCreated",value:function(e){var t=e.path.getCenterPoint(),n=t.x,i=t.y;e.path.set(T({left:n,top:i},w.default.fObjectOptions.SELECTION_STYLE));var o=this.createObjectProperties(e.path);this.fire(S.ADD_OBJECT,o)}},{key:"_onSelectionCleared",value:function(){this.fire(S.SELECTION_CLEARED)}},{key:"_onSelectionCreated",value:function(e){this.fire(S.SELECTION_CREATED,e.target)}},{key:"discardSelection",value:function(){this._canvas.discardActiveObject(),this._canvas.renderAll()}},{key:"changeSelectableAll",value:function(t){this._canvas.forEachObject(function(e){e.selectable=t,e.hoverCursor=t?"move":"crosshair"})}},{key:"createObjectProperties",value:function(e){var t={id:P(e),type:e.type};return T(t,x.default.getProperties(e,["left","top","width","height","fill","stroke","strokeWidth","opacity","angle"])),-1<["i-text","text"].indexOf(e.type)&&T(t,this._createTextProperties(e,t)),t}},{key:"_createTextProperties",value:function(e){var t={};return T(t,x.default.getProperties(e,["text","fontFamily","fontSize","fontStyle","textAlign","textDecoration","fontWeight"])),t}},{key:"_addFabricObject",value:function(e){var t=P(e);return this._objects[t]=e,t}},{key:"_removeFabricObject",value:function(e){delete this._objects[e]}},{key:"resetTargetObjectForCopyPaste",value:function(){var e=this.getActiveObject();e&&(this.targetObjectForCopyPaste=e)}},{key:"pasteObject",value:function(){var t=this;if(!this.targetObjectForCopyPaste)return a.default.resolve([]);var e=this.targetObjectForCopyPaste,n="activeSelection"===e.type?e.getObjects():[e],i=null;return this.discardSelection(),this._cloneObject(n).then(function(e){i=1<e.length?t.getActiveSelectionFromObjects(e):e[0],t.targetObjectForCopyPaste=i,t.setActiveObject(i)})}},{key:"_cloneObject",value:function(e){var t=this,n=h.default.map(e,function(e){return t._cloneObjectItem(e)});return a.default.all(n)}},{key:"_cloneObjectItem",value:function(e){var n=this;return this._copyFabricObjectForPaste(e).then(function(e){var t=n.createObjectProperties(e);return n.add(e),n.fire(S.ADD_OBJECT,t),e})}},{key:"_copyFabricObjectForPaste",value:function(e){function u(e,t){return t?e-10:e+10}var d=this;return this._copyFabricObject(e).then(function(e){var t=e.left,n=e.top,i=e.width,o=e.height,a=d.getCanvasSize(),r=a.width,s=a.height,l=t+i/2,c=n+o/2;return e.set(h.default.extend({left:u(t,r<l+10),top:u(n,s<c+10)},w.default.fObjectOptions.SELECTION_STYLE)),e})}},{key:"_copyFabricObject",value:function(e){return new a.default(function(t){e.clone(function(e){t(e)})})}}]),F);function F(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=t.cssMaxWidth,i=t.cssMaxHeight,o=t.useItext,a=void 0!==o&&o,r=t.useDragAddIcon,s=void 0!==r&&r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,F),this.canvasImage=null,this.cssMaxWidth=n||1e3,this.cssMaxHeight=i||800,this.useItext=a,this.useDragAddIcon=s,this.cropSelectionStyle={},this.targetObjectForCopyPaste=null,this.imageName="",this._objects={},this._canvas=null,this._drawingMode=M.NORMAL,this._drawingModeMap={},this._componentMap={},this._handler={onMouseDown:this._onMouseDown.bind(this),onObjectAdded:this._onObjectAdded.bind(this),onObjectRemoved:this._onObjectRemoved.bind(this),onObjectMoved:this._onObjectMoved.bind(this),onObjectScaled:this._onObjectScaled.bind(this),onObjectRotated:this._onObjectRotated.bind(this),onObjectSelected:this._onObjectSelected.bind(this),onPathCreated:this._onPathCreated.bind(this),onSelectionCleared:this._onSelectionCleared.bind(this),onSelectionCreated:this._onSelectionCreated.bind(this)},this._setObjectCachingToFalse(),this._setCanvasElement(e),this._createDrawingModeInstances(),this._createComponents(),this._attachCanvasEvents()}L.mixin(B),e.exports=B},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(n(2)),r=l(n(8)),s=l(n(0));function l(e){return e&&e.__esModule?e:{default:e}}var c=s.default.componentNames,u=s.default.rejectMessages,d={padding:0,crossOrigin:"Anonymous"},h=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,r.default),i(f,[{key:"load",value:function(t,e){var n=this,i=void 0;if(t||e)i=this._setBackgroundImage(e).then(function(e){return n.setCanvasImage(t,e),n.adjustCanvasDimension(),e});else{var o=this.getCanvas();o.backgroundImage=null,o.renderAll(),i=new a.default(function(e){n.setCanvasImage("",null),e()})}return i}},{key:"_setBackgroundImage",value:function(e){var o=this;return e?new a.default(function(t,n){var i=o.getCanvas();i.setBackgroundImage(e,function(){var e=i.backgroundImage;e&&e.getElement()?t(e):n(u.loadingImageFailed)},d)}):a.default.reject(u.loadImage)}}]),f);function f(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,c.IMAGE_LOADER,e))}e.exports=h},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var h=c(n(1)),a=c(n(5)),r=c(n(8)),s=c(n(112)),l=n(0),f=n(4);function c(e){return e&&e.__esModule?e:{default:e}}var u={presetRatio:null,top:-10,left:-10,height:1,width:1},d=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(v,r.default),i(v,[{key:"start",value:function(){if(!this._cropzone){var e=this.getCanvas();e.forEachObject(function(e){e.evented=!1}),this._cropzone=new s.default(e,h.default.extend({left:0,top:0,width:.5,height:.5,strokeWidth:0,cornerSize:10,cornerColor:"black",fill:"transparent"},l.CROPZONE_DEFAULT_OPTIONS,this.graphics.cropSelectionStyle)),e.discardActiveObject(),e.add(this._cropzone),e.on("mouse:down",this._listeners.mousedown),e.selection=!1,e.defaultCursor="crosshair",a.default.util.addListener(document,"keydown",this._listeners.keydown),a.default.util.addListener(document,"keyup",this._listeners.keyup)}}},{key:"end",value:function(){var e=this.getCanvas(),t=this._cropzone;t&&(e.remove(t),e.selection=!0,e.defaultCursor="default",e.off("mouse:down",this._listeners.mousedown),e.forEachObject(function(e){e.evented=!0}),this._cropzone=null,a.default.util.removeListener(document,"keydown",this._listeners.keydown),a.default.util.removeListener(document,"keyup",this._listeners.keyup))}},{key:"changeVisibility",value:function(e){this._cropzone&&this._cropzone.set({visible:e})}},{key:"_onFabricMouseDown",value:function(e){var t=this.getCanvas();if(!e.target){t.selection=!1;var n=t.getPointer(e.e);this._startX=n.x,this._startY=n.y,t.on({"mouse:move":this._listeners.mousemove,"mouse:up":this._listeners.mouseup})}}},{key:"_onFabricMouseMove",value:function(e){var t=this.getCanvas(),n=t.getPointer(e.e),i=n.x,o=n.y,a=this._cropzone;10<Math.abs(i-this._startX)+Math.abs(o-this._startY)&&(t.remove(a),a.set(this._calcRectDimensionFromPoint(i,o)),t.add(a),t.setActiveObject(a))}},{key:"_calcRectDimensionFromPoint",value:function(e,t){var n=this.getCanvas(),i=n.getWidth(),o=n.getHeight(),a=this._startX,r=this._startY,s=(0,f.clamp)(e,0,a),l=(0,f.clamp)(t,0,r),c=(0,f.clamp)(e,a,i)-s,u=(0,f.clamp)(t,r,o)-l;return this._withShiftKey&&(u<c?u=c:c<u&&(c=u),e<=a&&(s=a-c),t<=r&&(l=r-u)),{left:s,top:l,width:c,height:u}}},{key:"_onFabricMouseUp",value:function(){var e=this._cropzone,t=this._listeners,n=this.getCanvas();n.setActiveObject(e),n.off({"mouse:move":t.mousemove,"mouse:up":t.mouseup})}},{key:"getCroppedImageData",value:function(e){var t=this.getCanvas(),n=t.contains(this._cropzone);if(!e)return null;n&&t.remove(this._cropzone);var i={imageName:this.getImageName(),url:t.toDataURL(e)};return n&&t.add(this._cropzone),i}},{key:"getCropzoneRect",value:function(){var e=this._cropzone;return e.isValid()?{left:e.left,top:e.top,width:e.width,height:e.height}:null}},{key:"setCropzoneRect",value:function(e){var t=this.getCanvas(),n=this._cropzone;t.discardActiveObject(),t.selection=!1,t.remove(n),n.set(e?this._getPresetPropertiesForCropSize(e):u),t.add(n),t.selection=!0,e&&t.setActiveObject(n)}},{key:"_getPresetPropertiesForCropSize",value:function(e){function t(e,t){return t<e?t/e:1}var n=this.getCanvas(),i=n.getWidth(),o=n.getHeight(),a=o<=i?i:o,r=a*e,s=a,l=t(r,i),c=h.default.map([r,s],function(e){return e*l});r=c[0];var u=t(s=c[1],o),d=h.default.map([r,s],function(e){return(0,f.fixFloatingPoint)(e*u)});return r=d[0],{presetRatio:e,top:(o-(s=d[1]))/2,left:(i-r)/2,width:r,height:s}}},{key:"_onKeyDown",value:function(e){e.keyCode===l.keyCodes.SHIFT&&(this._withShiftKey=!0)}},{key:"_onKeyUp",value:function(e){e.keyCode===l.keyCodes.SHIFT&&(this._withShiftKey=!1)}}]),v);function v(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,v);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(v.__proto__||Object.getPrototypeOf(v)).call(this,l.componentNames.CROPPER,e));return t._cropzone=null,t._startX=null,t._startY=null,t._withShiftKey=!1,t._listeners={keydown:t._onKeyDown.bind(t),keyup:t._onKeyUp.bind(t),mousedown:t._onFabricMouseDown.bind(t),mousemove:t._onFabricMouseMove.bind(t),mouseup:t._onFabricMouseUp.bind(t)},t}e.exports=d},function(e,t,n){"use strict";var c=o(n(1)),i=o(n(5)),u=n(4),r=n(0);function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(){}var l=["tl","tr","mt","ml","mr","mb","bl","br"];function v(e,t){return t<e?"width":"height"}var d=i.default.util.createClass(i.default.Rect,{initialize:function(e,t,n){(t=c.default.extend(t,n)).type="cropzone",this.callSuper("initialize",t),this._addEventHandler(),this.canvas=e,this.options=t},canvasEventDelegation:function(e){var t="unregisted";return this.canvasEventTrigger[e]!==s?t="registed":[r.eventNames.OBJECT_MOVED,r.eventNames.OBJECT_SCALED].indexOf(e)<0&&(t="none"),t},canvasEventRegister:function(e,t){this.canvasEventTrigger[e]=t},_addEventHandler:function(){var e;this.canvasEventTrigger=(a(e={},r.eventNames.OBJECT_MOVED,s),a(e,r.eventNames.OBJECT_SCALED,s),e),this.on({moving:this._onMoving.bind(this),scaling:this._onScaling.bind(this)})},_renderCropzone:function(e){var t=this.flipX?-1:1,n=this.flipY?-1:1,i=t/this.scaleX,o=n/this.scaleY;e.scale(i,o),this._fillOuterRect(e,"rgba(0, 0, 0, 0.5)"),this.options.lineWidth?(this._fillInnerRect(e),this._strokeBorder(e,"rgb(255, 255, 255)",{lineWidth:this.options.lineWidth})):(this._strokeBorder(e,"rgb(0, 0, 0)",{lineDashWidth:7}),this._strokeBorder(e,"rgb(255, 255, 255)",{lineDashWidth:7,lineDashOffset:7})),e.scale(1/i,1/o)},_render:function(e){this.callSuper("_render",e),this._renderCropzone(e)},_fillOuterRect:function(e,t){var n=this._getCoordinates(),i=n.x,o=n.y;e.save(),e.fillStyle=t,e.beginPath(),e.moveTo(i[0]-1,o[0]-1),e.lineTo(i[3]+1,o[0]-1),e.lineTo(i[3]+1,o[3]+1),e.lineTo(i[0]-1,o[3]+1),e.lineTo(i[0]-1,o[0]-1),e.closePath(),e.moveTo(i[1],o[1]),e.lineTo(i[1],o[2]),e.lineTo(i[2],o[2]),e.lineTo(i[2],o[1]),e.lineTo(i[1],o[1]),e.closePath(),e.fill(),e.restore()},_fillInnerRect:function(e){var t=this._getCoordinates(),n=t.x,i=t.y,o=this._caculateInnerPosition(n,(n[2]-n[1])/3),a=this._caculateInnerPosition(i,(i[2]-i[1])/3);e.save(),e.strokeStyle="rgba(255, 255, 255, 0.7)",e.lineWidth=this.options.lineWidth,e.beginPath(),e.moveTo(o[0],a[1]),e.lineTo(o[3],a[1]),e.moveTo(o[0],a[2]),e.lineTo(o[3],a[2]),e.moveTo(o[1],a[0]),e.lineTo(o[1],a[3]),e.moveTo(o[2],a[0]),e.lineTo(o[2],a[3]),e.stroke(),e.closePath(),e.restore()},_caculateInnerPosition:function(e,t){var n=[];return n[0]=e[1],n[1]=e[1]+t,n[2]=e[1]+2*t,n[3]=e[2],n},_getCoordinates:function(){var e=this.canvas,t=this.width,n=this.height,i=this.left,o=this.top,a=t/2,r=n/2,s=e.getHeight(),l=e.getWidth();return{x:c.default.map([-(a+i),-a,a,l-i-t+a],Math.ceil),y:c.default.map([-(r+o),-r,r,s-o-n+r],Math.ceil)}},_strokeBorder:function(e,t,n){var i=n.lineDashWidth,o=n.lineDashOffset,a=n.lineWidth,r=this.width/2,s=this.height/2;e.save(),e.strokeStyle=t,e.setLineDash&&e.setLineDash([i,i]),o&&(e.lineDashOffset=o),a&&(e.lineWidth=a),e.beginPath(),e.moveTo(-r,-s),e.lineTo(r,-s),e.lineTo(r,s),e.lineTo(-r,s),e.lineTo(-r,-s),e.stroke(),e.restore()},_onMoving:function(){var e=this.height,t=this.width,n=this.left,i=this.top,o=this.canvas.getWidth()-t,a=this.canvas.getHeight()-e;this.left=(0,u.clamp)(n,0,o),this.top=(0,u.clamp)(i,0,a),this.canvasEventTrigger[r.eventNames.OBJECT_MOVED](this)},_onScaling:function(e){var t=e.transform.corner,n=this.canvas.getPointer(e.e),i=this._calcScalingSizeFromPointer(n,t);this.scale(1).set(i),this.canvasEventTrigger[r.eventNames.OBJECT_SCALED](this)},_calcScalingSizeFromPointer:function(e,t){var n;return(n=t,0<=l.indexOf(n))&&this._resizeCropZone(e,t)},adjustRatioCropzoneSize:function(e){var t=e.width,n=e.height,i=e.leftMaker,o=e.topMaker,a=e.maxWidth,r=e.maxHeight,s=e.scaleTo;if(t=a?(0,u.clamp)(t,1,a):t,n=r?(0,u.clamp)(n,1,r):n,!this.presetRatio)return{width:t,height:n,left:i(t),top:o(n)};"width"===s?n=t/this.presetRatio:t=n*this.presetRatio;var l=Math.min(a/t,r/n);if(l<=1){var c=[t,n].map(function(e){return e*l});t=c[0],n=c[1]}return{width:t,height:n,left:i(t),top:o(n)}},_getCropzoneRectInfo:function(){var e=this.canvas,t=e.width,n=e.height,i=this.getBoundingRect(!1,!0),o=i.top,a=i.left,r=i.width,s=i.height;return{rectTop:o,rectLeft:a,rectWidth:r,rectHeight:s,rectRight:a+r,rectBottom:o+s,canvasWidth:t,canvasHeight:n}},_resizeCropZone:function(e,t){var n=e.x,i=e.y,o=this._getCropzoneRectInfo(),a=o.rectWidth,r=o.rectHeight,s=o.rectTop,l=o.rectLeft,c=o.rectBottom,u=o.rectRight,d=o.canvasWidth,h=o.canvasHeight,f={tl:{width:u-n,height:c-i,leftMaker:function(e){return u-e},topMaker:function(e){return c-e},maxWidth:u,maxHeight:c,scaleTo:v(l-n,s-i)},tr:{width:n-l,height:c-i,leftMaker:function(){return l},topMaker:function(e){return c-e},maxWidth:d-l,maxHeight:c,scaleTo:v(n-u,s-i)},mt:{width:a,height:c-i,leftMaker:function(){return l},topMaker:function(e){return c-e},maxWidth:d-l,maxHeight:c,scaleTo:"height"},ml:{width:u-n,height:r,leftMaker:function(e){return u-e},topMaker:function(){return s},maxWidth:u,maxHeight:h-s,scaleTo:"width"},mr:{width:n-l,height:r,leftMaker:function(){return l},topMaker:function(){return s},maxWidth:d-l,maxHeight:h-s,scaleTo:"width"},mb:{width:a,height:i-s,leftMaker:function(){return l},topMaker:function(){return s},maxWidth:d-l,maxHeight:h-s,scaleTo:"height"},bl:{width:u-n,height:i-s,leftMaker:function(e){return u-e},topMaker:function(){return s},maxWidth:u,maxHeight:h-s,scaleTo:v(l-n,i-c)},br:{width:n-l,height:i-s,leftMaker:function(){return l},topMaker:function(){return s},maxWidth:d-l,maxHeight:h-s,scaleTo:v(n-u,i-c)}};return this.adjustRatioCropzoneSize(f[t])},isValid:function(){return 0<=this.left&&0<=this.top&&0<this.width&&0<this.height}});e.exports=d},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=c(n(1)),r=c(n(2)),s=c(n(8)),l=c(n(0));function c(e){return e&&e.__esModule?e:{default:e}}var u=l.default.componentNames,d=l.default.rejectMessages,h=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(f,s.default),i(f,[{key:"getCurrentSetting",value:function(){var e=this.getCanvasImage();return{flipX:e.flipX,flipY:e.flipY}}},{key:"set",value:function(e){var t=this.getCurrentSetting(),n=t.flipX!==e.flipX,i=t.flipY!==e.flipY;return n||i?(a.default.extend(t,e),this.setImageProperties(t,!0),this._invertAngle(n,i),this._flipObjects(n,i),r.default.resolve({flipX:t.flipX,flipY:t.flipY,angle:this.getCanvasImage().angle})):r.default.reject(d.flip)}},{key:"_invertAngle",value:function(e,t){var n=this.getCanvasImage(),i=n.angle;e&&(i*=-1),t&&(i*=-1),n.rotate(parseFloat(i)).setCoords()}},{key:"_flipObjects",value:function(e,t){var n=this.getCanvas();e&&n.forEachObject(function(e){e.set({angle:parseFloat(-1*e.angle),flipX:!e.flipX,left:n.width-e.left}).setCoords()}),t&&n.forEachObject(function(e){e.set({angle:parseFloat(-1*e.angle),flipY:!e.flipY,top:n.height-e.top}).setCoords()}),n.renderAll()}},{key:"reset",value:function(){return this.set({flipX:!1,flipY:!1})}},{key:"flipX",value:function(){var e=this.getCurrentSetting();return this.set({flipX:!e.flipX,flipY:e.flipY})}},{key:"flipY",value:function(){var e=this.getCurrentSetting();return this.set({flipX:e.flipX,flipY:!e.flipY})}}]),f);function f(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,f),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(f.__proto__||Object.getPrototypeOf(f)).call(this,u.FLIP,e))}e.exports=h},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var l=s(n(5)),a=s(n(2)),r=s(n(8));function s(e){return e&&e.__esModule?e:{default:e}}var c=s(n(0)).default.componentNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,r.default),i(d,[{key:"getCurrentAngle",value:function(){return this.getCanvasImage().angle}},{key:"setAngle",value:function(e){var t=this.getCurrentAngle()%360;e%=360;var n=this.getCanvasImage(),i=n.getCenterPoint();n.set({angle:e}).setCoords(),this.adjustCanvasDimension();var o=n.getCenterPoint();return this._rotateForEachObject(i,o,e-t),a.default.resolve(e)}},{key:"_rotateForEachObject",value:function(o,e,a){var t=this.getCanvas(),r=o.x-e.x,s=o.y-e.y;t.forEachObject(function(e){var t=e.getCenterPoint(),n=l.default.util.degreesToRadians(a),i=l.default.util.rotatePoint(t,o,n);e.set({left:i.x-r,top:i.y-s,angle:(e.angle+a)%360}),e.setCoords()}),t.renderAll()}},{key:"rotate",value:function(e){var t=this.getCurrentAngle();return this.setAngle(t+e)}}]),d);function d(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,c.ROTATION,e))}e.exports=u},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(n(5)),r=l(n(8)),s=l(n(0));function l(e){return e&&e.__esModule?e:{default:e}}var c=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(u,r.default),i(u,[{key:"start",value:function(e){this.getCanvas().isDrawingMode=!0,this.setBrush(e)}},{key:"setBrush",value:function(e){var t=this.getCanvas().freeDrawingBrush;e=e||{},this.width=e.width||this.width,e.color&&(this.oColor=new a.default.Color(e.color)),t.width=this.width,t.color=this.oColor.toRgba()}},{key:"end",value:function(){this.getCanvas().isDrawingMode=!1}}]),u);function u(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(u.__proto__||Object.getPrototypeOf(u)).call(this,s.default.componentNames.FREE_DRAWING,e));return t.width=12,t.oColor=new a.default.Color("rgba(0, 0, 0, 0.5)"),t}e.exports=c},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(n(5)),r=l(n(8)),s=l(n(0));function l(e){return e&&e.__esModule?e:{default:e}}var c=s.default.eventNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,r.default),i(d,[{key:"start",value:function(e){var t=this.getCanvas();t.defaultCursor="crosshair",t.selection=!1,this.setBrush(e),t.forEachObject(function(e){e.set({evented:!1})}),t.on({"mouse:down":this._listeners.mousedown})}},{key:"setBrush",value:function(e){var t=this.getCanvas().freeDrawingBrush;e=e||{},this._width=e.width||this._width,e.color&&(this._oColor=new a.default.Color(e.color)),t.width=this._width,t.color=this._oColor.toRgba()}},{key:"end",value:function(){var e=this.getCanvas();e.defaultCursor="default",e.selection=!0,e.forEachObject(function(e){e.set({evented:!0})}),e.off("mouse:down",this._listeners.mousedown)}},{key:"_onFabricMouseDown",value:function(e){var t=this.getCanvas(),n=t.getPointer(e.e),i=[n.x,n.y,n.x,n.y];this._line=new a.default.Line(i,{stroke:this._oColor.toRgba(),strokeWidth:this._width,evented:!1}),this._line.set(s.default.fObjectOptions.SELECTION_STYLE),t.add(this._line),t.on({"mouse:move":this._listeners.mousemove,"mouse:up":this._listeners.mouseup})}},{key:"_onFabricMouseMove",value:function(e){var t=this.getCanvas(),n=t.getPointer(e.e);this._line.set({x2:n.x,y2:n.y}),this._line.setCoords(),t.renderAll()}},{key:"_onFabricMouseUp",value:function(){var e=this.getCanvas(),t=this.graphics.createObjectProperties(this._line);this.fire(c.ADD_OBJECT,t),this._line=null,e.off({"mouse:move":this._listeners.mousemove,"mouse:up":this._listeners.mouseup})}}]),d);function d(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,s.default.componentNames.LINE,e));return t._width=12,t._oColor=new a.default.Color("rgba(0, 0, 0, 0.5)"),t._listeners={mousedown:t._onFabricMouseDown.bind(t),mousemove:t._onFabricMouseMove.bind(t),mouseup:t._onFabricMouseUp.bind(t)},t}e.exports=u},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var l=s(n(5)),c=s(n(1)),u=s(n(2)),a=s(n(8)),d=s(n(0)),r=s(n(4));function s(e){return e&&e.__esModule?e:{default:e}}var h=d.default.eventNames,f={fill:"#000000",left:0,top:0},v={fill:"#000000",fontStyle:"normal",fontWeight:"normal",textAlign:"left",underline:!1},p=c.default.browser,g=r.default.makeStyleText({position:"absolute",padding:0,display:"none",border:"1px dotted red",overflow:"hidden",resize:"none",outline:"none","border-radius":0,"background-color":"transparent","-webkit-appearance":"none","z-index":9999,"white-space":"pre"}),m=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(b,a.default),i(b,[{key:"start",value:function(){var e=this.getCanvas();e.selection=!1,e.defaultCursor="text",e.on({"mouse:down":this._listeners.mousedown,"object:selected":this._listeners.select,"before:selection:cleared":this._listeners.selectClear,"object:scaling":this._listeners.scaling,"text:editing":this._listeners.modify}),this.useItext?e.forEachObject(function(e){"i-text"===e.type&&e.set({left:e.left-e.width/2,top:e.top-e.height/2,originX:"left",originY:"top"})}):this._createTextarea(),this.setCanvasRatio()}},{key:"end",value:function(){var t=this.getCanvas();t.selection=!0,t.defaultCursor="default",this.useItext?t.forEachObject(function(e){"i-text"===e.type&&(""===e.text?t.remove(e):e.set({left:e.left+e.width/2,top:e.top+e.height/2,originX:"center",originY:"center"}))}):(t.discardActiveObject(),this._removeTextarea()),t.off({"mouse:down":this._listeners.mousedown,"object:selected":this._listeners.select,"before:selection:cleared":this._listeners.selectClear,"object:scaling":this._listeners.scaling,"text:editing":this._listeners.modify})}},{key:"add",value:function(a,r){var s=this;return new u.default(function(e){var t=s.getCanvas(),n=null,i=d.default.fObjectOptions.SELECTION_STYLE,o=s._defaultStyles;s._setInitPos(r.position),r.styles&&(o=c.default.extend(o,r.styles)),s.useItext?(n=new l.default.IText(a,o),i=c.default.extend({},i,{originX:"left",originY:"top"})):n=new l.default.Text(a,o),n.set(i),n.on({mouseup:s._onFabricMouseUp.bind(s)}),t.add(n),t.getActiveObject()||t.setActiveObject(n),s.isPrevEditing=!0,e(s.graphics.createObjectProperties(n))})}},{key:"change",value:function(t,n){var i=this;return new u.default(function(e){t.set("text",n),i.getCanvas().renderAll(),e()})}},{key:"setStyle",value:function(n,i){var t=this;return new u.default(function(e){c.default.forEach(i,function(e,t){n[t]===e&&"fontSize"!==t&&(i[t]=v[t]||"")},t),"textDecoration"in i&&c.default.extend(i,t._getTextDecorationAdaptObject(i.textDecoration)),n.set(i),t.getCanvas().renderAll(),e()})}},{key:"getText",value:function(e){return e.text}},{key:"setSelectedInfo",value:function(e,t){this._selectedObj=e,this._isSelected=t}},{key:"isSelected",value:function(){return this._isSelected}},{key:"getSelectedObj",value:function(){return this._selectedObj}},{key:"setCanvasRatio",value:function(){var e=this.getCanvasElement(),t=parseInt(e.style.maxWidth,10),n=e.width/t;this._ratio=n}},{key:"getCanvasRatio",value:function(){return this._ratio}},{key:"_getTextDecorationAdaptObject",value:function(e){return{underline:"underline"===e,linetrought:"line-through"===e,overline:"overline"===e}}},{key:"_setInitPos",value:function(e){e=e||this.getCanvasImage().getCenterPoint(),this._defaultStyles.left=e.x,this._defaultStyles.top=e.y}},{key:"_createTextarea",value:function(){var e=this.getCanvasElement().parentNode,t=document.createElement("textarea");t.className="tui-image-eidtor-textarea",t.setAttribute("style",g),t.setAttribute("wrap","off"),e.appendChild(t),this._textarea=t,this._listeners=c.default.extend(this._listeners,{input:this._onInput.bind(this),keydown:this._onKeyDown.bind(this),blur:this._onBlur.bind(this),scroll:this._onScroll.bind(this)}),p.msie&&9===p.version?l.default.util.addListener(t,"keydown",this._listeners.keydown):l.default.util.addListener(t,"input",this._listeners.input),l.default.util.addListener(t,"blur",this._listeners.blur),l.default.util.addListener(t,"scroll",this._listeners.scroll)}},{key:"_removeTextarea",value:function(){var e=this.getCanvasElement().parentNode,t=e.querySelector("textarea");e.removeChild(t),this._textarea=null,p.msie&&p.version<10?l.default.util.removeListener(t,"keydown",this._listeners.keydown):l.default.util.removeListener(t,"input",this._listeners.input),l.default.util.removeListener(t,"blur",this._listeners.blur),l.default.util.removeListener(t,"scroll",this._listeners.scroll)}},{key:"_onInput",value:function(){var e=this.getCanvasRatio(),t=this._editingObj,n=this._textarea.style;n.width=Math.ceil(t.width/e)+"px",n.height=Math.ceil(t.height/e)+"px"}},{key:"_onKeyDown",value:function(){var e=this,t=this.getCanvasRatio(),n=this._editingObj,i=this._textarea.style;setTimeout(function(){n.text(e._textarea.value),i.width=Math.ceil(n.width/t)+"px",i.height=Math.ceil(n.height/t)+"px"},0)}},{key:"_onBlur",value:function(){var e=this.getCanvasRatio(),t=this._editingObj,n=this._editingObjInfos,i=this._textarea.value,o=t.width/e-n.width/e,a=t.height/e-n.height/e;if(1===e&&(o/=2,a/=2),this._textarea.style.display="none",t.set({left:n.left+o,top:n.top+a}),i.length){this.getCanvas().add(t);var r={id:c.default.stamp(t),type:t.type,text:i};this.fire(h.TEXT_CHANGED,r)}}},{key:"_onScroll",value:function(){this._textarea.scrollLeft=0,this._textarea.scrollTop=0}},{key:"_onFabricScaling",value:function(e){var t=e.target,n=t.fontSize*t.scaleY;t.fontSize=n,t.scaleX=1,t.scaleY=1}},{key:"_onFabricSelectClear",value:function(e){var t=this.getSelectedObj();this.isPrevEditing=!0,this.setSelectedInfo(e.target,!1),t&&""===t.text&&this.getCanvas().remove(t)}},{key:"_onFabricSelect",value:function(e){this.isPrevEditing=!0,this.setSelectedInfo(e.target,!0)}},{key:"_onFabricMouseDown",value:function(e){var t=e.target;t&&!t.isType("text")||(this.isPrevEditing?this.isPrevEditing=!1:this._fireAddText(e))}},{key:"_fireAddText",value:function(e){var t=e.target,n=e.e||{},i=this.getCanvas().getPointer(n);t||this.fire(h.ADD_TEXT,{originPosition:{x:i.x,y:i.y},clientPosition:{x:n.clientX||0,y:n.clientY||0}})}},{key:"_onFabricMouseUp",value:function(e){var t=e.target,n=(new Date).getTime();(t.isEditing||this._isDoubleClick(n))&&(this.useItext||this._changeToEditingMode(t),this.fire(h.TEXT_EDITING)),this._lastClickTime=n}},{key:"_isDoubleClick",value:function(e){return e-this._lastClickTime<500}},{key:"_changeToEditingMode",value:function(e){var t=this.getCanvasRatio(),n=this._textarea.style,i=this.getCanvas();this.isPrevEditing=!0,i.remove(e),i.discardActiveObject(),this._editingObj=e,this._textarea.value=e.text,this._editingObjInfos={left:e.left,top:e.top,width:e.width,height:e.height},n.display="block",n.left=e.oCoords.tl.x/t+"px",n.top=e.oCoords.tl.y/t+"px",n.width=Math.ceil(e.width/t)+"px",n.height=Math.ceil(e.height/t)+"px",n.transform="rotate("+e.angle+"deg)",n.color=e.fill,n["font-size"]=e.fontSize/t+"px",n["font-family"]=e.fontFamily,n["font-style"]=e.fontStyle,n["font-weight"]=e.fontWeight,n["text-align"]=e.textAlign,n["line-height"]=e.lineHeight+.1,n["transform-origin"]="left top",this._textarea.focus()}}]),b);function b(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,b);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(b.__proto__||Object.getPrototypeOf(b)).call(this,d.default.componentNames.TEXT,e));return t._defaultStyles=f,t._isSelected=!1,t._selectedObj={},t._editingObj={},t._listeners={mousedown:t._onFabricMouseDown.bind(t),select:t._onFabricSelect.bind(t),selectClear:t._onFabricSelectClear.bind(t),scaling:t._onFabricScaling.bind(t)},t._textarea=null,t._ratio=1,t._lastClickTime=(new Date).getTime(),t._editingObjInfos={},t.isPrevEditing=!1,t.useItext=e.useItext,t}e.exports=m},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=l(n(5)),d=l(n(1)),r=l(n(2)),s=l(n(8)),h=l(n(0));function l(e){return e&&e.__esModule?e:{default:e}}var c=h.default.eventNames,f=h.default.rejectMessages,u={arrow:"M 0 90 H 105 V 120 L 160 60 L 105 0 V 30 H 0 Z",cancel:"M 0 30 L 30 60 L 0 90 L 30 120 L 60 90 L 90 120 L 120 90 L 90 60 L 120 30 L 90 0 L 60 30 L 30 0 Z"},v=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(p,s.default),i(p,[{key:"add",value:function(l,c){var u=this;return new r.default(function(e,t){var n=u.getCanvas(),i=u._pathMap[l],o=h.default.fObjectOptions.SELECTION_STYLE,a=0<=Object.keys(h.default.defaultIconPath).indexOf(l),r=u.useDragAddIcon&&a,s=i?u._createIcon(i):null;s||t(f.invalidParameters),s.set(d.default.extend({type:"icon",fill:u._oColor},o,c,u.graphics.controlStyle)),n.add(s).setActiveObject(s),r&&u._addWithDragEvent(n),e(u.graphics.createObjectProperties(s))})}},{key:"_addWithDragEvent",value:function(t){var n=this;t.on({"mouse:move":function(e){t.selection=!1,n.fire(c.ICON_CREATE_RESIZE,{moveOriginPointer:t.getPointer(e.e)})},"mouse:up":function(e){n.fire(c.ICON_CREATE_END,{moveOriginPointer:t.getPointer(e.e)}),t.defaultCursor="default",t.off("mouse:up"),t.off("mouse:move"),t.selection=!0}})}},{key:"registerPaths",value:function(e){var n=this;d.default.forEach(e,function(e,t){n._pathMap[t]=e},this)}},{key:"setColor",value:function(e,t){this._oColor=e,t&&"icon"===t.get("type")&&(t.set({fill:this._oColor}),this.getCanvas().renderAll())}},{key:"getColor",value:function(e){return e.fill}},{key:"_createIcon",value:function(e){return new a.default.Path(e)}}]),p);function p(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,p);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(p.__proto__||Object.getPrototypeOf(p)).call(this,h.default.componentNames.ICON,e));return t._oColor="#000000",t._pathMap=u,t.useDragAddIcon=e.useDragAddIcon,t}e.exports=v},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=n(1),l=p(n(2)),r=p(n(5)),s=p(n(8)),c=p(n(120)),u=p(n(0)),d=p(n(121)),h=p(n(122)),f=p(n(123)),v=p(n(124));function p(e){return e&&e.__esModule?e:{default:e}}var g=u.default.rejectMessages,m=r.default.Image.filters;m.Mask=c.default,m.Blur=d.default,m.Sharpen=h.default,m.Emboss=f.default,m.ColorFilter=v.default;var b=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(y,s.default),i(y,[{key:"add",value:function(a,r){var s=this;return new l.default(function(e,t){var n=s._getSourceImage(),i=s.getCanvas(),o=s._getFilter(n,a);(o=o||s._createFilter(n,a,r))||t(g.invalidParameters),s._changeFilterValues(o,r),s._apply(n,function(){i.renderAll(),e({type:a,action:"add",options:r})})})}},{key:"remove",value:function(a){var r=this;return new l.default(function(e,t){var n=r._getSourceImage(),i=r.getCanvas(),o=r.getOptions(a);n.filters.length||t(g.unsupportedOperation),r._removeFilter(n,a),r._apply(n,function(){i.renderAll(),e({type:a,action:"remove",options:o})})})}},{key:"hasFilter",value:function(e){return!!this._getFilter(this._getSourceImage(),e)}},{key:"getOptions",value:function(e){var t=this._getSourceImage(),n=this._getFilter(t,e);return n?(0,a.extend)({},n.options):null}},{key:"_changeFilterValues",value:function(n,i){(0,a.forEach)(i,function(e,t){(0,a.isUndefined)(n[t])||(n[t]=e)}),(0,a.forEach)(n.options,function(e,t){(0,a.isUndefined)(i[t])||(n.options[t]=i[t])})}},{key:"_apply",value:function(e,t){e.filters.push(),e.applyFilters()&&t()}},{key:"_getSourceImage",value:function(){return this.getCanvasImage()}},{key:"_createFilter",value:function(e,t,n){var i=void 0,o=this._getFabricFilterType(t),a=r.default.Image.filters[o];return a&&((i=new a(n)).options=n,e.filters.push(i)),i}},{key:"_getFilter",value:function(e,t){var n=null;if(e){var i=this._getFabricFilterType(t),o=e.filters.length,a=void 0,r=void 0;for(r=0;r<o;r+=1)if((a=e.filters[r]).type===i){n=a;break}}return n}},{key:"_removeFilter",value:function(e,t){var n=this._getFabricFilterType(t);e.filters=(0,a.filter)(e.filters,function(e){return e.type!==n})}},{key:"_getFabricFilterType",value:function(e){return e.charAt(0).toUpperCase()+e.slice(1)}}]),y);function y(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,y),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(y.__proto__||Object.getPrototypeOf(y)).call(this,u.default.componentNames.FILTER,e))}e.exports=b},function(e,t,n){"use strict";var i,o=n(5),a=(i=o)&&i.__esModule?i:{default:i};var r=a.default.util.createClass(a.default.Image.filters.BlendImage,{applyTo:function(e){if(this.mask){var t=e.canvasEl,n=t.width,i=t.height,o=this._createCanvasOfMask(n,i),a=t.getContext("2d"),r=o.getContext("2d"),s=a.getImageData(0,0,n,i);this._drawMask(r,t,a),this._mapData(r,s,n,i),e.imageData=s}},_createCanvasOfMask:function(e,t){var n=a.default.util.createCanvasElement();return n.width=e,n.height=t,n},_drawMask:function(e){var t=this.mask,n=t.getElement(),i=t.angle,o=t.left,a=t.scaleX,r=t.scaleY,s=t.top;e.save(),e.translate(o,s),e.rotate(i*Math.PI/180),e.scale(a,r),e.drawImage(n,-n.width/2,-n.height/2),e.restore()},_mapData:function(e,t,n,i){for(var o=t.data,a=t.height,r=o,s=t.width*a*4,l=e.getImageData(0,0,n,i).data,c=0;c<s;c+=4)r[c+3]=l[c]}});e.exports=r},function(e,t,n){"use strict";var i,o=n(5),a=(i=o)&&i.__esModule?i:{default:i};var r=a.default.util.createClass(a.default.Image.filters.Convolute,{type:"Blur",initialize:function(){this.matrix=[1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9,1/9]}});e.exports=r},function(e,t,n){"use strict";var i,o=n(5),a=(i=o)&&i.__esModule?i:{default:i};var r=a.default.util.createClass(a.default.Image.filters.Convolute,{type:"Sharpen",initialize:function(){this.matrix=[0,-1,0,-1,5,-1,0,-1,0]}});e.exports=r},function(e,t,n){"use strict";var i,o=n(5),a=(i=o)&&i.__esModule?i:{default:i};var r=a.default.util.createClass(a.default.Image.filters.Convolute,{type:"Emboss",initialize:function(){this.matrix=[1,1,1,1,.7,-1,-1,-1,-1]}});e.exports=r},function(e,t,n){"use strict";var i,o=n(5),c=(i=o)&&i.__esModule?i:{default:i};var a=c.default.util.createClass(c.default.Image.filters.BaseFilter,{type:"ColorFilter",initialize:function(e){e=e||{},this.color=e.color||"#FFFFFF",this.threshold=e.threshold||45,this.x=e.x||null,this.y=e.y||null},applyTo:function(e){var t,n=e.canvasEl,i=n.getContext("2d"),o=i.getImageData(0,0,n.width,n.height),a=o.data,r=this.threshold,s=c.default.Color.sourceFromHex(this.color),l=void 0;for(this.x&&this.y&&(s=this._getColor(o,this.x,this.y)),l=0,t=a.length;l<t;l+=4)this._isOutsideThreshold(a[l],s[0],r)||this._isOutsideThreshold(a[l+1],s[1],r)||this._isOutsideThreshold(a[l+2],s[2],r)||(a[l]=a[l+1]=a[l+2]=a[l+3]=0);i.putImageData(o,0,0)},_isOutsideThreshold:function(e,t,n){var i=e-t;return Math.abs(i)>n},_getColor:function(e,t,n){var i=[0,0,0,0],o=e.data,a=4*(e.width*n+t);return i[0]=o[a],i[1]=o[1+a],i[2]=o[2+a],i[3]=o[3+a],i}});e.exports=a},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=d(n(5)),r=d(n(2)),s=d(n(8)),l=d(n(0)),c=d(n(126)),u=n(1);function d(e){return e&&e.__esModule?e:{default:e}}var h=l.default.rejectMessages,f=l.default.eventNames,v=l.default.SHAPE_DEFAULT_OPTIONS,p=l.default.keyCodes,g=(0,u.extend)({strokeWidth:1,stroke:"#000000",fill:"#ffffff",width:1,height:1,rx:0,ry:0},v),m=["rect","circle","triangle"],b=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(y,s.default),i(y,[{key:"start",value:function(){var e=this.getCanvas();this._isSelected=!1,e.defaultCursor="crosshair",e.selection=!1,e.uniScaleTransform=!0,e.on({"mouse:down":this._handlers.mousedown}),a.default.util.addListener(document,"keydown",this._handlers.keydown),a.default.util.addListener(document,"keyup",this._handlers.keyup)}},{key:"end",value:function(){var e=this.getCanvas();this._isSelected=!1,e.defaultCursor="default",e.selection=!0,e.uniScaleTransform=!1,e.off({"mouse:down":this._handlers.mousedown}),a.default.util.removeListener(document,"keydown",this._handlers.keydown),a.default.util.removeListener(document,"keyup",this._handlers.keyup)}},{key:"setStates",value:function(e,t){this._type=e,t&&(this._options=(0,u.extend)(this._options,t))}},{key:"add",value:function(i,o){var a=this;return new r.default(function(e){var t=a.getCanvas();o=a._extendOptions(o);var n=a._createInstance(i,o);a._bindEventOnShape(n),t.add(n).setActiveObject(n),e(a.graphics.createObjectProperties(n))})}},{key:"change",value:function(n,i){var o=this;return new r.default(function(e,t){(0,u.inArray)(n.get("type"),m)<0&&t(h.unsupportedType),n.set(i),o.getCanvas().renderAll(),e()})}},{key:"_createInstance",value:function(e,t){var n=void 0;switch(e){case"rect":n=new a.default.Rect(t);break;case"circle":n=new a.default.Ellipse((0,u.extend)({type:"circle"},t));break;case"triangle":n=new a.default.Triangle(t);break;default:n={}}return n}},{key:"_extendOptions",value:function(e){var t=l.default.fObjectOptions.SELECTION_STYLE;return(e=(0,u.extend)({},g,this._options,t,e)).isRegular&&(e.lockUniScaling=!0),e}},{key:"_bindEventOnShape",value:function(e){var i=this,o=this.getCanvas();e.on({added:function(){i._shapeObj=this,c.default.setOrigins(i._shapeObj)},selected:function(){i._isSelected=!0,i._shapeObj=this,o.uniScaleTransform=!0,o.defaultCursor="default",c.default.setOrigins(i._shapeObj)},deselected:function(){i._isSelected=!1,i._shapeObj=null,o.defaultCursor="crosshair",o.uniScaleTransform=!1},modified:function(){var e=i._shapeObj;c.default.adjustOriginToCenter(e),c.default.setOrigins(e)},scaling:function(e){var t=o.getPointer(e.e),n=i._shapeObj;o.setCursor("crosshair"),c.default.resize(n,t,!0)}})}},{key:"_onFabricMouseDown",value:function(e){if(e.target||(this._isSelected=!1,this._shapeObj=!1),!this._isSelected&&!this._shapeObj){var t=this.getCanvas();this._startPoint=t.getPointer(e.e),t.on({"mouse:move":this._handlers.mousemove,"mouse:up":this._handlers.mouseup})}}},{key:"_onFabricMouseMove",value:function(e){var t=this,n=this.getCanvas(),i=n.getPointer(e.e),o=this._startPoint.x,a=this._startPoint.y,r=o-i.x,s=a-i.y,l=this._shapeObj;l?(this._shapeObj.set({isRegular:this._withShiftKey}),c.default.resize(l,i),n.renderAll()):this.add(this._type,{left:o,top:a,width:r,height:s}).then(function(e){t.fire(f.ADD_OBJECT,e)})}},{key:"_onFabricMouseUp",value:function(){var t=this,e=this.getCanvas(),n=this._startPoint.x,i=this._startPoint.y,o=this._shapeObj;o?o&&(c.default.adjustOriginToCenter(o),this.fire(f.ADD_OBJECT_AFTER,this.graphics.createObjectProperties(o))):this.add(this._type,{left:n,top:i,width:20,height:20}).then(function(e){t.fire(f.ADD_OBJECT,e)}),e.off({"mouse:move":this._handlers.mousemove,"mouse:up":this._handlers.mouseup})}},{key:"_onKeyDown",value:function(e){e.keyCode===p.SHIFT&&(this._withShiftKey=!0,this._shapeObj&&(this._shapeObj.isRegular=!0))}},{key:"_onKeyUp",value:function(e){e.keyCode===p.SHIFT&&(this._withShiftKey=!1,this._shapeObj&&(this._shapeObj.isRegular=!1))}}]),y);function y(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,y);var t=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(y.__proto__||Object.getPrototypeOf(y)).call(this,l.default.componentNames.SHAPE,e));return t._shapeObj=null,t._type="rect",t._options=(0,u.extend)({},g),t._isSelected=!1,t._startPoint={},t._withShiftKey=!1,t._handlers={mousedown:t._onFabricMouseDown.bind(t),mousemove:t._onFabricMouseMove.bind(t),mouseup:t._onFabricMouseUp.bind(t),keydown:t._onKeyDown.bind(t),keyup:t._onKeyUp.bind(t)},t}e.exports=b},function(e,t,n){"use strict";var R={rect:1,circle:2,triangle:1},D={rect:{w:"width",h:"height"},circle:{w:"rx",h:"ry"},triangle:{w:"width",h:"height"}};function L(e,t,n){var i=e.x,o=e.y,a=t.x,r=t.y,s=n*Math.PI/180;return{originX:(a-i)*Math.cos(s)-(r-o)*Math.sin(s)+i<i?"right":"left",originY:(a-i)*Math.sin(s)+(r-o)*Math.cos(s)+o<o?"bottom":"top"}}e.exports={setOrigins:function(e){var t=e.getPointByOrigin("left","top"),n=e.getPointByOrigin("right","top"),i=e.getPointByOrigin("right","bottom"),o=e.getPointByOrigin("left","bottom");e.origins={lt:t,rt:n,rb:i,lb:o}},resize:function(e,t,n){var i,o,a,r,s,l,c,u,d,h,f,v,p,g,m,b,y,_,k,E,w,x,C,O,S,M,j,T,P,I,A;"center"===(g=e).originX&&"center"===g.originY&&(s=t,c=(l=e).getPointByOrigin("center","center"),u=L(c,s,-l.angle),d=u.originX,h=u.originY,f=l.getPointByOrigin(d,h),v=l.left-(c.x-f.x),p=l.top-(c.y-f.y),l.set({originX:d,originY:h,left:v,top:p}),l.setCoords(),o=(i=e).originX,a=i.originY,r=o.substring(0,1)+a.substring(0,1),i.startPoint=i.origins[r]),n?function(e){var t=e.type,n=e.scaleX,i=e.scaleY,o=D[t],a=e[o.w]*n,r=e[o.h]*i;if(e.isRegular){var s=Math.max(n,i);a=e[o.w]*s,r=e[o.h]*s}var l={hasControls:!1,hasBorders:!1,scaleX:1,scaleY:1};l[o.w]=a,l[o.h]=r,e.set(l)}(e):(m=t,y=(b=e).type,_=b.strokeWidth,k=b.startPoint,E=R[y],w=D[y],x=!("triangle"!==b.type),C={},O=Math.abs(k.x-m.x)/E,S=Math.abs(k.y-m.y)/E,_<O&&(O-=_/E),_<S&&(S-=_/E),b.isRegular&&(O=S=Math.max(O,S),x&&(S=Math.sqrt(3)/2*O)),C[w.w]=O,C[w.h]=S,b.set(C)),M=t,T=(j=e).startPoint,P=L(T,M,-j.angle),I=P.originX,A=P.originY,j.setPositionByOrigin(T,I,A),j.setCoords()},adjustOriginToCenter:function(e){var t=e.getPointByOrigin("center","center"),n=e.originX,i=e.originY,o=e.getPointByOrigin(n,i),a=e.left+(t.x-o.x),r=e.top+(t.y-o.y);e.set({hasControls:!0,hasBorders:!0,originX:"center",originY:"center",left:a,top:r}),e.setCoords()}}},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(17)),r=s(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var l=r.default.drawingModes,c=r.default.componentNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,a.default),i(d,[{key:"start",value:function(e){e.getComponent(c.CROPPER).start()}},{key:"end",value:function(e){e.getComponent(c.CROPPER).end()}}]),d);function d(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,l.CROPPER))}e.exports=u},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(17)),r=s(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var l=r.default.drawingModes,c=r.default.componentNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,a.default),i(d,[{key:"start",value:function(e,t){e.getComponent(c.FREE_DRAWING).start(t)}},{key:"end",value:function(e){e.getComponent(c.FREE_DRAWING).end()}}]),d);function d(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,l.FREE_DRAWING))}e.exports=u},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(17)),r=s(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var l=r.default.drawingModes,c=r.default.componentNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,a.default),i(d,[{key:"start",value:function(e,t){e.getComponent(c.LINE).start(t)}},{key:"end",value:function(e){e.getComponent(c.LINE).end()}}]),d);function d(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,l.LINE_DRAWING))}e.exports=u},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(17)),r=s(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var l=r.default.drawingModes,c=r.default.componentNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,a.default),i(d,[{key:"start",value:function(e){e.getComponent(c.SHAPE).start()}},{key:"end",value:function(e){e.getComponent(c.SHAPE).end()}}]),d);function d(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,l.SHAPE))}e.exports=u},function(e,t,n){"use strict";var i=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e};function o(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}var a=s(n(17)),r=s(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var l=r.default.drawingModes,c=r.default.componentNames,u=(function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(d,a.default),i(d,[{key:"start",value:function(e){e.getComponent(c.TEXT).start()}},{key:"end",value:function(e){e.getComponent(c.TEXT).end()}}]),d);function d(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,d),function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(d.__proto__||Object.getPrototypeOf(d)).call(this,l.TEXT))}e.exports=u},function(e,t,n){},function(e,t,n){"use strict";var i=r(n(3)),o=r(n(2)),a=r(n(0));function r(e){return e&&e.__esModule?e:{default:e}}var s=a.default.componentNames,l=a.default.commandNames,c=s.ICON,u={name:l.ADD_ICON,execute:function(t,e,n){var i=this;return t.getComponent(c).add(e,n).then(function(e){return i.undoData.object=t.getObject(e.id),e})},undo:function(e){return e.remove(this.undoData.object),o.default.resolve()}};i.default.register(u),e.exports=u},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(2));function a(e){return e&&e.__esModule?e:{default:e}}var r={name:a(n(0)).default.commandNames.ADD_IMAGE_OBJECT,execute:function(t,e){var n=this;return t.addImageObject(e).then(function(e){return n.undoData.object=t.getObject(e.id),e})},undo:function(e){return e.remove(this.undoData.object),o.default.resolve()}};i.default.register(r),e.exports=r},function(e,t,n){"use strict";var i=r(n(3)),o=r(n(2)),a=r(n(0));function r(e){return e&&e.__esModule?e:{default:e}}var s=a.default.commandNames,l=a.default.rejectMessages,c={name:s.ADD_OBJECT,execute:function(n,i){return new o.default(function(e,t){n.contains(i)?t(l.addedObject):(n.add(i),e(i))})},undo:function(n,i){return new o.default(function(e,t){n.contains(i)?(n.remove(i),e(i)):t(l.noObject)})}};i.default.register(c),e.exports=c},function(e,t,n){"use strict";var i=r(n(3)),o=r(n(2)),a=r(n(0));function r(e){return e&&e.__esModule?e:{default:e}}var s=a.default.componentNames,l=a.default.commandNames,c=s.SHAPE,u={name:l.ADD_SHAPE,execute:function(t,e,n){var i=this;return t.getComponent(c).add(e,n).then(function(e){return i.undoData.object=t.getObject(e.id),e})},undo:function(e){return e.remove(this.undoData.object),o.default.resolve()}};i.default.register(u),e.exports=u},function(e,t,n){"use strict";var i=a(n(3)),r=a(n(2)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var s=o.default.componentNames,l=o.default.commandNames,c=o.default.rejectMessages,u=s.TEXT,d={name:l.ADD_TEXT,execute:function(i,e,t){var o=this,n=i.getComponent(u);if(this.undoData.object){var a=this.undoData.object;return new r.default(function(e,t){i.contains(a)?t(c.redo):(i.add(a),e(a))})}return n.add(e,t).then(function(e){var t=e.id,n=i.getObject(t);return o.undoData.object=n,e})},undo:function(e){return e.remove(this.undoData.object),r.default.resolve()}};i.default.register(d),e.exports=d},function(e,t,n){"use strict";var d=a(n(1)),i=a(n(3)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,h=o.default.rejectMessages,s=o.default.commandNames,f=r.FILTER,v=null;var l={name:s.APPLY_FILTER,execute:function(e,t,n,i){var o,a,r,s=e.getComponent(f);if("mask"===t){var l=e.getObject(n.maskObjId);if(!l||!l.isType("image"))return Promise.reject(h.invalidParameters);d.default.extend(n,{mask:l}),e.remove(n.mask)}if(!this.isRedo){var c=s.getOptions(t),u=(o=c,a=n,r={},"mask"===t&&(r.object=a.mask),r.options=o,r);v=this.setUndoData(u,v,i)}return s.add(t,n)},undo:function(e,t){var n=e.getComponent(f);if("mask"!==t)return this.undoData.options?n.add(t,this.undoData.options):n.remove(t);var i=this.undoData.object;return e.add(i),e.setActiveObject(i),n.remove(t)}};i.default.register(l),e.exports=l},function(e,t,n){"use strict";var i=a(n(3)),l=a(n(2)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,c=o.default.rejectMessages,s=o.default.commandNames,u=r.ICON,d={name:s.CHANGE_ICON_COLOR,execute:function(o,a,r){var s=this;return new l.default(function(e,t){var n=o.getComponent(u),i=o.getObject(a);i||t(c.noObject),s.undoData.object=i,s.undoData.color=n.getColor(i),n.setColor(r,i),e()})},undo:function(e){var t=e.getComponent(u),n=this.undoData.object,i=n.object,o=n.color;return t.setColor(o,i),l.default.resolve()}};i.default.register(d),e.exports=d},function(e,t,n){"use strict";var u=a(n(1)),i=a(n(3)),d=a(n(2)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,h=o.default.rejectMessages,s=o.default.commandNames,f=r.SHAPE,v=null;var l={name:s.CHANGE_SHAPE,execute:function(e,t,n,i){var o,a,r,s=e.getComponent(f),l=e.getObject(t);if(!l)return d.default.reject(h.noObject);if(!this.isRedo){var c=(o=n,r={object:a=l,options:{}},u.default.forEachOwnProperties(o,function(e,t){r.options[t]=a[t]}),r);v=this.setUndoData(c,v,i)}return s.change(l,n)},undo:function(e){var t=e.getComponent(f),n=this.undoData,i=n.object,o=n.options;return t.change(i,o)}};i.default.register(l),e.exports=l},function(e,t,n){"use strict";var i=r(n(3)),a=r(n(2)),o=r(n(0));function r(e){return e&&e.__esModule?e:{default:e}}var s=o.default.componentNames,l=o.default.rejectMessages,c=o.default.commandNames,u=s.TEXT,d={name:c.CHANGE_TEXT,execute:function(e,t,n){var i=e.getComponent(u),o=e.getObject(t);return o?(this.undoData.object=o,this.undoData.text=i.getText(o),i.change(o,n)):a.default.reject(l.noObject)},undo:function(e){var t=e.getComponent(u),n=this.undoData,i=n.object,o=n.text;return t.change(i,o)}};i.default.register(d),e.exports=d},function(e,t,n){"use strict";var u=a(n(1)),i=a(n(3)),d=a(n(2)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,h=o.default.rejectMessages,s=o.default.commandNames,f=r.TEXT,v=null;var l={name:s.CHANGE_TEXT_STYLE,execute:function(e,t,n,i){var o,a,r,s=e.getComponent(f),l=e.getObject(t);if(!l)return d.default.reject(h.noObject);if(!this.isRedo){var c=(o=n,r={object:a=l,styles:{}},u.default.forEachOwnProperties(o,function(e,t){var n=a[t];r.styles[t]=n}),r);v=this.setUndoData(c,v,i)}return s.setStyle(l,n)},undo:function(e){var t=e.getComponent(f),n=this.undoData,i=n.object,o=n.styles;return t.setStyle(i,o)}};i.default.register(l),e.exports=l},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(2));function a(e){return e&&e.__esModule?e:{default:e}}var r={name:a(n(0)).default.commandNames.CLEAR_OBJECTS,execute:function(t){var n=this;return new o.default(function(e){n.undoData.objects=t.removeAll(),e()})},undo:function(e){return e.add(this.undoData.objects),o.default.resolve()}};i.default.register(r),e.exports=r},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,s=o.default.commandNames,l=r.FLIP,c={name:s.FLIP_IMAGE,execute:function(e,t){var n=e.getComponent(l);return this.undoData.setting=n.getCurrentSetting(),n[t]()},undo:function(e){return e.getComponent(l).set(this.undoData.setting)}};i.default.register(c),e.exports=c},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,s=o.default.commandNames,l=r.IMAGE_LOADER,c={name:s.LOAD_IMAGE,execute:function(e,t,n){var i=e.getComponent(l),o=i.getCanvasImage(),a=o?o.width:0,r=o?o.height:0,s=e.removeAll(!0).filter(function(e){return"cropzone"!==e.type});return s.forEach(function(e){e.evented=!0}),this.undoData={name:i.getImageName(),image:o,objects:s},i.load(t,n).then(function(e){return{oldWidth:a,oldHeight:r,newWidth:e.width,newHeight:e.height}})},undo:function(e){var t=e.getComponent(l),n=this.undoData,i=n.objects,o=n.name,a=n.image;return e.removeAll(!0),e.add(i),t.load(o,a)}};i.default.register(c),e.exports=c},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,s=o.default.commandNames,l=r.FILTER,c={name:s.REMOVE_FILTER,execute:function(e,t){var n=e.getComponent(l);return this.undoData.options=n.getOptions(t),n.remove(t)},undo:function(e,t){var n=e.getComponent(l),i=this.undoData.options;return n.add(t,i)}};i.default.register(c),e.exports=c},function(e,t,n){"use strict";var i=r(n(3)),a=r(n(2)),o=r(n(0));function r(e){return e&&e.__esModule?e:{default:e}}var s=o.default.commandNames,l=o.default.rejectMessages,c={name:s.REMOVE_OBJECT,execute:function(n,i){var o=this;return new a.default(function(e,t){o.undoData.objects=n.removeObjectById(i),o.undoData.objects.length?e():t(l.noObject)})},undo:function(e){return e.add(this.undoData.objects),a.default.resolve()}};i.default.register(c),e.exports=c},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(2));function a(e){return e&&e.__esModule?e:{default:e}}var r={name:a(n(0)).default.commandNames.RESIZE_CANVAS_DIMENSION,execute:function(t,n){var i=this;return new o.default(function(e){i.undoData.size={width:t.cssMaxWidth,height:t.cssMaxHeight},t.setCssMaxDimension(n),t.adjustCanvasDimension(),e()})},undo:function(e){return e.setCssMaxDimension(this.undoData.size),e.adjustCanvasDimension(),o.default.resolve()}};i.default.register(r),e.exports=r},function(e,t,n){"use strict";var i=a(n(3)),o=a(n(0));function a(e){return e&&e.__esModule?e:{default:e}}var r=o.default.componentNames,s=o.default.commandNames,l=r.ROTATION,c=null;var u={name:s.ROTATE_IMAGE,execute:function(e,t,n,i){var o=e.getComponent(l);if(!this.isRedo){var a={angle:o.getCurrentAngle()};c=this.setUndoData(a,c,i)}return o[t](n)},undo:function(e){var t=e.getComponent(l),n=this.args,i=n[1],o=n[2];return"setAngle"===i?t[i](this.undoData.angle):t.rotate(-o)}};i.default.register(u),e.exports=u},function(e,t,n){"use strict";var a=s(n(1)),i=s(n(3)),r=s(n(2)),o=s(n(0));function s(e){return e&&e.__esModule?e:{default:e}}var l=o.default.commandNames,c=o.default.rejectMessages,u={name:l.SET_OBJECT_PROPERTIES,execute:function(e,t,n){var i=this,o=e.getObject(t);return o?(this.undoData.props={},a.default.forEachOwnProperties(n,function(e,t){i.undoData.props[t]=o[t]}),e.setObjectProperties(t,n),r.default.resolve()):r.default.reject(c.noObject)},undo:function(e,t){var n=this.undoData.props;return e.setObjectProperties(t,n),r.default.resolve()}};i.default.register(u),e.exports=u},function(e,t,n){"use strict";var i=r(n(3)),o=r(n(2)),a=r(n(0));function r(e){return e&&e.__esModule?e:{default:e}}var s=a.default.commandNames,l=a.default.rejectMessages,c={name:s.SET_OBJECT_POSITION,execute:function(e,t,n){return e.getObject(t)?(this.undoData.objectId=t,this.undoData.props=e.getObjectProperties(t,["left","top"]),e.setObjectPosition(t,n),e.renderAll(),o.default.resolve()):o.default.reject(l.noObject)},undo:function(e){var t=this.undoData,n=t.objectId,i=t.props;return e.setObjectProperties(n,i),e.renderAll(),o.default.resolve()}};i.default.register(c),e.exports=c}],a.c=s,a.d=function(e,t,n){a.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},a.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(t,e){if(1&e&&(t=a(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(a.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)a.d(n,i,function(e){return t[e]}.bind(null,i));return n},a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,"a",t),t},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},a.p="/dist",a(a.s=43);function a(e){if(s[e])return s[e].exports;var t=s[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,a),t.l=!0,t.exports}var r,s});