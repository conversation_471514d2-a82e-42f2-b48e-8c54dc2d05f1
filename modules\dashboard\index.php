<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Get current year
$current_year = date('Y');

// Fetch incident statistics for current year
$incident_stats_query = "SELECT
    COUNT(*) as total_incidents,
    SUM(CASE WHEN status = 'Open' THEN 1 ELSE 0 END) as open_incidents,
    SUM(CASE WHEN status = 'In Progress' THEN 1 ELSE 0 END) as in_progress_incidents,
    SUM(CASE WHEN status = 'Under Investigation' THEN 1 ELSE 0 END) as investigating_incidents,
    SUM(CASE WHEN status = 'Resolved' THEN 1 ELSE 0 END) as resolved_incidents,
    SUM(CASE WHEN status = 'Closed' THEN 1 ELSE 0 END) as closed_incidents,
    SUM(CASE WHEN severity = 'Critical' THEN 1 ELSE 0 END) as critical_incidents,
    SUM(CASE WHEN severity = 'High' THEN 1 ELSE 0 END) as high_incidents,
    SUM(CASE WHEN severity = 'Medium' THEN 1 ELSE 0 END) as medium_incidents,
    SUM(CASE WHEN severity = 'Low' THEN 1 ELSE 0 END) as low_incidents
FROM incidents
WHERE YEAR(created_at) = :current_year";

$stmt = $pdo->prepare($incident_stats_query);
$stmt->bindParam(':current_year', $current_year);
$stmt->execute();
$incident_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Fetch recent incidents (last 10)
$recent_incidents_query = "SELECT
    i.incident_no,
    i.event_date,
    i.reporter,
    i.channel,
    i.ticket_no,
    i.type_of_event,
    i.severity,
    i.status,
    i.responsible_person,
    i.created_at,
    u.username AS created_by
FROM incidents i
LEFT JOIN users u ON i.created_by = u.id
ORDER BY i.created_at DESC
LIMIT 10";

$recent_stmt = $pdo->prepare($recent_incidents_query);
$recent_stmt->execute();
$recent_incidents = $recent_stmt->fetchAll(PDO::FETCH_ASSOC);

// Include header
require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-tachometer-alt text-primary"></i>
                Dashboard - Cybersecurity Incident Management
            </h1>
        </div>
    </div>

    <!-- Current Year Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i>
                        Incident Summary for <?php echo $current_year; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Total Incidents -->
                        <div class="col-md-2">
                            <div class="card border-info mb-3">
                                <div class="card-header bg-info text-white text-center">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="card-body text-center">
                                    <h3 class="card-title text-info"><?php echo $incident_stats['total_incidents'] ?: 0; ?></h3>
                                    <p class="card-text small">Total Incidents</p>
                                </div>
                            </div>
                        </div>

                        <!-- Open Incidents -->
                        <div class="col-md-2">
                            <div class="card border-danger mb-3">
                                <div class="card-header bg-danger text-white text-center">
                                    <i class="fas fa-folder-open"></i>
                                </div>
                                <div class="card-body text-center">
                                    <h3 class="card-title text-danger"><?php echo $incident_stats['open_incidents'] ?: 0; ?></h3>
                                    <p class="card-text small">Open</p>
                                </div>
                            </div>
                        </div>

                        <!-- In Progress -->
                        <div class="col-md-2">
                            <div class="card border-primary mb-3">
                                <div class="card-header bg-primary text-white text-center">
                                    <i class="fas fa-sync"></i>
                                </div>
                                <div class="card-body text-center">
                                    <h3 class="card-title text-primary"><?php echo $incident_stats['in_progress_incidents'] ?: 0; ?></h3>
                                    <p class="card-text small">In Progress</p>
                                </div>
                            </div>
                        </div>

                        <!-- Under Investigation -->
                        <div class="col-md-2">
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-dark text-center">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="card-body text-center">
                                    <h3 class="card-title text-warning"><?php echo $incident_stats['investigating_incidents'] ?: 0; ?></h3>
                                    <p class="card-text small">Investigating</p>
                                </div>
                            </div>
                        </div>

                        <!-- Resolved -->
                        <div class="col-md-2">
                            <div class="card border-info mb-3">
                                <div class="card-header bg-info text-white text-center">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="card-body text-center">
                                    <h3 class="card-title text-info"><?php echo $incident_stats['resolved_incidents'] ?: 0; ?></h3>
                                    <p class="card-text small">Resolved</p>
                                </div>
                            </div>
                        </div>

                        <!-- Closed -->
                        <div class="col-md-2">
                            <div class="card border-success mb-3">
                                <div class="card-header bg-success text-white text-center">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="card-body text-center">
                                    <h3 class="card-title text-success"><?php echo $incident_stats['closed_incidents'] ?: 0; ?></h3>
                                    <p class="card-text small">Closed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Severity Summary -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-circle"></i>
                        Incident Severity Breakdown (<?php echo $current_year; ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Critical -->
                        <div class="col-md-3">
                            <div class="card border-danger mb-3">
                                <div class="card-header bg-danger text-white text-center">
                                    <i class="fas fa-fire"></i> Critical
                                </div>
                                <div class="card-body text-center">
                                    <h2 class="card-title text-danger"><?php echo $incident_stats['critical_incidents'] ?: 0; ?></h2>
                                    <p class="card-text small">Immediate attention required</p>
                                </div>
                            </div>
                        </div>

                        <!-- High -->
                        <div class="col-md-3">
                            <div class="card border-warning mb-3">
                                <div class="card-header bg-warning text-dark text-center">
                                    <i class="fas fa-exclamation"></i> High
                                </div>
                                <div class="card-body text-center">
                                    <h2 class="card-title text-warning"><?php echo $incident_stats['high_incidents'] ?: 0; ?></h2>
                                    <p class="card-text small">High priority incidents</p>
                                </div>
                            </div>
                        </div>

                        <!-- Medium -->
                        <div class="col-md-3">
                            <div class="card border-info mb-3">
                                <div class="card-header bg-info text-white text-center">
                                    <i class="fas fa-minus"></i> Medium
                                </div>
                                <div class="card-body text-center">
                                    <h2 class="card-title text-info"><?php echo $incident_stats['medium_incidents'] ?: 0; ?></h2>
                                    <p class="card-text small">Medium priority incidents</p>
                                </div>
                            </div>
                        </div>

                        <!-- Low -->
                        <div class="col-md-3">
                            <div class="card border-success mb-3">
                                <div class="card-header bg-success text-white text-center">
                                    <i class="fas fa-check"></i> Low
                                </div>
                                <div class="card-body text-center">
                                    <h2 class="card-title text-success"><?php echo $incident_stats['low_incidents'] ?: 0; ?></h2>
                                    <p class="card-text small">Low priority incidents</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Incidents -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-clock"></i>
                        Recent Incidents (Last 10)
                    </h5>
                    <div>
                        <a href="<?=BASE_URL?>/modules/incidents/list.php" class="btn btn-light btn-sm">
                            <i class="fas fa-list"></i> View All
                        </a>
                        <a href="<?=BASE_URL?>/modules/incidents/create.php" class="btn btn-danger btn-sm">
                            <i class="fas fa-plus"></i> Create New
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($recent_incidents)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0 small">
                            <thead class="thead-dark">
                                <tr>
                                    <th style="min-width: 120px;">Incident No.</th>
                                    <th style="min-width: 130px;">Event Date</th>
                                    <th style="min-width: 120px;">Reporter</th>
                                    <th style="min-width: 100px;">Channel</th>
                                    <th style="min-width: 100px;">Ticket No.</th>
                                    <th style="min-width: 150px;">Type of Event</th>
                                    <th style="min-width: 100px;">Severity</th>
                                    <th style="min-width: 100px;">Status</th>
                                    <th style="min-width: 130px;">Responsible Person</th>
                                    <th style="min-width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_incidents as $incident): ?>
                                    <tr class="<?php echo getIncidentRowClass($incident['severity'], $incident['status']); ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($incident['incident_no']); ?></strong>
                                        </td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($incident['event_date'])); ?></td>
                                        <td><?php echo htmlspecialchars($incident['reporter']); ?></td>
                                        <td><?php echo htmlspecialchars($incident['channel']); ?></td>
                                        <td><?php echo htmlspecialchars($incident['ticket_no'] ?: '-'); ?></td>
                                        <td><?php echo htmlspecialchars($incident['type_of_event']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php echo getSeverityBadgeClass($incident['severity']); ?>">
                                                <?php echo htmlspecialchars($incident['severity']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo getIncidentStatusBadgeClass($incident['status']); ?>">
                                                <?php echo htmlspecialchars($incident['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($incident['responsible_person']); ?></td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="<?=BASE_URL?>/modules/incidents/view.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>"
                                                   class="btn btn-info btn-sm" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?=BASE_URL?>/modules/incidents/edit.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>"
                                                   class="btn btn-warning btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No incidents found</h5>
                        <p class="text-muted">No cybersecurity incidents have been reported yet.</p>
                        <a href="<?=BASE_URL?>/modules/incidents/create.php" class="btn btn-danger">
                            <i class="fas fa-plus"></i> Create First Incident Report
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="<?=BASE_URL?>/modules/incidents/create.php" class="btn btn-danger btn-block btn-lg">
                                <i class="fas fa-plus-circle"></i><br>
                                <span>Create Incident</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?=BASE_URL?>/modules/incidents/list.php" class="btn btn-info btn-block btn-lg">
                                <i class="fas fa-list"></i><br>
                                <span>View All Incidents</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?=BASE_URL?>/modules/tickets/list.php" class="btn btn-primary btn-block btn-lg">
                                <i class="fas fa-ticket-alt"></i><br>
                                <span>View Tickets</span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="<?=BASE_URL?>/modules/reports/index.php" class="btn btn-success btn-block btn-lg">
                                <i class="fas fa-chart-bar"></i><br>
                                <span>View Reports</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Dashboard specific styles */
.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.card-body {
    border-radius: 0 0 10px 10px;
}

.table th {
    background-color: #343a40;
    color: white;
    border-color: #454d55;
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.5rem 0.3rem;
    vertical-align: middle;
}

.table td {
    padding: 0.4rem 0.3rem;
    vertical-align: middle;
    font-size: 0.8rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,.075);
    transition: background-color 0.2s ease;
}

.badge {
    font-size: 0.7rem;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
}

/* Quick action buttons */
.btn-block.btn-lg {
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.btn-block.btn-lg i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.btn-block.btn-lg span {
    display: block;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-2, .col-md-3 {
        margin-bottom: 1rem;
    }

    .btn-block.btn-lg {
        padding: 1rem;
    }

    .btn-block.btn-lg i {
        font-size: 1.5rem;
    }
}
</style>
<?php
// Include main script loader
// This is where you can include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
// Include footer
require_once '../../includes/footer.php';
