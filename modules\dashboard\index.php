<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';


// Include header
require_once '../../includes/header.php';
?>

<div class="container">
    <h1>Dashboard</h1>
    <div class="row">
        <div class="col-md-3">
            <div class="card border-<?=getStatusBadgeClass('Open')?> mb-3">
                <div class="card-header bg-<?=getStatusBadgeClass('Open')?> text-white">
                    <i class="fas fa-folder-open"></i> Open Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $openTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets กำลังเปิดอยู่</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-<?=getStatusBadgeClass('In Progress')?> mb-3">
                <div class="card-header bg-<?=getStatusBadgeClass('In Progress')?> text-white">
                    <i class="fas fa-sync"></i> In Progress Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $inProgressTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets กำลังดำเนินการอยู่</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-<?=getStatusBadgeClass('Pending')?> mb-3">
                <div class="card-header bg-<?=getStatusBadgeClass('Pending')?> text-white">
                    <i class="fas fa-clock"></i> Pending Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $pendingTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets รอเนินการ</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-<?=getStatusBadgeClass('Closed')?> mb-3">
                <div class="card-header bg-<?=getStatusBadgeClass('Closed')?> text-white">
                    <i class="fas fa-check-circle"></i> Closed Tickets
                </div>
                <div class="card-body">
                    <h3 class="card-title text-center"><?php echo $closedTickets; ?></h3>
                    <p class="card-text small">จำนวน Tickets ที่ดำเนินการเสร็จแล้ว</p>
                </div>
            </div>
        </div>
    </div>
    <br>
    <div class="container-fluid p-1 d-flex align-items-center justify-content-center mt-5">
        <h3 class="text-primary">Recent Tickets</h3>
        <span class="badge rounded-pill bg-success ms-3 text-white">(Last 10)</span>
    </div>
   

    <style>
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
        transition: background-color 0.2s ease;
    }
    </style>
</div>
<?php
// Include main script loader
// This is where you can include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
// Include footer
require_once '../../includes/footer.php';
