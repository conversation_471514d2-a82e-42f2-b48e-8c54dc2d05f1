<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-01-01'); // Default to start of current year
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Default to today
$status_filter = $_GET['status'] ?? '';
$severity_filter = $_GET['severity'] ?? '';
$type_filter = $_GET['type'] ?? '';
$report_type = $_GET['report_type'] ?? 'summary';

// Build where conditions for filtering
$where_conditions = ["DATE(i.created_at) BETWEEN :date_from AND :date_to"];
$params = [':date_from' => $date_from, ':date_to' => $date_to];

if (!empty($status_filter)) {
    $where_conditions[] = "i.status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($severity_filter)) {
    $where_conditions[] = "i.severity = :severity";
    $params[':severity'] = $severity_filter;
}

if (!empty($type_filter)) {
    $where_conditions[] = "i.type_of_event = :type";
    $params[':type'] = $type_filter;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get summary statistics
$summary_query = "SELECT
    COUNT(*) as total_incidents,
    SUM(CASE WHEN status = 'Open' THEN 1 ELSE 0 END) as open_count,
    SUM(CASE WHEN status = 'In Progress' THEN 1 ELSE 0 END) as in_progress_count,
    SUM(CASE WHEN status = 'Under Investigation' THEN 1 ELSE 0 END) as investigating_count,
    SUM(CASE WHEN status = 'Resolved' THEN 1 ELSE 0 END) as resolved_count,
    SUM(CASE WHEN status = 'Closed' THEN 1 ELSE 0 END) as closed_count,
    SUM(CASE WHEN severity = 'Critical' THEN 1 ELSE 0 END) as critical_count,
    SUM(CASE WHEN severity = 'High' THEN 1 ELSE 0 END) as high_count,
    SUM(CASE WHEN severity = 'Medium' THEN 1 ELSE 0 END) as medium_count,
    SUM(CASE WHEN severity = 'Low' THEN 1 ELSE 0 END) as low_count
FROM incidents i
$where_clause";

$summary_stmt = $pdo->prepare($summary_query);
$summary_stmt->execute($params);
$summary_stats = $summary_stmt->fetch(PDO::FETCH_ASSOC);

// Get detailed incident data
$detail_query = "SELECT
    i.incident_no,
    i.event_date,
    i.reporter,
    i.channel,
    i.ticket_no,
    i.is_ever_happened,
    i.incident_ref_no,
    i.type_of_event,
    i.event_detail,
    i.effect,
    i.preliminary_operations,
    i.severity,
    i.attach_files,
    i.responsible_person,
    i.editing_details,
    i.date_of_completion,
    i.investigation_summary,
    i.future_preventive_measures,
    i.approver,
    i.approval_date,
    i.status,
    i.created_at,
    u.username AS created_by
FROM incidents i
LEFT JOIN users u ON i.created_by = u.id
$where_clause
ORDER BY i.created_at DESC";

$detail_stmt = $pdo->prepare($detail_query);
$detail_stmt->execute($params);
$incidents = $detail_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get monthly trend data
$trend_query = "SELECT
    DATE_FORMAT(created_at, '%Y-%m') as month,
    COUNT(*) as incident_count,
    SUM(CASE WHEN severity = 'Critical' THEN 1 ELSE 0 END) as critical_count,
    SUM(CASE WHEN severity = 'High' THEN 1 ELSE 0 END) as high_count
FROM incidents i
$where_clause
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY month DESC
LIMIT 12";

$trend_stmt = $pdo->prepare($trend_query);
$trend_stmt->execute($params);
$trend_data = $trend_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get incident type breakdown
$type_query = "SELECT
    type_of_event,
    COUNT(*) as count,
    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM incidents i2 $where_clause)), 2) as percentage
FROM incidents i
$where_clause
GROUP BY type_of_event
ORDER BY count DESC";

$type_stmt = $pdo->prepare($type_query);
$type_stmt->execute($params);
$type_breakdown = $type_stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-chart-bar text-primary"></i>
                Cybersecurity Incident Reports
            </h1>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-filter"></i> Report Filters
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row">
                        <div class="col-md-2">
                            <label for="date_from">From Date:</label>
                            <input type="date" class="form-control" name="date_from" id="date_from"
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to">To Date:</label>
                            <input type="date" class="form-control" name="date_to" id="date_to"
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="status">Status:</label>
                            <select class="form-control" name="status" id="status">
                                <option value="">All Statuses</option>
                                <?php foreach (getIncidentStatuses() as $status): ?>
                                    <option value="<?php echo $status; ?>" <?php echo ($status_filter == $status) ? 'selected' : ''; ?>>
                                        <?php echo $status; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="severity">Severity:</label>
                            <select class="form-control" name="severity" id="severity">
                                <option value="">All Severities</option>
                                <?php foreach (getIncidentSeverities() as $severity): ?>
                                    <option value="<?php echo $severity; ?>" <?php echo ($severity_filter == $severity) ? 'selected' : ''; ?>>
                                        <?php echo $severity; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="type">Type:</label>
                            <select class="form-control" name="type" id="type">
                                <option value="">All Types</option>
                                <?php foreach (getIncidentTypes() as $type): ?>
                                    <option value="<?php echo $type; ?>" <?php echo ($type_filter == $type) ? 'selected' : ''; ?>>
                                        <?php echo $type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i> Generate Report
                                </button>
                            </div>
                        </div>
                    </form>

                    <?php if (!empty($status_filter) || !empty($severity_filter) || !empty($type_filter) || $date_from != date('Y-01-01') || $date_to != date('Y-m-d')): ?>
                        <div class="mt-3">
                            <a href="index.php" class="btn btn-secondary btn-sm">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i>
                        Summary Statistics (<?php echo date('d/m/Y', strtotime($date_from)); ?> - <?php echo date('d/m/Y', strtotime($date_to)); ?>)
                    </h5>
                    <div>
                        <button class="btn btn-outline-light btn-sm" onclick="exportToExcel('summaryTable')">
                            <i class="fas fa-download"></i> Export Summary
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Total Incidents -->
                        <div class="col-md-2">
                            <div class="card border-info text-center">
                                <div class="card-body">
                                    <h2 class="text-info"><?php echo $summary_stats['total_incidents'] ?: 0; ?></h2>
                                    <p class="card-text">Total Incidents</p>
                                </div>
                            </div>
                        </div>

                        <!-- Status Breakdown -->
                        <div class="col-md-2">
                            <div class="card border-danger text-center">
                                <div class="card-body">
                                    <h3 class="text-danger"><?php echo $summary_stats['open_count'] ?: 0; ?></h3>
                                    <p class="card-text small">Open</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card border-primary text-center">
                                <div class="card-body">
                                    <h3 class="text-primary"><?php echo $summary_stats['in_progress_count'] ?: 0; ?></h3>
                                    <p class="card-text small">In Progress</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card border-warning text-center">
                                <div class="card-body">
                                    <h3 class="text-warning"><?php echo $summary_stats['investigating_count'] ?: 0; ?></h3>
                                    <p class="card-text small">Investigating</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card border-info text-center">
                                <div class="card-body">
                                    <h3 class="text-info"><?php echo $summary_stats['resolved_count'] ?: 0; ?></h3>
                                    <p class="card-text small">Resolved</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card border-success text-center">
                                <div class="card-body">
                                    <h3 class="text-success"><?php echo $summary_stats['closed_count'] ?: 0; ?></h3>
                                    <p class="card-text small">Closed</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Severity Breakdown -->
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="card border-danger text-center">
                                <div class="card-header bg-danger text-white">
                                    <i class="fas fa-fire"></i> Critical
                                </div>
                                <div class="card-body">
                                    <h2 class="text-danger"><?php echo $summary_stats['critical_count'] ?: 0; ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-warning text-center">
                                <div class="card-header bg-warning text-dark">
                                    <i class="fas fa-exclamation"></i> High
                                </div>
                                <div class="card-body">
                                    <h2 class="text-warning"><?php echo $summary_stats['high_count'] ?: 0; ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-info text-center">
                                <div class="card-header bg-info text-white">
                                    <i class="fas fa-minus"></i> Medium
                                </div>
                                <div class="card-body">
                                    <h2 class="text-info"><?php echo $summary_stats['medium_count'] ?: 0; ?></h2>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card border-success text-center">
                                <div class="card-header bg-success text-white">
                                    <i class="fas fa-check"></i> Low
                                </div>
                                <div class="card-body">
                                    <h2 class="text-success"><?php echo $summary_stats['low_count'] ?: 0; ?></h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Trend Analysis and Type Breakdown -->
    <div class="row mb-4">
        <!-- Monthly Trend -->
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i>
                        Monthly Trend (Last 12 Months)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($trend_data)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Month</th>
                                        <th>Total</th>
                                        <th>Critical</th>
                                        <th>High</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($trend_data as $trend): ?>
                                        <tr>
                                            <td><?php echo date('M Y', strtotime($trend['month'] . '-01')); ?></td>
                                            <td><span class="badge badge-info"><?php echo $trend['incident_count']; ?></span></td>
                                            <td><span class="badge badge-danger"><?php echo $trend['critical_count']; ?></span></td>
                                            <td><span class="badge badge-warning"><?php echo $trend['high_count']; ?></span></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No trend data available for the selected period.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Incident Type Breakdown -->
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i>
                        Incident Type Breakdown
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($type_breakdown)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Type of Event</th>
                                        <th>Count</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($type_breakdown as $type): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($type['type_of_event']); ?></td>
                                            <td><span class="badge badge-primary"><?php echo $type['count']; ?></span></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar"
                                                         style="width: <?php echo $type['percentage']; ?>%">
                                                        <?php echo $type['percentage']; ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">No incident type data available for the selected period.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Incident Report -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i>
                        Detailed Incident Report (<?php echo count($incidents); ?> records)
                    </h5>
                    <div>
                        <button class="btn btn-outline-light btn-sm" onclick="exportToExcel('detailTable')">
                            <i class="fas fa-download"></i> Export Details
                        </button>
                        <button class="btn btn-outline-light btn-sm" onclick="printReport()">
                            <i class="fas fa-print"></i> Print
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (!empty($incidents)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped mb-0 small" id="detailTable">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Incident No.</th>
                                        <th>Event Date</th>
                                        <th>Reporter</th>
                                        <th>Channel</th>
                                        <th>Ticket No.</th>
                                        <th>Ever Happened</th>
                                        <th>Incident Ref.No</th>
                                        <th>Type of Event</th>
                                        <th>Event Detail</th>
                                        <th>Effect</th>
                                        <th>Preliminary Operations</th>
                                        <th>Severity</th>
                                        <th>Attach Files</th>
                                        <th>Responsible Person</th>
                                        <th>Editing Details</th>
                                        <th>Date of Completion</th>
                                        <th>Investigation Summary</th>
                                        <th>Future Preventive Measures</th>
                                        <th>Approver</th>
                                        <th>Approval Date</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($incidents as $incident): ?>
                                        <tr class="<?php echo getIncidentRowClass($incident['severity'], $incident['status']); ?>">
                                            <td><strong><?php echo htmlspecialchars($incident['incident_no']); ?></strong></td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($incident['event_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($incident['reporter']); ?></td>
                                            <td><?php echo htmlspecialchars($incident['channel']); ?></td>
                                            <td><?php echo htmlspecialchars($incident['ticket_no'] ?: '-'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo $incident['is_ever_happened'] ? 'warning' : 'success'; ?>">
                                                    <?php echo $incident['is_ever_happened'] ? 'Yes' : 'No'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($incident['incident_ref_no'] ?: '-'); ?></td>
                                            <td><?php echo htmlspecialchars($incident['type_of_event']); ?></td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['event_detail']), 100); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="max-width: 150px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['effect'] ?: '-'), 80); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="max-width: 150px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['preliminary_operations'] ?: '-'), 80); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php echo getSeverityBadgeClass($incident['severity']); ?>">
                                                    <?php echo htmlspecialchars($incident['severity']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($incident['attach_files']): ?>
                                                    <i class="fas fa-paperclip text-info" title="Has attachments"></i>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($incident['responsible_person']); ?></td>
                                            <td>
                                                <div style="max-width: 150px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['editing_details'] ?: '-'), 80); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php echo $incident['date_of_completion'] ? date('d/m/Y H:i', strtotime($incident['date_of_completion'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['investigation_summary'] ?: '-'), 100); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="max-width: 200px; overflow: hidden;">
                                                    <?php echo text_to_readmore(htmlspecialchars($incident['future_preventive_measures'] ?: '-'), 100); ?>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($incident['approver'] ?: '-'); ?></td>
                                            <td>
                                                <?php echo $incident['approval_date'] ? date('d/m/Y H:i', strtotime($incident['approval_date'])) : '-'; ?>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php echo getIncidentStatusBadgeClass($incident['status']); ?>">
                                                    <?php echo htmlspecialchars($incident['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($incident['created_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No incidents found</h5>
                            <p class="text-muted">No incidents match the selected criteria for the specified date range.</p>
                            <a href="<?=BASE_URL?>/modules/incidents/create.php" class="btn btn-danger">
                                <i class="fas fa-plus"></i> Create New Incident
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
/* Report specific styles */
.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.table th {
    background-color: #343a40;
    color: white;
    border-color: #454d55;
    font-weight: 600;
    font-size: 0.8rem;
    padding: 0.4rem 0.3rem;
    vertical-align: middle;
    white-space: nowrap;
}

.table td {
    padding: 0.3rem 0.2rem;
    vertical-align: middle;
    font-size: 0.75rem;
}

.table-responsive {
    max-height: 60vh;
    overflow-y: auto;
}

/* Sticky header for detailed table */
#detailTable thead th {
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,.075);
}

.badge {
    font-size: 0.65rem;
}

.progress {
    background-color: #e9ecef;
}

.progress-bar {
    background-color: #007bff;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
}

/* Print styles */
@media print {
    .btn, .card-header .btn {
        display: none !important;
    }

    .card {
        border: 1px solid #dee2e6 !important;
        page-break-inside: avoid;
    }

    .table {
        font-size: 0.7rem;
    }

    .table th, .table td {
        padding: 0.2rem;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-2, .col-md-3, .col-md-6 {
        margin-bottom: 1rem;
    }

    .table th, .table td {
        font-size: 0.7rem;
        padding: 0.2rem;
    }
}
</style>

<script>
function exportToExcel(tableID) {
    let table = document.getElementById(tableID);
    if (!table) {
        // If no specific table, export summary data
        let summaryData = generateSummaryHTML();
        let url = 'data:application/vnd.ms-excel,' + encodeURIComponent(summaryData);
        let downloadLink = document.createElement("a");
        document.body.appendChild(downloadLink);
        downloadLink.href = url;
        downloadLink.download = 'incident_summary_report.xls';
        downloadLink.click();
        document.body.removeChild(downloadLink);
    } else {
        let html = table.outerHTML;
        let url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
        let downloadLink = document.createElement("a");
        document.body.appendChild(downloadLink);
        downloadLink.href = url;
        downloadLink.download = 'incident_detailed_report.xls';
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }
}

function generateSummaryHTML() {
    let dateFrom = '<?php echo $date_from; ?>';
    let dateTo = '<?php echo $date_to; ?>';
    let totalIncidents = '<?php echo $summary_stats['total_incidents'] ?: 0; ?>';
    let openCount = '<?php echo $summary_stats['open_count'] ?: 0; ?>';
    let inProgressCount = '<?php echo $summary_stats['in_progress_count'] ?: 0; ?>';
    let investigatingCount = '<?php echo $summary_stats['investigating_count'] ?: 0; ?>';
    let resolvedCount = '<?php echo $summary_stats['resolved_count'] ?: 0; ?>';
    let closedCount = '<?php echo $summary_stats['closed_count'] ?: 0; ?>';
    let criticalCount = '<?php echo $summary_stats['critical_count'] ?: 0; ?>';
    let highCount = '<?php echo $summary_stats['high_count'] ?: 0; ?>';
    let mediumCount = '<?php echo $summary_stats['medium_count'] ?: 0; ?>';
    let lowCount = '<?php echo $summary_stats['low_count'] ?: 0; ?>';

    return `
        <table border="1">
            <tr><th colspan="2">Cybersecurity Incident Summary Report</th></tr>
            <tr><td>Report Period</td><td>${dateFrom} to ${dateTo}</td></tr>
            <tr><td>Generated On</td><td>${new Date().toLocaleString()}</td></tr>
            <tr><th colspan="2">Summary Statistics</th></tr>
            <tr><td>Total Incidents</td><td>${totalIncidents}</td></tr>
            <tr><th colspan="2">Status Breakdown</th></tr>
            <tr><td>Open</td><td>${openCount}</td></tr>
            <tr><td>In Progress</td><td>${inProgressCount}</td></tr>
            <tr><td>Under Investigation</td><td>${investigatingCount}</td></tr>
            <tr><td>Resolved</td><td>${resolvedCount}</td></tr>
            <tr><td>Closed</td><td>${closedCount}</td></tr>
            <tr><th colspan="2">Severity Breakdown</th></tr>
            <tr><td>Critical</td><td>${criticalCount}</td></tr>
            <tr><td>High</td><td>${highCount}</td></tr>
            <tr><td>Medium</td><td>${mediumCount}</td></tr>
            <tr><td>Low</td><td>${lowCount}</td></tr>
        </table>
    `;
}

function printReport() {
    window.print();
}

// Auto-refresh functionality
function autoRefresh() {
    if (confirm('Do you want to refresh the report with current data?')) {
        location.reload();
    }
}

// Set up auto-refresh every 5 minutes (optional)
// setInterval(autoRefresh, 300000);

// Initialize tooltips
$(document).ready(function() {
    $('[title]').tooltip();

    // Add click handlers for quick date filters
    $('#quickToday').click(function() {
        let today = new Date().toISOString().split('T')[0];
        $('#date_from').val(today);
        $('#date_to').val(today);
    });

    $('#quickThisWeek').click(function() {
        let today = new Date();
        let firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
        let lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));
        $('#date_from').val(firstDay.toISOString().split('T')[0]);
        $('#date_to').val(lastDay.toISOString().split('T')[0]);
    });

    $('#quickThisMonth').click(function() {
        let today = new Date();
        let firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        let lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        $('#date_from').val(firstDay.toISOString().split('T')[0]);
        $('#date_to').val(lastDay.toISOString().split('T')[0]);
    });
});
</script>

<?php
require_once '../../includes/main_script_loader.php';
?>
<script src="<?=BASE_URL?>/assets/js/reports-ticket.js?v=<?=$timestamp?>"></script>
<?php
require_once '../../includes/footer.php';
?>
