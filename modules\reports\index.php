<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Replace the existing query with:
$query = "";

$result = $pdo->query($query);

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Reports</h4>
            <button class="btn btn-outline-light" onclick="exportToExcel('Report')">
                <i class="fas fa-download"></i> Export to Excel
            </button>
        </div>
        <div class="card-body">
           
        </div>
    </div>
</div>


<script>
function exportToExcel(tableID) {
    let table = document.getElementById(tableID);
    let html = table.outerHTML;
    let url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
    let downloadLink = document.createElement("a");
    document.body.appendChild(downloadLink);
    downloadLink.href = url;
    downloadLink.download = 'report.xls';
    downloadLink.click();
    document.body.removeChild(downloadLink);
}
</script>

<?php
require_once '../../includes/main_script_loader.php';
?>
<script src="<?=BASE_URL?>/assets/js/reports-ticket.js?v=<?=$timestamp?>"></script>
<?php
require_once '../../includes/footer.php';
?>
