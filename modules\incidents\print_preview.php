<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Get incident ID from URL
$incident_id = $_GET['incident_id'] ?? '';

if (empty($incident_id)) {
    header("Location: list.php");
    exit();
}

// Fetch incident details (same query as view.php)
$query = "SELECT i.*,
       u.username AS created_by_username,
       u.fullname AS created_by_name,
       u2.fullname AS approver_name,
       u3.fullname AS responsible_person_name
FROM incidents i
LEFT JOIN users u ON i.created_by = u.id
LEFT JOIN users u2 ON i.approver = u2.username
LEFT JOIN users u3 ON i.responsible_person = u3.username
WHERE i.incident_no = ?";

$stmt = $pdo->prepare($query);
$stmt->execute([$incident_id]);
$incident = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$incident) {
    header("Location: list.php?error=Incident not found");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= WEBTITLE ?></title>
    <link rel="icon" href="<?= BASE_URL ?>/assets/images/favicon.png" sizes="32x32">
    <link href="../../assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="../../assets/css/fontawesome.min.css" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 20mm 15mm;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            margin: 0;
            padding: 20px;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }

        .print-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #4e73df, #36b9cc, #1cc88a, #f6c23e, #e74a3b);
        }

        .print-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .print-header img {
            height: 60px;
            margin-right: 20px;
            border-radius: 8px;
            background: white;
            padding: 5px;
        }

        .print-header h1 {
            font-size: 22pt;
            font-weight: 700;
            margin: 0;
            flex-grow: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .print-header .report-meta {
            text-align: right;
            font-size: 10pt;
            opacity: 0.9;
        }

        .info-section {
            margin-bottom: 25px;
            page-break-inside: avoid;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .section-title {
            background: linear-gradient(135deg, #4e73df 0%, #36b9cc 100%);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 14pt;
            margin: 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
        }

        .section-content {
            padding: 20px;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .info-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
            transition: background-color 0.2s ease;
        }

        .info-table tr:last-child td {
            border-bottom: none;
        }

        .info-table td:first-child {
            font-weight: 600;
            width: 35%;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #495057;
            border-right: 2px solid #dee2e6;
        }

        .info-table td:last-child {
            background-color: #ffffff;
        }

        .text-content {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            white-space: pre-wrap;
            word-wrap: break-word;
            margin-bottom: 15px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
            font-family: 'Courier New', monospace;
            font-size: 10pt;
            line-height: 1.5;
        }

        .text-content h4 {
            color: #4e73df;
            font-size: 12pt;
            font-weight: 600;
            margin: 0 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 2px solid #e9ecef;
        }

        .badge {
            display: inline-block;
            padding: 6px 12px;
            font-size: 9pt;
            font-weight: 600;
            border-radius: 20px;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .badge-success {
            background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);
        }
        .badge-warning {
            background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);
            color: #2c3e50;
        }
        .badge-danger {
            background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
        }
        .badge-info {
            background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
        }
        .badge-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        .badge-primary {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .column {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .print-controls {
            text-align: center;
            margin: 30px 0;
            page-break-inside: avoid;
        }

        .btn {
            padding: 12px 24px;
            margin: 0 8px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-weight: 600;
            font-size: 11pt;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
            color: white;
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .severity-critical { border-left: 5px solid #e74a3b; }
        .severity-high { border-left: 5px solid #f6c23e; }
        .severity-medium { border-left: 5px solid #36b9cc; }
        .severity-low { border-left: 5px solid #1cc88a; }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 10pt;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 72pt;
            color: rgba(0,0,0,0.03);
            font-weight: 900;
            z-index: -1;
            pointer-events: none;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }

            .print-controls {
                display: none !important;
            }

            .print-container {
                max-width: none;
                margin: 0;
                padding: 20px;
                box-shadow: none;
                border-radius: 0;
            }

            .info-section {
                box-shadow: none;
                border: 1px solid #ddd;
            }

            .btn {
                display: none;
            }

            .watermark {
                display: block;
            }
        }

        @media screen and (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .print-container {
                padding: 15px;
                margin: 10px;
            }

            .print-header {
                flex-direction: column;
                text-align: center;
            }

            .print-header img {
                margin: 0 0 15px 0;
            }
        }
    </style>
</head>
<body>
    <!-- Watermark for print -->
    <div class="watermark">CONFIDENTIAL</div>

    <div class="print-container severity-<?php echo strtolower($incident['severity']); ?>">
        <!-- Print Controls -->
        <div class="print-controls">
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print"></i> Print Report
            </button>
            <a href="view.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to View
            </a>
        </div>

        <!-- Header -->
        <div class="print-header">
            <img src="../../assets/images/company brand.png" alt="Company Logo">
            <div>
                <h1>Cybersecurity Incident Report</h1>
                <div class="report-meta">
                    <div><strong><?php echo htmlspecialchars($incident['incident_no']); ?></strong></div>
                    <div>Generated: <?php echo date('d/m/Y H:i'); ?></div>
                </div>
            </div>
            <div class="status-indicator badge-<?php echo getIncidentStatusBadgeClass($incident['status']); ?>">
                <?php echo htmlspecialchars($incident['status']); ?>
            </div>
        </div>

        <!-- Basic Information and Classification -->
        <div class="two-column">
            <div class="column">
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-info-circle"></i> Basic Information
                    </div>
                    <div class="section-content">
                        <table class="info-table">
                            <tr>
                                <td><i class="fas fa-hashtag"></i> Incident No.:</td>
                                <td><strong><?php echo htmlspecialchars($incident['incident_no']); ?></strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-calendar-alt"></i> Event Date:</td>
                                <td><?php echo date('d/m/Y H:i', strtotime($incident['event_date'])); ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-user"></i> Reporter:</td>
                                <td><?php echo htmlspecialchars($incident['reporter']); ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-phone"></i> Channel:</td>
                                <td><?php echo htmlspecialchars($incident['channel']); ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-ticket-alt"></i> Ticket No.:</td>
                                <td><?php echo $incident['ticket_no'] ?: '<em>N/A</em>'; ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-history"></i> Ever Happened Before:</td>
                                <td>
                                    <span class="badge badge-<?php echo $incident['is_ever_happened'] ? 'warning' : 'success'; ?>">
                                        <?php echo $incident['is_ever_happened'] ? 'Yes' : 'No'; ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-link"></i> Incident Ref. No.:</td>
                                <td><?php echo htmlspecialchars($incident['incident_ref_no'] ?: 'N/A'); ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-flag"></i> Current Status:</td>
                                <td>
                                    <span class="badge badge-<?php echo getIncidentStatusBadgeClass($incident['status']); ?>">
                                        <?php echo htmlspecialchars($incident['status']); ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="column">
                <div class="info-section">
                    <div class="section-title">
                        <i class="fas fa-tags"></i> Incident Classification
                    </div>
                    <div class="section-content">
                        <table class="info-table">
                            <tr>
                                <td><i class="fas fa-exclamation-triangle"></i> Type of Event:</td>
                                <td><strong><?php echo htmlspecialchars($incident['type_of_event']); ?></strong></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-thermometer-half"></i> Severity Level:</td>
                                <td>
                                    <span class="badge badge-<?php echo getSeverityBadgeClass($incident['severity']); ?>">
                                        <?php echo htmlspecialchars($incident['severity']); ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-user-shield"></i> Responsible Person:</td>
                                <td><?php echo htmlspecialchars($incident['responsible_person_name'] ?: $incident['responsible_person']); ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-user-plus"></i> Created By:</td>
                                <td><?php echo htmlspecialchars($incident['created_by_name'] ?: $incident['created_by_username']); ?></td>
                            </tr>
                            <tr>
                                <td><i class="fas fa-clock"></i> Created At:</td>
                                <td><?php echo date('d/m/Y H:i', strtotime($incident['created_at'])); ?></td>
                            </tr>
                            <?php if ($incident['updated_at']): ?>
                            <tr>
                                <td><i class="fas fa-edit"></i> Last Updated:</td>
                                <td><?php echo date('d/m/Y H:i', strtotime($incident['updated_at'])); ?></td>
                            </tr>
                            <?php endif; ?>
                            <?php if ($incident['attach_files']): ?>
                            <tr>
                                <td><i class="fas fa-paperclip"></i> Attachments:</td>
                                <td><span class="badge badge-info">Available</span></td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Details -->
        <div class="info-section">
            <div class="section-title">
                <i class="fas fa-file-alt"></i> Event Details
            </div>
            <div class="section-content">
                <div class="text-content">
                    <h4><i class="fas fa-info-circle"></i> Event Detail</h4>
                    <?php echo nl2br(htmlspecialchars($incident['event_detail'])); ?>
                </div>

                <?php if ($incident['effect']): ?>
                <div class="text-content">
                    <h4><i class="fas fa-impact"></i> Impact & Effect</h4>
                    <?php echo nl2br(htmlspecialchars($incident['effect'])); ?>
                </div>
                <?php endif; ?>

                <?php if ($incident['preliminary_operations']): ?>
                <div class="text-content">
                    <h4><i class="fas fa-tools"></i> Preliminary Operations</h4>
                    <?php echo nl2br(htmlspecialchars($incident['preliminary_operations'])); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Investigation and Resolution -->
        <div class="info-section">
            <div class="section-title">
                <i class="fas fa-search"></i> Investigation & Resolution
            </div>
            <div class="section-content">
                <?php if ($incident['editing_details']): ?>
                <div class="text-content">
                    <h4><i class="fas fa-edit"></i> Additional Details</h4>
                    <?php echo nl2br(htmlspecialchars($incident['editing_details'])); ?>
                </div>
                <?php endif; ?>

                <?php if ($incident['date_of_completion']): ?>
                <div class="text-content">
                    <h4><i class="fas fa-check-circle"></i> Completion Date</h4>
                    <strong><?php echo date('d/m/Y H:i', strtotime($incident['date_of_completion'])); ?></strong>
                </div>
                <?php endif; ?>

                <?php if ($incident['investigation_summary']): ?>
                <div class="text-content">
                    <h4><i class="fas fa-microscope"></i> Investigation Summary</h4>
                    <?php echo nl2br(htmlspecialchars($incident['investigation_summary'])); ?>
                </div>
                <?php endif; ?>

                <?php if ($incident['future_preventive_measures']): ?>
                <div class="text-content">
                    <h4><i class="fas fa-shield-alt"></i> Future Preventive Measures</h4>
                    <?php echo nl2br(htmlspecialchars($incident['future_preventive_measures'])); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Approval Information -->
        <?php if ($incident['approver'] || $incident['approval_date']): ?>
        <div class="info-section">
            <div class="section-title">
                <i class="fas fa-stamp"></i> Approval Information
            </div>
            <div class="section-content">
                <table class="info-table">
                    <?php if ($incident['approver']): ?>
                    <tr>
                        <td><i class="fas fa-user-check"></i> Approver:</td>
                        <td><strong><?php echo htmlspecialchars($incident['approver_name'] ?: $incident['approver']); ?></strong></td>
                    </tr>
                    <?php endif; ?>
                    <?php if ($incident['approval_date']): ?>
                    <tr>
                        <td><i class="fas fa-calendar-check"></i> Approval Date:</td>
                        <td><strong><?php echo date('d/m/Y H:i', strtotime($incident['approval_date'])); ?></strong></td>
                    </tr>
                    <?php endif; ?>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- Report Footer -->
        <div class="info-section" style="margin-top: 40px;">
            <div class="section-title">
                <i class="fas fa-info"></i> Report Information
            </div>
            <div class="section-content">
                <table class="info-table">
                    <tr>
                        <td><i class="fas fa-calendar"></i> Report Generated:</td>
                        <td><strong><?php echo date('d/m/Y H:i:s'); ?></strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-user"></i> Generated By:</td>
                        <td><strong><?php echo htmlspecialchars($_SESSION['userdata']['username'] ?? 'System'); ?></strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-building"></i> Organization:</td>
                        <td><strong>CS Incident Management System</strong></td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-lock"></i> Classification:</td>
                        <td><span class="badge badge-warning">CONFIDENTIAL</span></td>
                    </tr>
                </table>

                <div style="margin-top: 20px; padding: 15px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 8px; border-left: 4px solid #4e73df;">
                    <p style="margin: 0; font-size: 10pt; color: #6c757d; text-align: center;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>CONFIDENTIAL DOCUMENT</strong> - This incident report contains sensitive security information.
                        Distribution should be limited to authorized personnel only.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced print preview functionality
        window.addEventListener('load', function() {
            // Add loading animation
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);

            // Optional: Auto-print when page loads with URL parameter
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auto_print') === 'true') {
                setTimeout(() => {
                    window.print();
                }, 1000);
            }
        });

        // Enhanced keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+P or Cmd+P for print
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'p') {
                e.preventDefault();
                window.print();
            }

            // Escape key to go back
            if (e.key === 'Escape') {
                window.history.back();
            }

            // Ctrl+S or Cmd+S to save as PDF (browser dependent)
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 's') {
                e.preventDefault();
                window.print();
            }
        });

        // Print event handlers
        window.addEventListener('beforeprint', function() {
            // Add print timestamp
            const printTime = new Date().toLocaleString();
            console.log('Report printed at:', printTime);

            // You could send analytics here
            // trackPrintEvent('incident_report', '<?php echo $incident['incident_no']; ?>');
        });

        window.addEventListener('afterprint', function() {
            // Optional: Show confirmation or redirect
            console.log('Print dialog closed');
        });

        // Add smooth scrolling for better UX
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add copy functionality for incident number
        function copyIncidentNumber() {
            const incidentNo = '<?php echo $incident['incident_no']; ?>';
            if (navigator.clipboard) {
                navigator.clipboard.writeText(incidentNo).then(() => {
                    showToast('Incident number copied to clipboard!');
                });
            }
        }

        // Simple toast notification
        function showToast(message) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4e73df;
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                opacity: 0;
                transform: translateY(-20px);
                transition: all 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateY(0)';
            }, 100);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }

        // Add click handler to incident number for copying
        document.addEventListener('DOMContentLoaded', function() {
            const incidentNumbers = document.querySelectorAll('strong');
            incidentNumbers.forEach(el => {
                if (el.textContent.includes('<?php echo substr($incident['incident_no'], 0, 3); ?>')) {
                    el.style.cursor = 'pointer';
                    el.title = 'Click to copy incident number';
                    el.addEventListener('click', copyIncidentNumber);
                }
            });
        });
    </script>
</body>
</html>