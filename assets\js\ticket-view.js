
// Tab handling
$(document).ready(function() {
    // Check if there's a stored active tab
    let activeTab = localStorage.getItem('ticketActiveTab');
    
    if (activeTab) {
        // Activate the stored tab
        $('#ticketActionTabs a[href="' + activeTab + '"]').tab('show');
    }
    
    // Store the active tab when changed
    $('#ticketActionTabs a').on('shown.bs.tab', function(e) {
        localStorage.setItem('ticketActiveTab', $(e.target).attr('href'));
    });
    
    // Clear active tab when leaving the page
    $(window).on('beforeunload', function() {
        localStorage.removeItem('ticketActiveTab');
    });
    
    // Switch to forward tab if there's a forward error
    if (window.location.href.includes('forward_error')) {
        $('#ticketActionTabs a[href="#forward-content"]').tab('show');
    }
     // Initialize help note
    $("#helpNote").draggable({
        handle: "#helpDragHandle",
        containment: "window",
        snap: "window",
        snapMode: "outer"
    });

    // Minimize/Maximize functionality
    $("#minimizeHelp").click(function() {
        $("#helpContent").slideToggle();
        $(this).find('i').toggleClass('fa-plus fa-minus');
        
        // Save state
        localStorage.setItem('helpMinimized', 
            $("#helpContent").is(':hidden'));
    });

    // Restore last state
    if (localStorage.getItem('helpMinimized') === 'true') {
        $("#helpContent").hide();
        $("#minimizeHelp").find('i').removeClass('fa-minus').addClass('fa-plus');
    }
    
    // Switch to interim tab if there's an interim update
    /*
    if (window.location.href.includes('interim_updated')) {
        $('#ticketActionTabs a[href="#interim-content"]').tab('show');
    }
    */
   
    // Initialize select2 for dropdown fields if available
    if ($.fn.select2) {
        $('#sim_operator, #sim_package').select2({
            theme: 'bootstrap4',
            placeholder: 'Select an option',
            allowClear: true
        });
    }
    
    // Copy to clipboard functionality for interim information
    $('.copy-btn').on('click', function() {
        var targetId = $(this).data('target');
        var targetElement = document.getElementById(targetId);
        
        if (targetElement) {
            // Create a temporary textarea element to copy from
            var textarea = document.createElement('textarea');
            textarea.value = targetElement.value;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            
            // Show success tooltip
            $(this).tooltip('hide')
                .attr('data-original-title', 'Copied!')
                .tooltip('show');
            
            // Reset tooltip after 1 second
            var btn = $(this);
            setTimeout(function() {
                btn.tooltip('hide')
                    .attr('data-original-title', 'Copy to clipboard');
            }, 1000);
        }
    });
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
