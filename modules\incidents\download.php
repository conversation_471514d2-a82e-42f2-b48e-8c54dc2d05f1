<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';

// Get file ID from URL
$file_id = $_GET['file_id'] ?? '';

if (empty($file_id) || !is_numeric($file_id)) {
    http_response_code(400);
    die('Invalid file ID');
}

try {
    // Fetch file details
    $query = "SELECT * FROM incident_attachments WHERE id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$file_id]);
    $file = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$file) {
        http_response_code(404);
        die('File not found');
    }

    // Check if file exists on disk
    if (!file_exists($file['file_path'])) {
        http_response_code(404);
        die('File not found on disk');
    }

    // Set headers for file download
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $file['file_name'] . '"');
    header('Content-Length: ' . filesize($file['file_path']));
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    // Output file content
    readfile($file['file_path']);
    exit();

} catch (PDOException $e) {
    http_response_code(500);
    die('Database error: ' . $e->getMessage());
}
?>
