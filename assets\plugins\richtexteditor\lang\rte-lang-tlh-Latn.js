﻿//Klingon (Latin) , Klingon (Latin)
RTE_DefaultConfig.text_language = "hol";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "qil";	//"Cancel"
RTE_DefaultConfig.text_normal = "motlh";	//"Normal"
RTE_DefaultConfig.text_h1 = "1 headline";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "headline 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "3 headline";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "4 headline";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "5 headline";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "6 headline";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "7 headline";	//"Headline 7"
RTE_DefaultConfig.text_close = "soq";	//"Close"
RTE_DefaultConfig.text_bold = "jaq";	//"Bold"
RTE_DefaultConfig.text_italic = "italic";	//"Italic"
RTE_DefaultConfig.text_underline = "underline";	//"Underline"
RTE_DefaultConfig.text_strike = "tlhegh mup";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "superscript";	//"Superscript"
RTE_DefaultConfig.text_subscript = "subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "upper case";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "ghuS case";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "Format teq";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "Link insert";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "mInDu'lIj Link";	//"Open Link"
RTE_DefaultConfig.text_editlink = "Link edit";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "Link teq";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "tlhegh 'ab";	//"Line Height"
RTE_DefaultConfig.text_indent = "indent";	//"Indent"
RTE_DefaultConfig.text_outdent = "outdent";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "bot Quote";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "ra' tetlh";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "unorder tetlh";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "SaS che' insert";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "Date insert";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "raS insert";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "ghItlhvam insert";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "Video insert";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "ngoq insert";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "pdf chenmoH";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "emoji insert";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "le' vuDmey'e'";	//"Special characters"
RTE_DefaultConfig.text_characters = "vuDmey'e'";	//"Characters"
RTE_DefaultConfig.text_fontname = "font";	//"Font"
RTE_DefaultConfig.text_fontsize = "size";	//"Size"
RTE_DefaultConfig.text_forecolor = "bIngDaq ghItlh leghlu' Color";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "Color Dub";	//"Back Color"
RTE_DefaultConfig.text_justify = "justify";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "poS justify";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "lugh justify";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "botlh justify";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "naQ justify";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "pagh justify";	//"Justify None"
RTE_DefaultConfig.text_delete = "delete";	//"Delete"
RTE_DefaultConfig.text_save = "teywI' toD";	//"Save file"
RTE_DefaultConfig.text_selectall = "Hoch wIv";	//"Select All"
RTE_DefaultConfig.text_code = "HTML ngoq";	//"HTML Code"
RTE_DefaultConfig.text_preview = "preview";	//"Preview"
RTE_DefaultConfig.text_print = "print";	//"Print"
RTE_DefaultConfig.text_undo = "ha'";	//"Undo"
RTE_DefaultConfig.text_redo = "redo";	//"Redo"
RTE_DefaultConfig.text_more = "vIta'Qo'.";	//"More..."
RTE_DefaultConfig.text_newdoc = "Doc chu'";	//"New Doc"
RTE_DefaultConfig.text_help = "hiqah";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "qechDaj Qorwagh";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "mej naQ jIHDaq";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "ghItlhvam Editor";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "ghItlhvam Styles";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "inline Styles";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "paragraph Styles";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "link Styles";	//"Link Styles"
RTE_DefaultConfig.text_link = "link";	//"Link"
RTE_DefaultConfig.text_style = "style";	//"Styles"
RTE_DefaultConfig.text_cssclass = "css Segh";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "pong url";	//"By Url"
RTE_DefaultConfig.text_upload = "upload";	//"Upload"
RTE_DefaultConfig.text_size = "size";	//"Size"
RTE_DefaultConfig.text_text = "bIngDaq ghItlh leghlu'";	//"Text"
RTE_DefaultConfig.text_opennewwin = "qaStaHvIS chu' tab mInDu'lIj";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "insert";	//"Insert"
RTE_DefaultConfig.text_update = "update";	//"Update"
RTE_DefaultConfig.text_find = "vItu' je ngaSwI' yuvtlhe' wIngaQmoHta'DI'";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "tu'";	//"Find"
RTE_DefaultConfig.text_replacewith = "ngaSwI' yuvtlhe' wIngaQmoHta'DI'";	//"Replace"
RTE_DefaultConfig.text_findnext = "veb";	//"Next"
RTE_DefaultConfig.text_replaceonce = "ngaSwI' yuvtlhe' wIngaQmoHta'DI'";	//"Replace"
RTE_DefaultConfig.text_replaceall = "Hoch ngaSwI' yuvtlhe' wIngaQmoHta'DI'";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "Case qul naQmey";	//"Match Case"
RTE_DefaultConfig.text_matchword = "qul naQmey mu'";	//"Match Word"
RTE_DefaultConfig.text_move_down = "vIH";	//"Move Down"
RTE_DefaultConfig.text_move_up = "vIH";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "auto size";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100 vatlhvI' width";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75 vatlhvI' width";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50 vatlhvI' width";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25 vatlhvI' width";	//"25% width"
RTE_DefaultConfig.text_controlsize = "HIjmeH Size";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "alt bIngDaq ghItlh leghlu'";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "justify";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "ghItlhvam Caption";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "Cells merge";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "split Cells chong";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "split Cells SaS";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "cell bIngDaq ghItlh leghlu' Color";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "cell Dub Color";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "Row wovbe' insert";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "Row Below insert";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "tut poS insert";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "tut nIH insert";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "tut delete";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "Row delete";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "raS delete";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "auto size";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "raS Header";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "paragraph chu' chel";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "paste";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "paste";	//"Paste"
RTE_DefaultConfig.text_pastetext = "paste bIngDaq ghItlh leghlu'";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "paste je Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "paste mu'mey";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "DubelmoHchugh ctrl + LUT VAGH DanoHmeH 'a ghIH paste vaj box below. Say' \r\nthe 'a ghIH automatically.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "paragraph";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "paragraph";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "vIH";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "vIH";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "duplicate";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "delete";	//"Delete"
RTE_DefaultConfig.text_pmore = "choghIjtaHghach...";	//"More.."
RTE_DefaultConfig.text_togglemore = "choghIjtaHghach...";	//"More.."
RTE_DefaultConfig.text_toggleborder = "toggle border";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "pe'";	//"Cut"
RTE_DefaultConfig.text_copy = "copy";	//"Copy"
RTE_DefaultConfig.text_copied = "copy";	//"copied"
RTE_DefaultConfig.text_insertgallery = "insert Gallery";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "Document insert";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "Template insert";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "preview";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "motlh";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "mobile";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "tablet";	//"Tablet"
RTE_DefaultConfig.text_table = "ras";	//"Table"
RTE_DefaultConfig.text_tablecell = "ratlh paw'chuq";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "raS";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "wej mIw'a'";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "automatic";	//"Automatic"
RTE_DefaultConfig.text_colormore = "neh";	//"More"
RTE_DefaultConfig.text_colorpicker = "color qaparHa'taHmeH";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "'ej bebvo' Palette";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "Colors 'oH pong";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "basic";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "addition";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "drag chagh 'ej";	//"Drag and drop"
RTE_DefaultConfig.text_or = "ghap";	//"or"
RTE_DefaultConfig.text_clicktoupload = "click upload";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "default ghItlhvam Caption";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "search";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "vuDmey'e' vuS yotlh SIch bIngDaq ghItlh leghlu' chel.";	//"The text to be added has reached the character limit for this field."
