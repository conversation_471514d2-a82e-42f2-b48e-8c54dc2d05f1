<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../config/defined.conf.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Function to send email notifications
function sendEmailNotification($to, $subject, $message) {
    $headers = "From: <EMAIL>\r\n";
    $headers .= "Reply-To: <EMAIL>\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";

    return mail($to, $subject, $message, $headers);
}

// Function to send SMS notifications (placeholder)
function sendSMSNotification($phoneNumber, $message) {
    // SMS sending logic would go here
    // This is a placeholder function
    return true;
}

// Function to notify users about ticket updates
function notifyUser($userId, $ticketId, $status) {
    // Fetch user email and phone number from the database
    // Placeholder for database connection and query
    $userEmail = "<EMAIL>"; // Replace with actual email fetch
    $userPhone = "1234567890"; // Replace with actual phone fetch

    $subject = "Ticket Update: Ticket #$ticketId";
    $message = "Your ticket with ID $ticketId has been updated to status: $status.";

    // Send email notification
    sendEmailNotification($userEmail, $subject, $message);

    // Send SMS notification
    sendSMSNotification($userPhone, $message);
}
?>