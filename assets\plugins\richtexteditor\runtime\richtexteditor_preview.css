﻿
/* TODO: use @import for your css */

body, table, p, div {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	color: #414141;
	font-size: 14px;
	line-height: 1.6;
}
a {
	color: #377dff;
	text-decoration: none;
	-webkit-transition: color 0.2s ease 0s, text-decoration 0.2s ease 0s;
	-moz-transition: color 0.2s ease 0s, text-decoration 0.2s ease 0s;
	-o-transition: color 0.2s ease 0s, text-decoration 0.2s ease 0s;
	transition: color 0.2s ease 0s, text-decoration 0.2s ease 0s;
}

/*RTE_DefaultConfig.items_InlineClasses*/
.my-cls-mark {
	background-color: yellow;
	font-weight: bold;
}

.my-cls-warning {
	background-color: orange;
	color: white;
	font-weight: bold;
}
/*RTE_DefaultConfig.items_ParagraphClasses*/
.my-cls-quote {
	margin: 10px;
	padding-left: 10px;
	border-left: dashed 1px red;
	font-style: italic;
}

.my-cls-largecenter {
	font-size: 1.5em;
	font-weight: bold;
	text-align: center;
	margin: 10px;
}

* {
	box-sizing: border-box;
}


body {
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

table {
	border-spacing: 0;
	border-collapse: collapse;
}

table:not([width]) {
	width: 100%;
}

table[border="0"] td, table:not([border]) td, table[border="0"] th, table:not([border]) th {
	border: 1px solid #ddd;
}
thead {
	background-color: #eee;
}
.table > tbody > tr > td, .table > tbody > tr > th, .table > tfoot > tr > td, .table > tfoot > tr > th, .table > thead > tr > td, .table > thead > tr > th {
	padding: 8px;
	line-height: 1.42857143;
	vertical-align: top;
	border-top: 1px solid #ddd;
}



.dp-highlighter {
	font-family: "Consolas", "Courier New", Courier, mono, serif;
	font-size: 12px;
	background-color: #E7E5DC;
	width: 99%;
	overflow: auto;
	margin: 18px 0 18px 0 !important;
	padding-top: 1px; /* adds a little border on top when controls are hidden */
}

	/* clear styles */
	.dp-highlighter ol,
	.dp-highlighter ol li,
	.dp-highlighter ol li span {
		margin: 0;
		padding: 0;
		border: none;
	}

	.dp-highlighter a,
	.dp-highlighter a:hover {
		background: none;
		border: none;
		padding: 0;
		margin: 0;
	}

	.dp-highlighter .bar {
		padding-left: 45px;
	}

	.dp-highlighter.collapsed .bar,
	.dp-highlighter.nogutter .bar {
		padding-left: 0px;
	}

	.dp-highlighter ol {
		list-style: decimal; /* for ie */
		background-color: #fff;
		margin: 0px 0px 1px 45px !important; /* 1px bottom margin seems to fix occasional Firefox scrolling */
		padding: 0px;
		color: #5C5C5C;
	}

	.dp-highlighter.nogutter ol,
	.dp-highlighter.nogutter ol li {
		list-style: none !important;
		margin-left: 0px !important;
	}

	.dp-highlighter ol li,
	.dp-highlighter .columns div {
		list-style: decimal-leading-zero; /* better look for others, override cascade from OL */
		list-style-position: outside !important;
		border-left: 3px solid #6CE26C;
		background-color: #F8F8F8;
		color: #5C5C5C;
		padding: 0 3px 0 10px !important;
		margin: 0 !important;
	}

	.dp-highlighter.nogutter ol li,
	.dp-highlighter.nogutter .columns div {
		border: 0;
	}

	.dp-highlighter .columns {
		background-color: #F8F8F8;
		color: gray;
		overflow: hidden;
		width: 100%;
	}

		.dp-highlighter .columns div {
			padding-bottom: 5px;
		}

	.dp-highlighter ol li.alt {
		background-color: #FFF;
		color: inherit;
	}

	.dp-highlighter ol li span {
		color: black;
		background-color: inherit;
	}

	/* Adjust some properties when collapsed */

	.dp-highlighter.collapsed ol {
		margin: 0px;
	}

		.dp-highlighter.collapsed ol li {
			display: none;
		}

	/* Additional modifications when in print-view */

	.dp-highlighter.printing {
		border: none;
	}

		.dp-highlighter.printing .tools {
			display: none !important;
		}

		.dp-highlighter.printing li {
			display: list-item !important;
		}

	/* Styles for the tools */

	.dp-highlighter .tools {
		padding: 3px 8px 3px 10px;
		font: 9px Verdana, Geneva, Arial, Helvetica, sans-serif;
		color: silver;
		background-color: #f8f8f8;
		padding-bottom: 10px;
		border-left: 3px solid #6CE26C;
	}

	.dp-highlighter.nogutter .tools {
		border-left: 0;
	}

	.dp-highlighter.collapsed .tools {
		border-bottom: 0;
	}

	.dp-highlighter .tools a {
		font-size: 9px;
		color: #a0a0a0;
		background-color: inherit;
		text-decoration: none;
		margin-right: 10px;
	}

		.dp-highlighter .tools a:hover {
			color: red;
			background-color: inherit;
			text-decoration: underline;
		}

/* About dialog styles */

.dp-about {
	background-color: #fff;
	color: #333;
	margin: 0px;
	padding: 0px;
}

	.dp-about table {
		width: 100%;
		height: 100%;
		font-size: 11px;
		font-family: Tahoma, Verdana, Arial, sans-serif !important;
	}

	.dp-about td {
		padding: 10px;
		vertical-align: top;
	}

	.dp-about .copy {
		border-bottom: 1px solid #ACA899;
		height: 95%;
	}

	.dp-about .title {
		color: red;
		background-color: inherit;
		font-weight: bold;
	}

	.dp-about .para {
		margin: 0 0 4px 0;
	}

	.dp-about .footer {
		background-color: #ECEADB;
		color: #333;
		border-top: 1px solid #fff;
		text-align: right;
	}

	.dp-about .close {
		font-size: 11px;
		font-family: Tahoma, Verdana, Arial, sans-serif !important;
		background-color: #ECEADB;
		color: #333;
		width: 60px;
		height: 22px;
	}

/* Language specific styles */

.dp-highlighter .comment, .dp-highlighter .comments {
	color: #008200;
	background-color: inherit;
}

.dp-highlighter .string {
	color: blue;
	background-color: inherit;
}

.dp-highlighter .keyword {
	color: #069;
	font-weight: bold;
	background-color: inherit;
}

.dp-highlighter .preprocessor {
	color: gray;
	background-color: inherit;
}
