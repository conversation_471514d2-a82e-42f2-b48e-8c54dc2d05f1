<?php
session_start();

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Fetch assigned tickets for user's team
$query = "SELECT t.ticket_number,
       t.customer_number,
       c.CusName AS customer_name,
       t.issue_details,
       t.priority,
       t.status,
       t.created_at,
       tm.name AS team_name,
       u.username AS created_by
FROM tickets t
LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
LEFT JOIN teams tm ON t.assigned_team = tm.id
LEFT JOIN users u ON t.username = u.username
WHERE t.assigned_team IN
    (SELECT team_members.team_id
     FROM users
     LEFT JOIN team_members ON users.id = team_members.user_id
     WHERE users.id = :user_id)
ORDER BY t.created_at DESC";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$assigned_tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once '../../includes/header.php';
?>

<div class="container mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0">Team Assigned Tickets</h4>
            <span class="badge badge-light">
                Total: <?php echo count($assigned_tickets); ?>
            </span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover small">
                    <thead>
                        <tr>
                            <th>Ticket No.</th>
                            <th>Customer</th>
                            <th>Issue Detail</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Created By</th>
                            <th>Created At</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($assigned_tickets)): ?>
                            <?php foreach ($assigned_tickets as $ticket): ?>
                                <tr class="<?php echo getTicketRowClass($ticket['priority'], $ticket['status']); ?>">
                                    <td><?php echo htmlspecialchars($ticket['ticket_number']); ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($ticket['customer_name']); ?>
                                        <small class="d-block text-muted">
                                            <?php echo htmlspecialchars($ticket['customer_number']); ?>
                                        </small>
                                    </td>
                                    <td><?php echo text_to_readmore(htmlspecialchars($ticket['issue_details'])); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                                            <?php echo htmlspecialchars($ticket['priority']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                            <?php echo htmlspecialchars($ticket['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($ticket['created_by']); ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></td>
                                    <td>
                                        <a href="<?=BASE_URL?>/modules/tickets/view.php?tkt_id=<?php echo urlencode($ticket['ticket_number']); ?>" 
                                           class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="8" class="text-center text-muted">
                                    No tickets assigned to your team.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.075);
    transition: background-color 0.2s ease;
}
</style>

<?php 
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>

