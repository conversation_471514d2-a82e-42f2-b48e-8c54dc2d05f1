<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../config/defined.conf.php';
require_once '../includes/authenCheck.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Handle image upload
if (isset($_FILES['file'])) {
    $file = $_FILES['file'];
    
    // Validate file type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowed_types)) {
        header('HTTP/1.1 400 Bad Request');
        echo json_encode(['error' => 'Invalid file type']);
        exit;
    }
    
    // Generate unique filename
    $upload_dir = '../uploads/comments/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $filename = uniqid() . '_' . $file['name'];
    $filepath = $upload_dir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        // Return success response
        echo json_encode([
            'location' => BASE_URL . '/uploads/comments/' . $filename
        ]);
    } else {
        header('HTTP/1.1 500 Internal Server Error');
        echo json_encode(['error' => 'Failed to upload file']);
    }
} else {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['error' => 'No file uploaded']);
}