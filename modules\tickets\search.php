<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

require_once '../../includes/header.php';

$searchTerm = isset($_GET['ticket']) ? trim($_GET['ticket']) : '';

if (!empty($searchTerm)) {
    $query = "SELECT 
        t.*, 
        c.CusName AS customer_name,
        c.CusAddress AS customer_address,
        tm.name AS team_name,
        u.username AS created_by
    FROM 
        tickets t
        LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
        LEFT JOIN teams tm ON t.assigned_team = tm.id
        LEFT JOIN users u ON t.username = u.username
    WHERE 
        t.ticket_number LIKE :search
        OR c.CusName LIKE :search
        OR t.customer_number LIKE :search
    ORDER BY t.created_at DESC
    LIMIT 100";
    
    $stmt = $pdo->prepare($query);
    $stmt->bindValue(':search', "%$searchTerm%");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-md-11 ml-auto mr-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Search Results</h4>
                    <span>Search term: "<?php echo htmlspecialchars($searchTerm); ?>"</span>
                </div>
                <div class="card-body">
                    <?php if (!empty($searchTerm)): ?>
                        <?php if (!empty($results)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover small">
                                    <thead>
                                        <tr>
                                            <th>Ticket #</th>
                                            <th>Customer</th>
                                            <th>Issue</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Team</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results as $ticket): ?>
                                            <tr>
                                                <td>
                                                    <?php echo htmlspecialchars($ticket['ticket_number']); ?></td>
                                               
                                                <td>
                                                    <?php echo htmlspecialchars($ticket['customer_name']); ?><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($ticket['customer_number']); ?></small>
                                                </td>
                                                <td><?php echo text_to_readmore(htmlspecialchars($ticket['issue_details']),70); ?></td>
                                                <td>
                                                    <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                                                        <?php echo htmlspecialchars($ticket['priority']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                                        <?php echo htmlspecialchars($ticket['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($ticket['team_name']); ?></td>
                                                <td><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></td>
                                                <td>
                                                    <a href="view.php?tkt_id=<?php echo urlencode($ticket['ticket_number']); ?>" 
                                                       class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                No tickets found matching your search criteria.
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="list.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer and scripts
// This is where you would include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>