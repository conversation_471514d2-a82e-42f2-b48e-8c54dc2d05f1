<?php
// Database configuration settings
$host = 'localhost'; // Database host
$db_name = 'ticket_management'; // Database name
$username = 'ticket'; // Database username
$password = 'jiNet123#'; // Database password
$Auth_Methode ='AZURE-OAUTH';//'AZURE-OAUTH';// 'NONE_AZURE-OAUTH';

try {
    // Create a new PDO instance
    $pdo = new PDO("mysql:host=$host;dbname=$db_name", $username, $password);
    // Set the PDO error mode to exception
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // Handle connection error
    die("Connection failed: " . $e->getMessage());
}
