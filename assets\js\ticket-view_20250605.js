
// Tab handling
$(document).ready(function() {
    // Check if there's a stored active tab
    let activeTab = localStorage.getItem('ticketActiveTab');
    
    if (activeTab) {
        // Activate the stored tab
        $('#ticketActionTabs a[href="' + activeTab + '"]').tab('show');
    }
    
    // Store the active tab when changed
    $('#ticketActionTabs a').on('shown.bs.tab', function(e) {
        localStorage.setItem('ticketActiveTab', $(e.target).attr('href'));
    });
    
    // Clear active tab when leaving the page
    $(window).on('beforeunload', function() {
        localStorage.removeItem('ticketActiveTab');
    });
    
    // Switch to forward tab if there's a forward error
    if (window.location.href.includes('forward_error')) {
        $('#ticketActionTabs a[href="#forward-content"]').tab('show');
    }
    
    // Initialize help note
    $("#helpNote").draggable({
        handle: "#helpDragHandle",
        containment: "window",
        snap: "window",
        snapMode: "outer"
    });

    // Minimize/Maximize functionality
    $("#minimizeHelp").click(function() {
        $("#helpContent").slideToggle();
        $(this).find('i').toggleClass('fa-plus fa-minus');
        
        // Save state
        localStorage.setItem('helpMinimized', 
            $("#helpContent").is(':hidden'));
    });

    // Restore last state
    if (localStorage.getItem('helpMinimized') === 'true') {
        $("#helpContent").hide();
        $("#minimizeHelp").find('i').removeClass('fa-minus').addClass('fa-plus');
    }
});
