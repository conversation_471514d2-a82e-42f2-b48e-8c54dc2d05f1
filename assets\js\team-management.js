$(document).ready(function() {
    // Initialize select2 for better dropdown experience
    if ($.fn.select2) {
        $('#user_id, #team_id, #new_role, #new_assign_team').select2({
            theme: 'bootstrap4',
            placeholder: 'Select an option',
            allowClear: true
        });
    }
    
    // Save active tab state
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        localStorage.setItem('teamManagementActiveTab', $(e.target).attr('id'));
    });
    
    // Restore active tab state
    var activeTab = localStorage.getItem('teamManagementActiveTab');
    if (activeTab) {
        $('#' + activeTab).tab('show');
    }
    
    // Filter functionality for team members table
    $("#teamMemberSearch").on("keyup", function() {
        var value = $(this).val().toLowerCase();
        $("#teamMembersTable tbody tr").filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
    
    // Confirmation for removing team members
    $(".remove-team-member").on("click", function(e) {
        if (!confirm("Are you sure you want to remove this user from the team?")) {
            e.preventDefault();
        }
    });
    
    // Toggle password visibility
    $("#togglePassword").on("click", function() {
        var passwordField = $("#new_password");
        var passwordFieldType = passwordField.attr('type');
        
        if (passwordFieldType === 'password') {
            passwordField.attr('type', 'text');
            $(this).find('i').removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            $(this).find('i').removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });
    
    // Initialize DataTable for users list if available
    if ($.fn.DataTable) {
        $('#usersTable').DataTable({
            "pageLength": 10,
            "order": [[0, "asc"]],
            "language": {
                "search": "Filter:",
                "lengthMenu": "Show _MENU_ users per page",
                "info": "Showing _START_ to _END_ of _TOTAL_ users"
            }
        });
    }
    
    // Form validation for new user
    $("#createUserForm").on("submit", function(e) {
        var username = $("#new_username").val().trim();
        var email = $("#new_email").val().trim();
        var password = $("#new_password").val().trim();
        var role = $("#new_role").val();
        var isValid = true;
        
        // Clear previous error messages
        $(".invalid-feedback").remove();
        $(".is-invalid").removeClass("is-invalid");
        
        // Validate username
        if (username === "") {
            $("#new_username").addClass("is-invalid");
            $("#new_username").after('<div class="invalid-feedback">Username is required</div>');
            isValid = false;
        }
        
        // Validate email
        if (email === "") {
            $("#new_email").addClass("is-invalid");
            $("#new_email").after('<div class="invalid-feedback">Email is required</div>');
            isValid = false;
        } else if (!isValidEmail(email)) {
            $("#new_email").addClass("is-invalid");
            $("#new_email").after('<div class="invalid-feedback">Invalid email format</div>');
            isValid = false;
        }
        
        // Validate password
        if (password === "") {
            $("#new_password").addClass("is-invalid");
            $("#new_password").after('<div class="invalid-feedback">Password is required</div>');
            isValid = false;
        } else if (password.length < 6) {
            $("#new_password").addClass("is-invalid");
            $("#new_password").after('<div class="invalid-feedback">Password must be at least 6 characters</div>');
            isValid = false;
        }
        
        // Validate role
        if (role === "") {
            $("#new_role").addClass("is-invalid");
            $("#new_role").after('<div class="invalid-feedback">Role is required</div>');
            isValid = false;
        }
        
        return isValid;
    });
    
    // Helper function to validate email
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
      // Handle edit user button clicks
    $('.edit-user').click(function() {
        var userId = $(this).data('user-id');
        var username = $(this).data('username');
        var email = $(this).data('email');
        var role = $(this).data('role');
        var phone = $(this).data('phone');
        
        $('#edit_user_id').val(userId);
        $('#edit_username').val(username);
        $('#edit_email').val(email);
        $('#edit_role').val(role);
        $('#edit_phone').val(phone);
        $('#edit_password').val(''); // Clear password field
    });
});

