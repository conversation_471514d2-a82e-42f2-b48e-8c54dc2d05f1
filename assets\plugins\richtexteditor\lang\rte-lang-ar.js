﻿//Arabic , العربية
RTE_DefaultConfig.text_language = "اللغه";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "الغاء";	//"Cancel"
RTE_DefaultConfig.text_normal = "العاديه";	//"Normal"
RTE_DefaultConfig.text_h1 = "العنوان الأول";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "العنوان 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "العنوان 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "العنوان 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "العنوان 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "العنوان 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "العنوان 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "اغلاق";	//"Close"
RTE_DefaultConfig.text_bold = "جريئه";	//"Bold"
RTE_DefaultConfig.text_italic = "مائل";	//"Italic"
RTE_DefaultConfig.text_underline = "تسطير";	//"Underline"
RTE_DefaultConfig.text_strike = "خط الإضراب";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "مرتفع";	//"Superscript"
RTE_DefaultConfig.text_subscript = "سوبركريبت";	//"Subcript"
RTE_DefaultConfig.text_ucase = "الحالة العليا";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "حالة أقل";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "إزالة التنسيق";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "إدراج الارتباط";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "رابط مفتوح";	//"Open Link"
RTE_DefaultConfig.text_editlink = "تحرير الارتباط";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "إزالة الارتباط";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "ارتفاع الخط";	//"Line Height"
RTE_DefaultConfig.text_indent = "المسافه البادئه";	//"Indent"
RTE_DefaultConfig.text_outdent = "المسافة البادئة";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "عرض أسعار الكتلة";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "قائمة مرتبة";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "قائمة غير مرتبة";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "إدراج قاعدة أفقية";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "تاريخ الإدراج";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "إدراج جدول";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "إدراج صورة";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "إدراج فيديو";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "إدراج التعليمات البرمجية";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "إنشاء PDF";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "إدراج الرموز التعبيرية";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "أحرف خاصة";	//"Special characters"
RTE_DefaultConfig.text_characters = "الاحرف";	//"Characters"
RTE_DefaultConfig.text_fontname = "الخط";	//"Font"
RTE_DefaultConfig.text_fontsize = "حجم";	//"Size"
RTE_DefaultConfig.text_forecolor = "لون النص";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "لون الظهر";	//"Back Color"
RTE_DefaultConfig.text_justify = "تبرير";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "ضبط اليسار";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "ضبط اليمين";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "مركز الـتّرَك";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "ضبط كامل";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "تبرير بلا";	//"Justify None"
RTE_DefaultConfig.text_delete = "حذف";	//"Delete"
RTE_DefaultConfig.text_save = "حفظ الملف";	//"Save file"
RTE_DefaultConfig.text_selectall = "حدد الكل";	//"Select All"
RTE_DefaultConfig.text_code = "رمز HTML";	//"HTML Code"
RTE_DefaultConfig.text_preview = "معاينه";	//"Preview"
RTE_DefaultConfig.text_print = "طباعه";	//"Print"
RTE_DefaultConfig.text_undo = "التراجع عن";	//"Undo"
RTE_DefaultConfig.text_redo = "اعادته";	//"Redo"
RTE_DefaultConfig.text_more = "اكثر...";	//"More..."
RTE_DefaultConfig.text_newdoc = "وثيقة جديدة";	//"New Doc"
RTE_DefaultConfig.text_help = "مساعده";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "احتواء الإطار";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "إنهاء ملء الشاشة";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "محرر الصور";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "أنماط الصور";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "أنماط مضمنة";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "أنماط الفقرة";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "أنماط الارتباط";	//"Link Styles"
RTE_DefaultConfig.text_link = "الارتباط";	//"Link"
RTE_DefaultConfig.text_style = "انماط";	//"Styles"
RTE_DefaultConfig.text_cssclass = "فئات CSS";	//"Css Classes"
RTE_DefaultConfig.text_url = "url";	//"Url"
RTE_DefaultConfig.text_byurl = "حسب عنوان URL";	//"By Url"
RTE_DefaultConfig.text_upload = "تحميل";	//"Upload"
RTE_DefaultConfig.text_size = "حجم";	//"Size"
RTE_DefaultConfig.text_text = "النص";	//"Text"
RTE_DefaultConfig.text_opennewwin = "فتح في علامة تبويب جديدة";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "ادراج";	//"Insert"
RTE_DefaultConfig.text_update = "تحديث";	//"Update"
RTE_DefaultConfig.text_find = "البحث والاستبدال";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "العثور";	//"Find"
RTE_DefaultConfig.text_replacewith = "استبدال";	//"Replace"
RTE_DefaultConfig.text_findnext = "القادم";	//"Next"
RTE_DefaultConfig.text_replaceonce = "استبدال";	//"Replace"
RTE_DefaultConfig.text_replaceall = "استبدال الكل";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "حالة المطابقة";	//"Match Case"
RTE_DefaultConfig.text_matchword = "مطابقة Word";	//"Match Word"
RTE_DefaultConfig.text_move_down = "الانتقال لأسفل";	//"Move Down"
RTE_DefaultConfig.text_move_up = "الانتقال لأعلى";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "حجم السيارات";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "عرض 100%";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "عرض 75%";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "عرض 50%";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "عرض 25%";	//"25% width"
RTE_DefaultConfig.text_controlsize = "تعيين الحجم";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "نص بديل";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "تبرير";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "تسمية توضيحية للصورة";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "دمج الخلايا";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "تقسيم الخلايا عمودي";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "تقسيم الخلايا الأفقي";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "لون نص الخلية";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "لون الخلية الخلفي";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "إدراج الصف أعلاه";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "إدراج الصف أدناه";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "إدراج عمود يسار";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "إدراج العمود إلى اليمين";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "حذف العمود";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "حذف الصف";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "حذف الجدول";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "حجم السيارات";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "رأس الجدول";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "إضافة فقرة جديدة";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "لصق";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "لصق";	//"Paste"
RTE_DefaultConfig.text_pastetext = "لصق النص";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "لصق كما Html";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "لصق كلمة";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "الرجاء استخدام CTRL+V للصق المحتوى في المربع أدناه. سيتم تنظيف المحتوى تلقائيًا.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "الفقرات";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "الفقرات";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "الانتقال لأعلى";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "الانتقال لأسفل";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "مكرره";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "حذف";	//"Delete"
RTE_DefaultConfig.text_pmore = "اكثر..";	//"More.."
RTE_DefaultConfig.text_togglemore = "اكثر..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "تبديل الحدود";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "قطع";	//"Cut"
RTE_DefaultConfig.text_copy = "نسخ";	//"Copy"
RTE_DefaultConfig.text_copied = "نسخ";	//"copied"
RTE_DefaultConfig.text_insertgallery = "إدراج معرض";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "إدراج مستند";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "إدراج قالب";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "معاينه";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "العاديه";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "المحمول";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "اللوحي";	//"Tablet"
RTE_DefaultConfig.text_table = "الجدول";	//"Table"
RTE_DefaultConfig.text_tablecell = "خلية الجدول";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "صف الجدول";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "عمود الجدول";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "التلقائي";	//"Automatic"
RTE_DefaultConfig.text_colormore = "اكثر";	//"More"
RTE_DefaultConfig.text_colorpicker = "منتقي الألوان";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "لوح ويب";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "الألوان المسماة";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "الاساسيه";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "الاضافه الي ذلك";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "السحب والإسقاط";	//"Drag and drop"
RTE_DefaultConfig.text_or = "او";	//"or"
RTE_DefaultConfig.text_clicktoupload = "انقر للتحميل";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "التسمية التوضيحية الافتراضية للصورة";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "البحث";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "وصل النص الذي سيتم إضافته إلى حد الأحرف لهذا الحقل.";	//"The text to be added has reached the character limit for this field."
