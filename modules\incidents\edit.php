<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Get incident ID from URL
$incident_id = $_GET['incident_id'] ?? '';

if (empty($incident_id)) {
    header("Location: list.php");
    exit();
}

// Initialize variables
$errors = [];
$success_message = "";

// Fetch incident details
$query = "SELECT * FROM incidents WHERE incident_no = ?";
$stmt = $pdo->prepare($query);
$stmt->execute([$incident_id]);
$incident = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$incident) {
    header("Location: list.php?error=Incident not found");
    exit();
}

// Get users for dropdowns
$users_query = "SELECT id, username, full_name FROM users ORDER BY full_name, username";
$users_result = $pdo->query($users_query);
$users = $users_result->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Collect form data
    $event_date = trim($_POST["event_date"]);
    $reporter = trim($_POST["reporter"]);
    $channel = trim($_POST["channel"]);
    $ticket_no = trim($_POST["ticket_no"]);
    $is_ever_happened = isset($_POST["is_ever_happened"]) ? 1 : 0;
    $incident_ref_no = trim($_POST["incident_ref_no"]);
    $type_of_event = trim($_POST["type_of_event"]);
    $event_detail = trim($_POST["event_detail"]);
    $effect = trim($_POST["effect"]);
    $preliminary_operations = trim($_POST["preliminary_operations"]);
    $severity = trim($_POST["severity"]);
    $responsible_person = trim($_POST["responsible_person"]);
    $editing_details = trim($_POST["editing_details"]);
    $date_of_completion = trim($_POST["date_of_completion"]);
    $investigation_summary = trim($_POST["investigation_summary"]);
    $future_preventive_measures = trim($_POST["future_preventive_measures"]);
    $approver = trim($_POST["approver"]);
    $approval_date = trim($_POST["approval_date"]);
    $status = trim($_POST["status"]);

    // Validation
    if (empty($event_date)) {
        $errors[] = "Event Date is required.";
    }
    if (empty($reporter)) {
        $errors[] = "Reporter is required.";
    }
    if (empty($channel)) {
        $errors[] = "Channel is required.";
    }
    if (empty($type_of_event)) {
        $errors[] = "Type of Event is required.";
    }
    if (empty($event_detail)) {
        $errors[] = "Event Detail is required.";
    }
    if (empty($severity)) {
        $errors[] = "Severity is required.";
    }
    if (empty($responsible_person)) {
        $errors[] = "Responsible Person is required.";
    }
    if (empty($status)) {
        $errors[] = "Status is required.";
    }

    // If no errors, update incident in database
    if (empty($errors)) {
        try {
            $query = "UPDATE incidents SET 
                event_date = ?, reporter = ?, channel = ?, ticket_no = ?, 
                is_ever_happened = ?, incident_ref_no = ?, type_of_event = ?, 
                event_detail = ?, effect = ?, preliminary_operations = ?, 
                severity = ?, responsible_person = ?, editing_details = ?, 
                date_of_completion = ?, investigation_summary = ?, 
                future_preventive_measures = ?, approver = ?, approval_date = ?, 
                status = ?, updated_at = NOW()
                WHERE incident_no = ?";
            
            $stmt = $pdo->prepare($query);
            $result = $stmt->execute([
                $event_date, $reporter, $channel, $ticket_no,
                $is_ever_happened, $incident_ref_no, $type_of_event,
                $event_detail, $effect, $preliminary_operations,
                $severity, $responsible_person, $editing_details,
                $date_of_completion, $investigation_summary,
                $future_preventive_measures, $approver, $approval_date,
                $status, $incident_id
            ]);

            if ($result) {
                $success_message = "Incident report updated successfully!";
                // Refresh incident data
                $stmt = $pdo->prepare("SELECT * FROM incidents WHERE incident_no = ?");
                $stmt->execute([$incident_id]);
                $incident = $stmt->fetch(PDO::FETCH_ASSOC);
            } else {
                $errors[] = "Failed to update incident report. Please try again.";
            }
        } catch (PDOException $e) {
            $errors[] = "Database error: " . $e->getMessage();
        }
    }
}

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-edit"></i> 
                        Edit Incident Report: <?php echo htmlspecialchars($incident['incident_no']); ?>
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success_message); ?>
                            <div class="mt-2">
                                <a href="view.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>" class="btn btn-sm btn-primary">View Incident</a>
                                <a href="list.php" class="btn btn-sm btn-secondary">Back to List</a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Basic Information</h5>
                                
                                <div class="form-group">
                                    <label for="event_date">Event Date <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control" id="event_date" name="event_date" 
                                           value="<?php echo date('Y-m-d\TH:i', strtotime($incident['event_date'])); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="reporter">Reporter <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="reporter" name="reporter" 
                                           value="<?php echo htmlspecialchars($incident['reporter']); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="channel">Channel <span class="text-danger">*</span></label>
                                    <select class="form-control" id="channel" name="channel" required>
                                        <option value="">Select Channel</option>
                                        <option value="Email" <?php echo ($incident['channel'] == 'Email') ? 'selected' : ''; ?>>Email</option>
                                        <option value="Phone" <?php echo ($incident['channel'] == 'Phone') ? 'selected' : ''; ?>>Phone</option>
                                        <option value="Internal System" <?php echo ($incident['channel'] == 'Internal System') ? 'selected' : ''; ?>>Internal System</option>
                                        <option value="External Report" <?php echo ($incident['channel'] == 'External Report') ? 'selected' : ''; ?>>External Report</option>
                                        <option value="Monitoring Alert" <?php echo ($incident['channel'] == 'Monitoring Alert') ? 'selected' : ''; ?>>Monitoring Alert</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="ticket_no">Ticket No.</label>
                                    <input type="text" class="form-control" id="ticket_no" name="ticket_no" 
                                           value="<?php echo htmlspecialchars($incident['ticket_no']); ?>">
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_ever_happened" name="is_ever_happened" value="1"
                                               <?php echo $incident['is_ever_happened'] ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="is_ever_happened">
                                            Has this type of incident ever happened before?
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="incident_ref_no">Incident Reference No.</label>
                                    <input type="text" class="form-control" id="incident_ref_no" name="incident_ref_no" 
                                           value="<?php echo htmlspecialchars($incident['incident_ref_no']); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="status">Status <span class="text-danger">*</span></label>
                                    <select class="form-control" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="Open" <?php echo ($incident['status'] == 'Open') ? 'selected' : ''; ?>>Open</option>
                                        <option value="In Progress" <?php echo ($incident['status'] == 'In Progress') ? 'selected' : ''; ?>>In Progress</option>
                                        <option value="Under Investigation" <?php echo ($incident['status'] == 'Under Investigation') ? 'selected' : ''; ?>>Under Investigation</option>
                                        <option value="Resolved" <?php echo ($incident['status'] == 'Resolved') ? 'selected' : ''; ?>>Resolved</option>
                                        <option value="Closed" <?php echo ($incident['status'] == 'Closed') ? 'selected' : ''; ?>>Closed</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Incident Details -->
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Incident Details</h5>
                                
                                <div class="form-group">
                                    <label for="type_of_event">Type of Event <span class="text-danger">*</span></label>
                                    <select class="form-control" id="type_of_event" name="type_of_event" required>
                                        <option value="">Select Event Type</option>
                                        <option value="Malware Attack" <?php echo ($incident['type_of_event'] == 'Malware Attack') ? 'selected' : ''; ?>>Malware Attack</option>
                                        <option value="Phishing Attack" <?php echo ($incident['type_of_event'] == 'Phishing Attack') ? 'selected' : ''; ?>>Phishing Attack</option>
                                        <option value="Data Breach" <?php echo ($incident['type_of_event'] == 'Data Breach') ? 'selected' : ''; ?>>Data Breach</option>
                                        <option value="Unauthorized Access" <?php echo ($incident['type_of_event'] == 'Unauthorized Access') ? 'selected' : ''; ?>>Unauthorized Access</option>
                                        <option value="DDoS Attack" <?php echo ($incident['type_of_event'] == 'DDoS Attack') ? 'selected' : ''; ?>>DDoS Attack</option>
                                        <option value="System Compromise" <?php echo ($incident['type_of_event'] == 'System Compromise') ? 'selected' : ''; ?>>System Compromise</option>
                                        <option value="Social Engineering" <?php echo ($incident['type_of_event'] == 'Social Engineering') ? 'selected' : ''; ?>>Social Engineering</option>
                                        <option value="Other" <?php echo ($incident['type_of_event'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="severity">Severity of the Incident <span class="text-danger">*</span></label>
                                    <select class="form-control" id="severity" name="severity" required>
                                        <option value="">Select Severity</option>
                                        <option value="Critical" <?php echo ($incident['severity'] == 'Critical') ? 'selected' : ''; ?>>Critical</option>
                                        <option value="High" <?php echo ($incident['severity'] == 'High') ? 'selected' : ''; ?>>High</option>
                                        <option value="Medium" <?php echo ($incident['severity'] == 'Medium') ? 'selected' : ''; ?>>Medium</option>
                                        <option value="Low" <?php echo ($incident['severity'] == 'Low') ? 'selected' : ''; ?>>Low</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="responsible_person">Responsible Person <span class="text-danger">*</span></label>
                                    <select class="form-control" id="responsible_person" name="responsible_person" required>
                                        <option value="">Select Responsible Person</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo htmlspecialchars($user['username']); ?>" 
                                                    <?php echo ($incident['responsible_person'] == $user['username']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Event Details Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">Event Details</h5>
                                
                                <div class="form-group">
                                    <label for="event_detail">Event Detail <span class="text-danger">*</span></label>
                                    <textarea class="form-control" id="event_detail" name="event_detail" rows="4" required><?php echo htmlspecialchars($incident['event_detail']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="effect">Effect</label>
                                    <textarea class="form-control" id="effect" name="effect" rows="3"><?php echo htmlspecialchars($incident['effect']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="preliminary_operations">Preliminary Operations</label>
                                    <textarea class="form-control" id="preliminary_operations" name="preliminary_operations" rows="3"><?php echo htmlspecialchars($incident['preliminary_operations']); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Investigation and Resolution Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">Investigation and Resolution</h5>
                                
                                <div class="form-group">
                                    <label for="editing_details">Editing Details</label>
                                    <textarea class="form-control" id="editing_details" name="editing_details" rows="3"><?php echo htmlspecialchars($incident['editing_details']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="date_of_completion">Date of Completion</label>
                                    <input type="datetime-local" class="form-control" id="date_of_completion" name="date_of_completion" 
                                           value="<?php echo $incident['date_of_completion'] ? date('Y-m-d\TH:i', strtotime($incident['date_of_completion'])) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="investigation_summary">Investigation Summary</label>
                                    <textarea class="form-control" id="investigation_summary" name="investigation_summary" rows="4"><?php echo htmlspecialchars($incident['investigation_summary']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="future_preventive_measures">Future Preventive Measures</label>
                                    <textarea class="form-control" id="future_preventive_measures" name="future_preventive_measures" rows="4"><?php echo htmlspecialchars($incident['future_preventive_measures']); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Approval Section -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5 class="text-primary mb-3">Approval</h5>
                                
                                <div class="form-group">
                                    <label for="approver">Approver</label>
                                    <select class="form-control" id="approver" name="approver">
                                        <option value="">Select Approver</option>
                                        <?php foreach ($users as $user): ?>
                                            <option value="<?php echo htmlspecialchars($user['username']); ?>" 
                                                    <?php echo ($incident['approver'] == $user['username']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($user['full_name'] ?: $user['username']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="approval_date">Approval Date</label>
                                    <input type="datetime-local" class="form-control" id="approval_date" name="approval_date" 
                                           value="<?php echo $incident['approval_date'] ? date('Y-m-d\TH:i', strtotime($incident['approval_date'])) : ''; ?>">
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-save"></i> Update Incident Report
                            </button>
                            <a href="view.php?incident_id=<?php echo urlencode($incident['incident_no']); ?>" class="btn btn-info btn-lg ml-2">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="list.php" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>
