﻿//Telugu , తెలుగు
RTE_DefaultConfig.text_language = "భాష";	//"Language"
RTE_DefaultConfig.text_ok = "OK";	//"OK"
RTE_DefaultConfig.text_cancel = "రద్దు";	//"Cancel"
RTE_DefaultConfig.text_normal = "సాధారణ";	//"Normal"
RTE_DefaultConfig.text_h1 = "న్యూస్ లైన్ 1";	//"Headline 1"
RTE_DefaultConfig.text_h2 = "హెడ్ లైన్ 2";	//"Headline 2"
RTE_DefaultConfig.text_h3 = "హెడ్ లైన్ 3";	//"Headline 3"
RTE_DefaultConfig.text_h4 = "హెడ్ లైన్ 4";	//"Headline 4"
RTE_DefaultConfig.text_h5 = "హెడ్ లైన్ 5";	//"Headline 5"
RTE_DefaultConfig.text_h6 = "న్యూస్ లైన్ 6";	//"Headline 6"
RTE_DefaultConfig.text_h7 = "న్యూస్ లైన్ 7";	//"Headline 7"
RTE_DefaultConfig.text_close = "దగ్గరగా";	//"Close"
RTE_DefaultConfig.text_bold = "బోల్డ్";	//"Bold"
RTE_DefaultConfig.text_italic = "ఇటాలిక్";	//"Italic"
RTE_DefaultConfig.text_underline = "అండర్ వేర్";	//"Underline"
RTE_DefaultConfig.text_strike = "సమ్మె రేఖ";	//"Strike Line"
RTE_DefaultConfig.text_superscript = "సూపర్ స్క్రిప్ట్";	//"Superscript"
RTE_DefaultConfig.text_subscript = "Subcript";	//"Subcript"
RTE_DefaultConfig.text_ucase = "అప్పర్ కేస్";	//"Upper Case"
RTE_DefaultConfig.text_lcase = "లోయర్ కేస్";	//"Lower Case"
RTE_DefaultConfig.text_removeformat = "ఆకృతిని తొలగించు";	//"Remove Format"
RTE_DefaultConfig.text_insertlink = "లింక్ చొప్పించు";	//"Insert Link"
RTE_DefaultConfig.text_openlink = "లింక్ ను తెరువు";	//"Open Link"
RTE_DefaultConfig.text_editlink = "లింక్ సంకలనం";	//"Edit Link"
RTE_DefaultConfig.text_unlink = "లింక్ తొలగించు";	//"Remove Link"
RTE_DefaultConfig.text_controlinsertlink = "@insertlink";	//"@insertlink"
RTE_DefaultConfig.text_controleditlink = "@editlink";	//"@editlink"
RTE_DefaultConfig.text_controlopenlink = "@openlink";	//"@openlink"
RTE_DefaultConfig.text_controlunlink = "@unlink";	//"@unlink"
RTE_DefaultConfig.text_lineheight = "పంక్తి ఎత్తు";	//"Line Height"
RTE_DefaultConfig.text_indent = "ఇండెంట్";	//"Indent"
RTE_DefaultConfig.text_outdent = "అవుట్ డెంట్";	//"Outdent"
RTE_DefaultConfig.text_insertblockquote = "బ్లాక్ కోట్";	//"Block Quote"
RTE_DefaultConfig.text_insertorderedlist = "ఆదేశించిన జాబితా";	//"Ordered List"
RTE_DefaultConfig.text_insertunorderedlist = "ఆదేశము లేని జాబితా";	//"Unordered List"
RTE_DefaultConfig.text_inserthorizontalrule = "క్షితిజ సమాంతర నిబంధనచొప్పించు";	//"Insert Horizontal Rule"
RTE_DefaultConfig.text_insertdate = "చొప్పించు తేదీ";	//"Insert Date"
RTE_DefaultConfig.text_inserttable = "పట్టికను చొప్పించు";	//"Insert Table"
RTE_DefaultConfig.text_insertimage = "చిత్తరువు చొప్పించు";	//"Insert Image"
RTE_DefaultConfig.text_insertvideo = "వీడియోను చొప్పించండి";	//"Insert Video"
RTE_DefaultConfig.text_insertcode = "చొప్పించు కోడ్";	//"Insert Code"
RTE_DefaultConfig.text_html2pdf = "PDF సృష్టించు";	//"Create PDF"
RTE_DefaultConfig.text_insertemoji = "ఎమోజిని చొప్పించు";	//"Insert Emoji"
RTE_DefaultConfig.text_insertchars = "ప్రత్యేక అక్షరాలు";	//"Special characters"
RTE_DefaultConfig.text_characters = "పాత్రలు";	//"Characters"
RTE_DefaultConfig.text_fontname = "ఫాంట్";	//"Font"
RTE_DefaultConfig.text_fontsize = "పరిమాణం";	//"Size"
RTE_DefaultConfig.text_forecolor = "వచన వర్ణం";	//"Text Color"
RTE_DefaultConfig.text_backcolor = "బ్యాక్ కలర్";	//"Back Color"
RTE_DefaultConfig.text_justify = "సమర్థిస్తూ";	//"Justify"
RTE_DefaultConfig.text_justifyleft = "ఎడమను సమర్థించు";	//"Justify Left"
RTE_DefaultConfig.text_justifyright = "కుడిని సమర్థించు";	//"Justify Right"
RTE_DefaultConfig.text_justifycenter = "కేంద్రాన్ని సమర్థించు";	//"Justify Center"
RTE_DefaultConfig.text_justifyfull = "పూర్తి ని సమర్ధించు";	//"Justify Full"
RTE_DefaultConfig.text_justifynone = "ఏదీ సమర్థించు";	//"Justify None"
RTE_DefaultConfig.text_delete = "డిలీట్";	//"Delete"
RTE_DefaultConfig.text_save = "ఫైలు భద్రపరుచు";	//"Save file"
RTE_DefaultConfig.text_selectall = "మొత్తం ఎంచుకోండి";	//"Select All"
RTE_DefaultConfig.text_code = "HTML కోడ్";	//"HTML Code"
RTE_DefaultConfig.text_preview = "ప్రివ్యూ";	//"Preview"
RTE_DefaultConfig.text_print = "ప్రింట్";	//"Print"
RTE_DefaultConfig.text_undo = "రద్దు";	//"Undo"
RTE_DefaultConfig.text_redo = "రెడో";	//"Redo"
RTE_DefaultConfig.text_more = "మరిన్ని...";	//"More..."
RTE_DefaultConfig.text_newdoc = "కొత్త పత్రి";	//"New Doc"
RTE_DefaultConfig.text_help = "సహాయం";	//"Help"
RTE_DefaultConfig.text_fullscreenenter = "గవాక్షానికి అమర్చు";	//"Fit to Window"
RTE_DefaultConfig.text_fullscreenexit = "పూర్తి తెర నుంచి నిష్క్రమించు";	//"Exit Full Screen"
RTE_DefaultConfig.text_fullscreen = "@text_fullscreenenter";	//"@text_fullscreenenter"
RTE_DefaultConfig.text_imageeditor = "ఇమేజ్ ఎడిటర్";	//"Image Editor"
RTE_DefaultConfig.text_imagestyle = "చిత్తరువు శైలులు";	//"Image Styles"
RTE_DefaultConfig.text_inlinestyle = "ఇన్ లైన్ శైలులు";	//"Inline Styles"
RTE_DefaultConfig.text_paragraphstyle = "పేరాశైలులు";	//"Paragraph Styles"
RTE_DefaultConfig.text_linkstyle = "లింక్ శైలులు";	//"Link Styles"
RTE_DefaultConfig.text_link = "లింక్";	//"Link"
RTE_DefaultConfig.text_style = "శైలులు";	//"Styles"
RTE_DefaultConfig.text_cssclass = "Css తరగతులు";	//"Css Classes"
RTE_DefaultConfig.text_url = "Url";	//"Url"
RTE_DefaultConfig.text_byurl = "Url ద్వారా";	//"By Url"
RTE_DefaultConfig.text_upload = "అప్ లోడ్";	//"Upload"
RTE_DefaultConfig.text_size = "పరిమాణం";	//"Size"
RTE_DefaultConfig.text_text = "టెక్ట్స్";	//"Text"
RTE_DefaultConfig.text_opennewwin = "కొత్త టాబ్ లో తెరువు";	//"Open in new tab"
RTE_DefaultConfig.text_insert = "చొప్పించండి";	//"Insert"
RTE_DefaultConfig.text_update = "అప్ డేట్";	//"Update"
RTE_DefaultConfig.text_find = "కనుగొనండి మరియు మార్చండి";	//"Find&Replace"
RTE_DefaultConfig.text_findwhat = "కనుగొనేందుకు";	//"Find"
RTE_DefaultConfig.text_replacewith = "భర్తీ";	//"Replace"
RTE_DefaultConfig.text_findnext = "వచ్చే";	//"Next"
RTE_DefaultConfig.text_replaceonce = "భర్తీ";	//"Replace"
RTE_DefaultConfig.text_replaceall = "మొత్తం భర్తీ";	//"Replace All"
RTE_DefaultConfig.text_matchcase = "మ్యాచ్ కేస్";	//"Match Case"
RTE_DefaultConfig.text_matchword = "మ్యాచ్ మాట";	//"Match Word"
RTE_DefaultConfig.text_move_down = "కిందికి జరుపు";	//"Move Down"
RTE_DefaultConfig.text_move_up = "పైకి జరుపు";	//"Move Up"
RTE_DefaultConfig.text_controlsizeauto = "స్వయంచాలక పరిమాణం";	//"Auto size"
RTE_DefaultConfig.text_controlsize100 = "100% వెడల్పు";	//"100% width"
RTE_DefaultConfig.text_controlsize75 = "75% వెడల్పు";	//"75% width"
RTE_DefaultConfig.text_controlsize50 = "50% వెడల్పు";	//"50% width"
RTE_DefaultConfig.text_controlsize25 = "25% వెడల్పు";	//"25% width"
RTE_DefaultConfig.text_controlsize = "అమరిక పరిమాణం";	//"Set Size"
RTE_DefaultConfig.text_controlalt = "Alt text";	//"Alt text"
RTE_DefaultConfig.text_controljustify = "సమర్థిస్తూ";	//"Justify"
RTE_DefaultConfig.text_imagecaption = "చిత్తరువు శీర్షిక";	//"Image Caption"
RTE_DefaultConfig.text_tablecellmerge = "కణాలను విలీనం";	//"Merge Cells"
RTE_DefaultConfig.text_tablecellsplitver = "విభజించిన కణాలు నిలువు";	//"Split Cells Vertical"
RTE_DefaultConfig.text_tablecellsplithor = "స్ప్లిట్ సెల్స్ హారిజాంటల్";	//"Split Cells Horizontal"
RTE_DefaultConfig.text_tablecellforecolor = "కణ పాఠ వర్ణం";	//"Cell Text Color"
RTE_DefaultConfig.text_tablecellbackcolor = "సెల్ బ్యాక్ కలర్";	//"Cell Back Color"
RTE_DefaultConfig.text_tablerowinsertabove = "పైన వరుస చొప్పించు";	//"Insert Row Above"
RTE_DefaultConfig.text_tablerowinsertbelow = "దిగువ వరుసలో చొప్పించండి";	//"Insert Row Below"
RTE_DefaultConfig.text_tablecolumninsertleft = "ఎడమ కాలమ్ చొప్పించు";	//"Insert Column Left"
RTE_DefaultConfig.text_tablecolumninsertright = "కుడి కాలమ్ చొప్పించండి";	//"Insert Column Right"
RTE_DefaultConfig.text_tablecolumndelete = "కాలమ్ తొలగించు";	//"Delete Column"
RTE_DefaultConfig.text_tablerowdelete = "వరుస తొలగించు";	//"Delete Row"
RTE_DefaultConfig.text_tabledelete = "పట్టిక తొలగించు";	//"Delete Table"
RTE_DefaultConfig.text_tableautosize = "స్వయంచాలక పరిమాణం";	//"Auto Size"
RTE_DefaultConfig.text_tableheader = "టేబుల్ హెడర్";	//"Table Header"
RTE_DefaultConfig.text_plusbtn = "కొత్త పేరాను జోడించు";	//"Add a new paragraph"
RTE_DefaultConfig.text_paste = "పేస్ట్";	//"Paste"
RTE_DefaultConfig.text_pasteauto = "పేస్ట్";	//"Paste"
RTE_DefaultConfig.text_pastetext = "పాఠాన్ని అతికించు";	//"Paste Text"
RTE_DefaultConfig.text_pasteashtml = "Html గా అతికించు";	//"Paste as Html"
RTE_DefaultConfig.text_pasteword = "పదాన్ని అతికించు";	//"Paste Word"
RTE_DefaultConfig.text_pasteinstruction = "దిగువ బాక్సులో కంటెంట్ ని పేస్ట్ చేయడం కొరకు దయచేసి CTRL+V ఉపయోగించండి. \r\n కంటెంట్ ఆటోమేటిక్ గా క్లీన్ చేయబడుతుంది.";	//"Please use CTRL+V to paste the content into the box below. \\r\\nThe content will be cleaned automatically."
RTE_DefaultConfig.text_paragraphop = "పేరాగ్రాఫ్ లు";	//"Paragraphs"
RTE_DefaultConfig.text_paragraphs = "పేరాగ్రాఫ్ లు";	//"Paragraphs"
RTE_DefaultConfig.text_pmoveup = "పైకి జరుపు";	//"Move Up"
RTE_DefaultConfig.text_pmovedown = "కిందికి జరుపు";	//"Move Down"
RTE_DefaultConfig.text_pduplicate = "నకిలీ";	//"Duplicate"
RTE_DefaultConfig.text_pdelete = "డిలీట్";	//"Delete"
RTE_DefaultConfig.text_pmore = "More..";	//"More.."
RTE_DefaultConfig.text_togglemore = "More..";	//"More.."
RTE_DefaultConfig.text_toggleborder = "సరిహద్దు ద్విక్రియం";	//"Toggle Border"
RTE_DefaultConfig.text_cut = "కట్";	//"Cut"
RTE_DefaultConfig.text_copy = "కాపీ";	//"Copy"
RTE_DefaultConfig.text_copied = "కాపీ చేశారు";	//"copied"
RTE_DefaultConfig.text_insertgallery = "గ్యాలరీని చొప్పించండి";	//"Insert Gallery"
RTE_DefaultConfig.text_insertdocument = "పత్రాన్ని చొప్పించు";	//"Insert Document"
RTE_DefaultConfig.text_inserttemplate = "టెంప్లెట్ చొప్పించండి";	//"Insert Template"
RTE_DefaultConfig.text_previewtitle = "ప్రివ్యూ";	//"Preview"
RTE_DefaultConfig.text_previewnormal = "సాధారణ";	//"Normal"
RTE_DefaultConfig.text_previewmobile = "మొబైల్";	//"Mobile"
RTE_DefaultConfig.text_previewtablet = "టాబ్లెట్";	//"Tablet"
RTE_DefaultConfig.text_table = "పట్టిక";	//"Table"
RTE_DefaultConfig.text_tablecell = "పట్టిక ఘటం";	//"Table Cell"
RTE_DefaultConfig.text_tablerow = "పట్టిక వరుస";	//"Table Row"
RTE_DefaultConfig.text_tablecolumn = "పట్టిక కాలమ్";	//"Table Column"
RTE_DefaultConfig.text_colorauto = "స్వయంచాలక";	//"Automatic"
RTE_DefaultConfig.text_colormore = "మరిన్ని";	//"More"
RTE_DefaultConfig.text_colorpicker = "రంగు పిక్కర్";	//"Color Picker"
RTE_DefaultConfig.text_colorwebpalette = "వెబ్ పాలెట్";	//"Web Palette"
RTE_DefaultConfig.text_colornamedcolors = "పేరు పెట్టబడింది రంగులు";	//"Named Colors"
RTE_DefaultConfig.text_colorbasic = "ప్రాథమిక";	//"Basic"
RTE_DefaultConfig.text_coloraddition = "చేరిక";	//"Addition"
RTE_DefaultConfig.text_draganddrop = "డ్రాగ్ మరియు డ్రాప్";	//"Drag and drop"
RTE_DefaultConfig.text_or = "లేదా";	//"or"
RTE_DefaultConfig.text_clicktoupload = "అప్ లోడ్ చేయడం కొరకు క్లిక్ చేయండి";	//"Click to upload"
RTE_DefaultConfig.text_defaultimagecaption = "స్వయంసిద్ధ చిత్తరువు శీర్షిక";	//"Default Image Caption"
RTE_DefaultConfig.text_searchemojis = "శోధన";	//"Search"
RTE_DefaultConfig.text_insertgallerytitle = "@insertgallery";	//"@insertgallery"
RTE_DefaultConfig.text_inserttemplatetitle = "@inserttemplate";	//"@inserttemplate"
RTE_DefaultConfig.text_reachmaxlength = "జోడించాల్సిన టెక్ట్స్ ఈ ఫీల్డ్ కొరకు క్యారెక్టర్ లిమిట్ ని చేరుకుంది.";	//"The text to be added has reached the character limit for this field."
