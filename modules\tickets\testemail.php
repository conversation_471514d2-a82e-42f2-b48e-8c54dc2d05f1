<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Fetch ticket ID from URL
$ticket_id = $_GET['tkt_id'] ?? 0;

$interim_mailto = '<EMAIL>'; //'<EMAIL>,<EMAIL>';


require_once '../../includes/header.php';

// Display interim update success message
if (isset($_GET['interim_updated'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Interim information updated successfully
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>';
}

// Add this where you handle other alerts
if (isset($_GET['email_sent'])) {
    echo '<div class="alert alert-success alert-dismissible fade show">
        Interim information updated and notification email sent successfully.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}

if (isset($_GET['email_failed'])) {
    echo '<div class="alert alert-warning alert-dismissible fade show">
        Interim information updated but email notification failed to send.
        <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>';
}
?>
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-11">

            <div class="card shadow">

                <div class="card-header bg-primary d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Ticket #</h4>

                </div>
                <div class="card-body">

                    <?php
                    // Handle interim form submission
                    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_interim') {
                        var_dump($_POST);
                        try {
                            // Check if record exists

                            // Prepare email content
                            $emailSubject = "Testing Interim Information Updated - Ticket #{$ticket_id}";
                            $emailBody = "
                                                    <html>
                                                    <body>
                                                        <h2>Interim Information Updated</h2>
                                                        <p>Ticket #{$ticket_id} interim information has been updated.</p>
                                                        
                                                        <h3>Details:</h3>
                                                        <ul>
                                                            <li><strong>Login Temp:</strong> {$_POST['login_temp']}</li>
                                                            <li><strong>SIM Number:</strong> {$_POST['sim_no']}</li>
                                                            <li><strong>SIM Serial:</strong> {$_POST['sim_serial']}</li>
                                                            <li><strong>SIM Operator:</strong> {$_POST['sim_operator']}</li>
                                                            <li><strong>SIM Package:</strong> {$_POST['sim_package']}</li>
                                                        </ul>
                                                        
                                                        <p>Updated by: {$_SESSION['userdata']['username']}</p>
                                                        <p><a href='" . BASE_URL . "/modules/tickets/view.php?tkt_id={$ticket_id}'>View Ticket</a></p>
                                                    </body>
                                                    </html>";

                            // Send email
                            $emailSent = sendInterimEmail($_POST['mailto'], $emailSubject, $emailBody);

                            if (!$emailSent) {
                                // Log email sending failure but don't stop the process
                                error_log("Failed to send interim update email for ticket #{$ticket_id}");
                            }

                            // Add email status to system comment
                            $interim_comment = "<div class='alert alert-info'>
                                                        <strong>Interim Information Updated</strong><br>
                                                        Interim details have been updated by " . htmlspecialchars($_SESSION['userdata']['username']) . ".<br>" .
                                ($emailSent ? "<span class='text-success'>✓ Notification email sent</span>" :
                                    "<span class='text-warning'>⚠ Email notification failed</span>") .
                                "</div>";

                            echo  $interim_comment;

                            // Refresh the page to show updated data
                            //header("Location: ?tkt_id=" . $ticket_id . "&interim_updated=1");
                            exit();
                        } catch (Exception $e) {
                            $error_message = "Error updating interim information: " . $e->getMessage();
                        }
                    }
                    ?>

                    <form method="POST" id="interimForm">
                        <input type="hidden" name="action" value="update_interim">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="mailto"><i class="fas fa-envelope"></i> Mail To: <span class="text-danger">*</span></label>
                                    <input type="email"
                                        class="form-control"
                                        id="mailto"
                                        name="mailto"
                                        multiple
                                        value="<?php echo htmlspecialchars($interim_data['mailto'] ?? $interim_mailto); ?>"
                                        required>
                                </div>

                                <div class="form-group">
                                    <label for="login_temp"><i class="fas fa-key"></i> Login Temp: <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="login_temp" name="login_temp"
                                        value="<?php echo htmlspecialchars($interim_data['login_temp'] ?? ''); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="sim_no"><i class="fas fa-sim-card"></i> SIM Number: <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="sim_no" name="sim_no"
                                        value="<?php echo htmlspecialchars($interim_data['sim_no'] ?? ''); ?>" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sim_serial"><i class="fas fa-barcode"></i> SIM Serial Number: <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="sim_serial" name="sim_serial"
                                        value="<?php echo htmlspecialchars($interim_data['sim_serial'] ?? ''); ?>" required>
                                </div>

                                <div class="form-group">
                                    <label for="sim_operator"><i class="fas fa-broadcast-tower"></i> SIM Operator: <span class="text-danger">*</span></label>
                                    <select class="form-control" id="sim_operator" name="sim_operator" required>
                                        <option value="">-- Select Operator --</option>
                                        <?php
                                        $operators = ['AIS', 'DTAC', 'NT', 'TRUE', 'Other'];
                                        foreach ($operators as $operator) {
                                            $selected = ($interim_data['sim_operator'] ?? '') === $operator ? 'selected' : '';
                                            echo "<option value=\"$operator\" $selected>$operator</option>";
                                        }
                                        ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="sim_package"><i class="fas fa-box"></i> SIM Package: <span class="text-danger">*</span></label>
                                    <select class="form-control" id="sim_package" name="sim_package" required>
                                        <option value="">-- Select Package --</option>
                                        <?php
                                        $packages = ['3G_AWifi 1ToAll M2MBusiness199BFixIPCorp', '3G_AWifi 1ToAll M2MBusiness349BFixIPCorp', '3G_AWifi CorpAPN M2M Pay 599BFixIPCorp', '3G_Corp APN Business Pay299BFixIPCorp', 'Other'];
                                        foreach ($packages as $package) {
                                            $selected = ($interim_data['sim_package'] ?? '') === $package ? 'selected' : '';
                                            echo "<option value=\"$package\" $selected>$package</option>";
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Interim
                            </button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>
</div>

</div>
</div>
</div>

<?php
// Include footer and scripts
// This is where you would include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
?>
<script src="../../assets/js/ticket-view.js?v=<?= $timestamp ?>"></script>
<?php
require_once '../../includes/footer.php';

?>