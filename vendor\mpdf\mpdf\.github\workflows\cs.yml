# https://help.github.com/en/categories/automating-your-workflow-with-github-actions

name: "Coding standard check"

on:
  pull_request:
  push:
    branches:
      - "development"
      - "test"

jobs:

  cs:

    name: "Coding standard"

    runs-on: ${{ matrix.operating-system }}

    strategy:
      matrix:
        php-version:
          - "7.4"

        operating-system: [ubuntu-latest]

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
          extensions: "mbstring"
          tools: composer:v2

      - name: "Install dependencies"
        run: "composer install --no-interaction --no-progress"

      - name: "CS"
        run: composer cs
