# Cybersecurity Incident Management Module

This module provides comprehensive cybersecurity incident reporting and management capabilities for the ticket management system.

## Features

### Core Functionality
- **Incident Creation**: Create detailed cybersecurity incident reports with all required fields
- **Incident Listing**: View all incidents with search, filtering, and sorting capabilities
- **Incident Details**: View comprehensive incident information with status tracking
- **Incident Editing**: Update incident details and status
- **File Attachments**: Upload and manage incident-related files
- **Status Tracking**: Track incident status changes over time

### Required Fields
The incident form includes all the requested fields:

1. **Incident No.** - Auto-generated unique identifier (INCyyyymm-NNNN format)
2. **Event Date** - Date and time when the incident occurred
3. **Reporter** - Person who reported the incident
4. **Channel** - How the incident was reported (Email, Phone, Internal System, etc.)
5. **Ticket No.** - Related ticket number (optional)
6. **Is Ever Happened** - Checkbox indicating if this type of incident occurred before
7. **Incident Ref. No** - Reference number for related incidents
8. **Type of Event** - Category of cybersecurity incident
9. **Event Detail** - Detailed description of the incident
10. **Effect** - Impact and consequences of the incident
11. **Preliminary Operations** - Initial response actions taken
12. **Severity of the Incident** - Critical, High, Medium, or Low
13. **Attach Files** - Multiple file upload support
14. **Responsible Person** - Person assigned to handle the incident
15. **Editing Details** - Additional editing information
16. **Date of Completion** - When the incident was resolved
17. **Investigation Summary** - Summary of investigation findings
18. **Future Preventive Measures** - Actions to prevent similar incidents
19. **Approver** - Person who approved the incident report
20. **Approval Date** - Date of approval

## File Structure

```
modules/incidents/
├── index.php          # Redirects to list.php
├── list.php           # Incident listing with search/filter
├── create.php         # Create new incident report
├── view.php           # View incident details
├── edit.php           # Edit incident report
├── download.php       # Download incident attachments
└── README.md          # This file
```

## Database Schema

### Main Tables
- **incidents** - Main incident data
- **incident_attachments** - File attachments
- **incident_status_history** - Status change tracking

### Key Features
- Auto-incrementing incident numbers
- File upload with size and type validation
- Status change history tracking
- Full-text search capabilities
- Comprehensive filtering options

## Installation

1. **Create Database Tables**:
   ```bash
   php create_incidents_tables.php
   ```

2. **Set Upload Permissions**:
   ```bash
   mkdir -p uploads/incidents
   chmod 755 uploads/incidents
   ```

3. **Access the Module**:
   Navigate to `modules/incidents/list.php` in your browser

## Usage

### Creating an Incident Report
1. Click "Create Incident Report" from the incidents list
2. Fill in all required fields (marked with *)
3. Upload relevant files (optional)
4. Submit the form

### Managing Incidents
- **View**: Click the "View" button to see full incident details
- **Edit**: Click the "Edit" button to modify incident information
- **Search**: Use the search box to find specific incidents
- **Filter**: Use dropdown filters to narrow down the incident list

### File Attachments
- Supported formats: JPG, PNG, PDF, DOC, DOCX, TXT, ZIP
- Maximum file size: 10MB per file
- Multiple files can be uploaded per incident

## Security Features

- User authentication required
- File type validation
- File size limits
- SQL injection protection
- XSS protection through proper escaping

## Integration

The module integrates seamlessly with the existing ticket management system:
- Uses the same authentication system
- Follows the same UI/UX patterns
- Shares common functions and utilities
- Added to the main navigation menu

## Customization

### Adding New Incident Types
Edit the `getIncidentTypes()` function in `includes/functions.php`

### Modifying Severity Levels
Edit the `getIncidentSeverities()` function in `includes/functions.php`

### Changing File Upload Limits
Modify the `$max_file_size` and `$allowed_types` variables in `create.php`

## Testing

Run the test script to validate the installation:
```bash
php test_incidents_module.php
```

## Support

For issues or questions regarding the incident management module, please contact the system administrator.
