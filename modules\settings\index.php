<?php
session_start();
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';


// Fetch user settings and user data
$user_id = $_SESSION['user_id'];
$settings = getUserSettings($user_id);
$userdata = $_SESSION['userdata'];

// Check if user is a member of System Admin team (id=3)
$is_system_admin = false;
$admin_check_query = "SELECT COUNT(*) FROM users WHERE id = :user_id AND `role` = 'admin'";
//"SELECT COUNT(*) FROM team_members WHERE user_id = :user_id AND team_id = 3";
$admin_check_stmt = $pdo->prepare($admin_check_query);
$admin_check_stmt->bindParam(':user_id', $user_id);
$admin_check_stmt->execute();
$is_system_admin = ($admin_check_stmt->fetchColumn() > 0);

// If user is admin, fetch all users and teams for the team assignment section
$all_users = [];
$all_teams = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $success_message = '';
    $error_message = '';

    // Update user settings
    $new_settings = [
        'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
        'sms_notifications' => isset($_POST['sms_notifications']) ? 1 : 0,
        'system_notifications' => isset($_POST['system_notifications']) ? 1 : 0,
        'theme' => $_POST['theme'],
        'language' => $_POST['language'],
        'items_per_page' => intval($_POST['items_per_page'])
    ];

    try {
        updateUserSettings($user_id, $new_settings);
        $settings = getUserSettings($user_id); // Refresh settings
        $success_message = "Settings updated successfully!";
    } catch (Exception $e) {
        $error_message = "Error updating settings: " . $e->getMessage();
    }
}

if ($is_system_admin) {
    // Fetch all users
    $users_query = "SELECT * FROM users ORDER BY username";
    $users_stmt = $pdo->query($users_query);
    $all_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Fetch all teams
    $teams_query = "SELECT id, name, description FROM teams ORDER BY name";
    $teams_stmt = $pdo->query($teams_query);
    $all_teams = $teams_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Handle team assignment form submission
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'assign_team') {
        $team_id = $_POST['team_id'] ?? 0;
        $user_id_to_assign = $_POST['user_id'] ?? 0;
        
        if ($team_id && $user_id_to_assign) {
            try {
                // Check if user is already in the team
                $check_query = "SELECT COUNT(*) FROM team_members WHERE team_id = :team_id AND user_id = :user_id";
                $check_stmt = $pdo->prepare($check_query);
                $check_stmt->bindParam(':team_id', $team_id);
                $check_stmt->bindParam(':user_id', $user_id_to_assign);
                $check_stmt->execute();
                
                if ($check_stmt->fetchColumn() > 0) {
                    $error_message = "User is already a member of this team.";
                } else {
                    // Add user to team
                    $insert_query = "INSERT INTO team_members (team_id, user_id) VALUES (:team_id, :user_id)";
                    $insert_stmt = $pdo->prepare($insert_query);
                    $insert_stmt->bindParam(':team_id', $team_id);
                    $insert_stmt->bindParam(':user_id', $user_id_to_assign);
                    $insert_stmt->execute();
                    
                    $success_message = "User successfully assigned to team!";
                }
            } catch (Exception $e) {
                $error_message = "Error assigning user to team: " . $e->getMessage();
            }
        } else {
            $error_message = "Please select both a user and a team.";
        }
    }
    
    // Handle team removal form submission
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'remove_from_team') {
        $team_member_id = $_POST['team_member_id'] ?? 0;
        
        if ($team_member_id) {
            try {
                // Remove user from team
                $delete_query = "DELETE FROM team_members WHERE id = :id";
                $delete_stmt = $pdo->prepare($delete_query);
                $delete_stmt->bindParam(':id', $team_member_id);
                $delete_stmt->execute();
                
                $success_message = "User successfully removed from team!";
            } catch (Exception $e) {
                $error_message = "Error removing user from team: " . $e->getMessage();
            }
        } else {
            $error_message = "Invalid team member selection.";
        }
    }
    
    // Fetch current team assignments for display
    $team_members_query = "SELECT tm.id, tm.team_id, tm.user_id, 
                          t.name AS team_name, 
                          u.username, u.email, u.role
                          FROM team_members tm
                          JOIN teams t ON tm.team_id = t.id
                          JOIN users u ON tm.user_id = u.id
                          ORDER BY t.name, u.username";
    $team_members_stmt = $pdo->query($team_members_query);
    $team_members = $team_members_stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Add this to the existing System Admin section where we handle form submissions
if ($is_system_admin) {
    // Handle new user creation
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'create_user') {
        $new_username = trim($_POST['new_username'] ?? '');
        $new_email = trim($_POST['new_email'] ?? '');
        $new_password = trim($_POST['new_password'] ?? '');
        $new_role = trim($_POST['new_role'] ?? '');
        $new_phone = trim($_POST['new_phone'] ?? '');
       
        // Validate input
        $validation_errors = [];
        
        if (empty($new_username)) {
            $validation_errors[] = "Username is required";
        } else {
            // Check if username already exists
            $check_username_query = "SELECT COUNT(*) FROM users WHERE username = :username";
            $check_username_stmt = $pdo->prepare($check_username_query);
            $check_username_stmt->bindParam(':username', $new_username);
            $check_username_stmt->execute();
            
            if ($check_username_stmt->fetchColumn() > 0) {
                $validation_errors[] = "Username already exists";
            }
        }
        
        if (empty($new_email)) {
            $validation_errors[] = "Email is required";
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            $validation_errors[] = "Invalid email format";
        } else {
            // Check if email already exists
            $check_email_query = "SELECT COUNT(*) FROM users WHERE email = :email";
            $check_email_stmt = $pdo->prepare($check_email_query);
            $check_email_stmt->bindParam(':email', $new_email);
            $check_email_stmt->execute();
            
            if ($check_email_stmt->fetchColumn() > 0) {
                $validation_errors[] = "Email already exists";
            }
        }
        
        if (empty($new_password)) {
            $validation_errors[] = "Password is required";
        } elseif (strlen($new_password) < 6) {
            $validation_errors[] = "Password must be at least 6 characters";
        }
        
        if (empty($new_role)) {
            $validation_errors[] = "Role is required";
        }
        
        // If no validation errors, create the user
        if (empty($validation_errors)) {
            try {
                // Hash the password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                
                // Insert the new user
                $insert_user_query = "INSERT INTO users (username, email, password, phone, role, created_at) 
                                     VALUES (:username, :email, :password, :phone, :role, NOW())";
                $insert_user_stmt = $pdo->prepare($insert_user_query);
                $insert_user_stmt->bindParam(':username', $new_username);
                $insert_user_stmt->bindParam(':email', $new_email);
                $insert_user_stmt->bindParam(':password', $hashed_password);
                $insert_user_stmt->bindParam(':phone', $new_phone);
                $insert_user_stmt->bindParam(':role', $new_role);
                
                if ($insert_user_stmt->execute()) {
                    $new_user_id = $pdo->lastInsertId();
                    
                    // Create default user settings
                    $default_settings_query = "INSERT INTO user_settings (user_id) VALUES (:user_id)";
                    $default_settings_stmt = $pdo->prepare($default_settings_query);
                    $default_settings_stmt->bindParam(':user_id', $new_user_id);
                    $default_settings_stmt->execute();
                    
                    $success_message = "User created successfully!";
                    
                    // Refresh the users list
                    $users_stmt = $pdo->query($users_query);
                    $all_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
                     // After successfully creating the user
                        if (!empty($new_user_id) && !empty($_POST['new_assign_team'])) {
                            $team_id_to_assign = $_POST['new_assign_team'];
                            
                            // Add user to team
                            try {
                                $insert_team_query = "INSERT INTO team_members (team_id, user_id) VALUES (:team_id, :user_id)";
                                $insert_team_stmt = $pdo->prepare($insert_team_query);
                                $insert_team_stmt->bindParam(':team_id', $team_id_to_assign);
                                $insert_team_stmt->bindParam(':user_id', $new_user_id);
                                $insert_team_stmt->execute();
                                
                                $success_message .= " User was also assigned to the selected team.";
                                
                                // Refresh team members list
                                $team_members_stmt = $pdo->query($team_members_query);
                                $team_members = $team_members_stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (Exception $e) {
                                $error_message = "User created but could not be assigned to team: " . $e->getMessage();
                            }
                        }
                } else {
                    $error_message = "Error creating user";
                }
            } catch (Exception $e) {
                $error_message = "Error creating user: " . $e->getMessage();
            }
        } else {
            $error_message = "Please correct the following errors: " . implode(", ", $validation_errors);
        }
    }
}

// Add this to your existing form handling section
if ($is_system_admin && $_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'edit_user') {
    $edit_user_id = $_POST['edit_user_id'];
    $edit_username = trim($_POST['edit_username']);
    $edit_email = trim($_POST['edit_email']);
    $edit_role = trim($_POST['edit_role']);
    $edit_phone = trim($_POST['edit_phone']);
    $edit_password = trim($_POST['edit_password']);
    
    try {
        // Start building the update query
        $update_fields = [];
        $params = [':user_id' => $edit_user_id];
        
        // Add fields to update
        if (!empty($edit_username)) {
            $update_fields[] = "username = :username";
            $params[':username'] = $edit_username;
        }
        if (!empty($edit_email)) {
            $update_fields[] = "email = :email";
            $params[':email'] = $edit_email;
        }
        if (!empty($edit_role)) {
            $update_fields[] = "role = :role";
            $params[':role'] = $edit_role;
        }
        if (!empty($edit_phone)) {
            $update_fields[] = "phone = :phone";
            $params[':phone'] = $edit_phone;
        }
        if (!empty($edit_password)) {
            $update_fields[] = "password = :password";
            $params[':password'] = password_hash($edit_password, PASSWORD_DEFAULT);
        }
        
        if (!empty($update_fields)) {
            $update_query = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = :user_id";
            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute($params);
            
            $success_message = "User updated successfully!";
            
            // Refresh the users list
            $users_stmt = $pdo->query($users_query);
            $all_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (Exception $e) {
        $error_message = "Error updating user: " . $e->getMessage();
    }
}


require_once '../../includes/header.php';
?>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">User Settings</h4>
                </div>
                <div class="card-body small">
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
                    <?php endif; ?>
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($error_message); ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>User Information</h5>
                                <div class="form-group">
                                    <label>Username:</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($userdata['username']); ?>" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Role:</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($userdata['role']); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>Notification Preferences</h5>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="email_notifications"
                                            name="email_notifications" <?php echo @$settings['email_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="email_notifications">Email Notifications</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="sms_notifications"
                                            name="sms_notifications" <?php echo @$settings['sms_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="sms_notifications">SMS Notifications</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="system_notifications"
                                            name="system_notifications" <?php echo @$settings['system_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="system_notifications">System Notifications</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>Display Settings</h5>
                                <div class="form-group">
                                    <label for="theme">Theme:</label>
                                    <select class="form-control form-control_sm" id="theme" name="theme">
                                        <option value="light" <?php echo (@$settings['theme'] == 'light') ? 'selected' : ''; ?>>Light</option>
                                        <option value="dark" <?php echo (@$settings['theme'] == 'dark') ? 'selected' : ''; ?>>Dark</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="language">Language:</label>
                                    <select class="form-control form-control_sm" id="language" name="language">
                                        <option value="en" <?php echo (@$settings['language'] == 'en') ? 'selected' : ''; ?>>English</option>
                                        <option value="th" <?php echo (@$settings['language'] == 'th') ? 'selected' : ''; ?>>Thai</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="items_per_page">Items per page:</label>
                                    <select class="form-control form-control_sm" id="items_per_page" name="items_per_page">
                                        <option value="10" <?php echo (@$settings['items_per_page'] == 10) ? 'selected' : ''; ?>>10</option>
                                        <option value="25" <?php echo (@$settings['items_per_page'] == 25) ? 'selected' : ''; ?>>25</option>
                                        <option value="50" <?php echo (@$settings['items_per_page'] == 50) ? 'selected' : ''; ?>>50</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <a href="../dashboard/index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($is_system_admin): ?>
<div class="row mt-5">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Team Management</h4>
            </div>
            <div class="card-body small">
                <ul class="nav nav-tabs" id="teamManagementTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="assign-tab" data-toggle="tab" href="#assign" role="tab">
                            <i class="fas fa-user-plus"></i> Assign Users to Teams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="current-tab" data-toggle="tab" href="#current" role="tab">
                            <i class="fas fa-users"></i> Current Team Members
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="new-user-tab" data-toggle="tab" href="#new-user" role="tab">
                            <i class="fas fa-user-plus"></i> Create New User
                        </a>
                    </li>
                </ul>
                
                <div class="tab-content mt-3" id="teamManagementTabContent">
                    <!-- Assign Users to Teams Tab -->
                    <div class="tab-pane mb-5 fade show active" id="assign" role="tabpanel">
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="assign_team">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="form-group">
                                        <label for="user_id"><i class="fas fa-user"></i> Select User:</label>
                                        <select class="form-control" id="user_id" name="user_id" required>
                                            <option value="">-- Select User --</option>
                                            <?php foreach ($all_users as $user): ?>
                                                <option value="<?php echo $user['id']; ?>">
                                                    <?php echo htmlspecialchars($user['username']); ?> 
                                                    (<?php echo htmlspecialchars($user['role']); ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="form-group">
                                        <label for="team_id"><i class="fas fa-users"></i> Select Team:</label>
                                        <select class="form-control" id="team_id" name="team_id" required>
                                            <option value="">-- Select Team --</option>
                                            <?php foreach ($all_teams as $team): ?>
                                                <option value="<?php echo $team['id']; ?>">
                                                    <?php echo htmlspecialchars($team['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-success btn-block">
                                        <i class="fas fa-plus-circle"></i> Assign
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Current Team Members Tab -->
                    <div class="tab-pane fade" id="current" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Team</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($team_members)): ?>
                                        <?php foreach ($team_members as $member): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($member['team_name']); ?></td>
                                                <td><?php echo htmlspecialchars($member['username']); ?></td>
                                                <td><?php echo htmlspecialchars($member['email']); ?></td>
                                                <td><?php echo htmlspecialchars($member['role']); ?></td>
                                                <td>
                                                    <form method="POST" action="" class="d-inline" 
                                                          onsubmit="return confirm('Are you sure you want to remove this user from the team?');">
                                                        <input type="hidden" name="action" value="remove_from_team">
                                                        <input type="hidden" name="team_member_id" value="<?php echo $member['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="fas fa-user-minus"></i> Remove
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="text-center">No team assignments found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- Create New User Tab -->
                    <div class="tab-pane fade" id="new-user" role="tabpanel" aria-labelledby="new-user-tab">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Create New User</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="" id="createUserForm">
                                    <input type="hidden" name="action" value="create_user">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="new_username"><i class="fas fa-user"></i> Username:</label>
                                                <input type="text" class="form-control" id="new_username" name="new_username" required>
                                                <small class="form-text text-muted">Username must be unique</small>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="new_email"><i class="fas fa-envelope"></i> Email:</label>
                                                <input type="email" class="form-control" id="new_email" name="new_email" required>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="new_password"><i class="fas fa-lock"></i> Password:</label>
                                                <div class="input-group">
                                                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <small class="form-text text-muted">Password must be at least 6 characters</small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="new_role"><i class="fas fa-user-tag"></i> Role:</label>
                                                <select class="form-control" id="new_role" name="new_role" required>
                                                    <option value="">-- Select Role --</option>
                                                    <option value="admin">Administrator</option>
                                                    <option value="technician">Technician</option>
                                                    <option value="customer_service">Customer Service</option>
                                                    <option value="customer">Customer</option>
                                                </select>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="new_phone"><i class="fas fa-phone"></i> Phone Number:</label>
                                                <input type="text" class="form-control" id="new_phone" name="new_phone">
                                                <small class="form-text text-muted">Optional</small>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label for="new_assign_team"><i class="fas fa-users"></i> Assign to Team:</label>
                                                <select class="form-control" id="new_assign_team" name="new_assign_team">
                                                    <option value="">-- None --</option>
                                                    <?php foreach ($all_teams as $team): ?>
                                                        <option value="<?php echo $team['id']; ?>">
                                                            <?php echo htmlspecialchars($team['name']); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <small class="form-text text-muted">Optional - You can assign the user to a team later</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group mt-3">
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-user-plus"></i> Create User
                                        </button>
                                        <button type="reset" class="btn btn-secondary">
                                            <i class="fas fa-undo"></i> Reset Form
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <!-- User List -->
                        <div class="mt-4">
                            <h5>Existing Users</h5>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="usersTable">
                                    <thead>
                                        <tr>
                                            <th>Username</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Phone</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($all_users as $user): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                <td><?php echo htmlspecialchars($user['role']); ?></td>
                                                <td><?php echo htmlspecialchars($user['phone'] ?? 'N/A'); ?></td>
                                                <td><?php echo isset($user['created_at']) ? date('Y-m-d', strtotime($user['created_at'])) : 'N/A'; ?></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-primary edit-user" 
                                                            data-user-id="<?php echo $user['id']; ?>"
                                                            data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                                            data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                            data-role="<?php echo htmlspecialchars($user['role']); ?>"
                                                            data-phone="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
                                                            data-toggle="modal" data-target="#editUserModal">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include main script loader
// This is where you can include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
// Add this before the closing </body> tag
if ($is_system_admin) {
    echo '<script src="' . BASE_URL . '/assets/js/team-management.js?v=' . time() . '"></script>';
}

?>

<!-- Add this after the users table -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="" id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_user">
                    <input type="hidden" name="edit_user_id" id="edit_user_id">
                    
                    <div class="form-group">
                        <label for="edit_username"><i class="fas fa-user"></i> Username:</label>
                        <input type="text" class="form-control" id="edit_username" name="edit_username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_email"><i class="fas fa-envelope"></i> Email:</label>
                        <input type="email" class="form-control" id="edit_email" name="edit_email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_role"><i class="fas fa-user-tag"></i> Role:</label>
                        <select class="form-control" id="edit_role" name="edit_role" required>
                            <option value="admin">Administrator</option>
                            <option value="technician">Technician</option>
                            <option value="customer_service">Customer Service</option>
                            <option value="customer">Customer</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_phone"><i class="fas fa-phone"></i> Phone:</label>
                        <input type="text" class="form-control" id="edit_phone" name="edit_phone">
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_password"><i class="fas fa-lock"></i> New Password:</label>
                        <input type="password" class="form-control" id="edit_password" name="edit_password">
                        <small class="form-text text-muted">Leave blank to keep current password</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php
require_once '../../includes/footer.php';
?>