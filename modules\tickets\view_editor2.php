<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Fetch ticket ID from URL
$ticket_id = $_GET['tkt_id']??0;

// Fetch ticket details with JOIN
$query = "SELECT 
    t.*, 
    c.CusName AS customer_name,
    c.<PERSON><PERSON>ddress AS customer_address,
    tm.name AS team_name,
    u.username AS created_by,
    t.ticket_type,
    t.affecting_service,
    t.symptoms_details,
    t.product_type,
    t.severity
FROM 
    tickets t 
    LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
    LEFT JOIN teams tm ON t.assigned_team = tm.id
    LEFT JOIN users u ON t.username = u.username
WHERE 
    t.ticket_number = ?";

$stmt = $pdo->prepare($query);
$stmt->execute([$ticket_id]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

// Fetch symptoms and product types for reference
$symptoms_query = "SELECT code, name FROM symptoms WHERE is_active = 1";
$symptoms_stmt = $pdo->query($symptoms_query);
$symptoms_list = $symptoms_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$product_types_query = "SELECT code, name FROM product_types WHERE is_active = 1";
$product_types_stmt = $pdo->query($product_types_query);
$product_types_list = $product_types_stmt->fetchAll(PDO::FETCH_KEY_PAIR);

// Fetch ticket comments
$comments_query = "SELECT 
    tc.*, u.username, u.role
    FROM ticket_comments tc
    LEFT JOIN users u ON tc.user_id = u.id
    WHERE tc.ticket_number = ?
    ORDER BY tc.created_at DESC";
$comments_stmt = $pdo->prepare($comments_query);
$comments_stmt->execute([$ticket_id]);
$comments = $comments_stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle new comment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comment'])) {
   
    $comment = base64_encode(trim($_POST['comment']));
    if (!empty($comment)) {
        $insert_query = "INSERT INTO ticket_comments (ticket_number, user_id, comment_base64, created_at) 
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
        
        $insert_stmt = $pdo->prepare($insert_query);
        $insert_stmt->execute([$ticket_id, $_SESSION['user_id'], $comment]);
        
        // Redirect to prevent form resubmission
        header("Location: ?tkt_id=" . $ticket_id);
        exit();
    }
}

// Handle status update submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    $new_status = trim($_POST['status']);
    $current_status = trim($_POST['current_status'] ?? '');
    $status_comment = trim($_POST['status_comment'] ?? '');
    if(empty($new_status) || $new_status === $current_status) {
        $error_message = "Invalid status update.";
         // Redirect to prevent form resubmission
         header("Location: view.php?tkt_id=" . $ticket_id . "&status_updated=0&error=" . urlencode($error_message));
         exit();
    } else {
        $ticket_id = $_POST['ticket_number'];
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Update ticket status
        $update_query = "UPDATE tickets SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE ticket_number = ?";
        $update_stmt = $pdo->prepare($update_query);
        $update_stmt->execute([$new_status, $ticket_id]);
        
        // Record status change in history
        $history_query = "INSERT INTO ticket_status_history 
            (ticket_number, status, changed_by, comments) 
            VALUES (?, ?, ?, ?)";
        $history_stmt = $pdo->prepare($history_query);
        $history_stmt->execute([
            $ticket_id,
            $new_status,
            $_SESSION['user_id'],
            $status_comment
        ]);
        
        $pdo->commit();
        
        // Redirect to prevent form resubmission
        header("Location: view.php?tkt_id=" . $ticket_id . "&status_updated=1");
        exit();

    } catch (Exception $e) {
        $pdo->rollBack();
        $error_message = "Error updating status: " . $e->getMessage();
    }
}//check if status is empty or same as current status
}


require_once '../../includes/header.php';

// Display status update success message
if (isset($_GET['status_updated'])) {
    if ($_GET['status_updated'] == 0) {
        echo '<div class="alert alert-danger alert-dismissible fade show">
            ' . htmlspecialchars($_GET['error']) . '
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    } else {
        echo '<div class="alert alert-success alert-dismissible fade show">
            Status updated successfully
            <button type="button" class="close" data-dismiss="alert">&times;</button>
          </div>';
    }
   
}
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-11">
            <?php if (!$ticket): ?>
                <div class="alert alert-danger">
                    Ticket not found. <a href="list.php" class="alert-link">Return to ticket list</a>
                </div>
            <?php else: ?>
                <div class="card shadow">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Ticket #<?php echo htmlspecialchars($ticket['ticket_number']); ?></h4>
                        <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                            <?php echo htmlspecialchars($ticket['status']); ?>
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="mb-3">Customer Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Customer Name:</th>
                                        <td><?php echo htmlspecialchars($ticket['customer_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Customer ID:</th>
                                        <td><?php echo htmlspecialchars($ticket['customer_number']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Address:</th>
                                        <td><Address class="small"><?php echo htmlspecialchars($ticket['customer_address']); ?></Address></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-3">Ticket Information</h5>
                                <table class="table table-sm">
                                    <tr>
                                        <th width="150">Ticket Type:</th>
                                        <td>
                                            <?php
                                            $ticket_types = [
                                                '1' => 'Incident',
                                                '2' => 'Problem',
                                                '3' => 'Change',
                                                '4' => 'Request',
                                                '5' => 'Service Request'
                                            ];
                                            echo htmlspecialchars($ticket_types[$ticket['ticket_type']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Service Impact:</th>
                                        <td>
                                            <?php
                                            $impact_types = [
                                                '1' => 'กระทบ',
                                                '2' => 'ไม่กระทบ'
                                            ];
                                            echo htmlspecialchars($impact_types[$ticket['affecting_service']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Symptoms:</th>
                                        <td>
                                            <?php
                                            echo htmlspecialchars($symptoms_list[$ticket['symptoms_details']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Product Type:</th>
                                        <td>
                                            <?php
                                            echo htmlspecialchars($product_types_list[$ticket['product_type']] ?? 'N/A');
                                            ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Severity:</th>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php
                                                $severity_types = [
                                                    'S1' => 'Severity 1 (S1- Service Critical Impact)',
                                                    'S2' => 'Severity 2 (S2- Significant Impact)', 
                                                    'S3' => 'Severity 3 (S3- Minor impact)',
                                                    'S4' => 'Severity 4 (S4- Low Impact)'
                                                ];
                                                echo htmlspecialchars($severity_types[$ticket['severity']] ?? 'N/A');
                                                ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Priority:</th>
                                        <td>
                                            <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                                                <?php echo htmlspecialchars($ticket['priority']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                     <tr>
                                        <th>Current Status:</th>
                                        <td>
                                            <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                                <?php echo htmlspecialchars($ticket['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Assigned Team:</th>
                                        <td><?php echo htmlspecialchars($ticket['team_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created By:</th>
                                        <td><?php echo htmlspecialchars($ticket['created_by']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Created At:</th>
                                        <td><?php echo date('d/m/Y H:i', strtotime($ticket['created_at'])); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Working Time:</th>
                                        <td>
                                            <?php 
                                            $working_time = calculateTicketWorkingTimeById($ticket['ticket_number']);
                                            if ($working_time) {
                                                echo '<span class="badge badge-info">' . $working_time['formatted'] . '</span>';
                                                if ($ticket['status'] !== 'Closed') {
                                                    echo ' <small class="text-muted">(Running)</small>';
                                                }
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h5 class="text-danger">
                                <i class="fas fa-exclamation-circle"></i> Issue Details
                            </h5>
                            <div class="p-3 rounded issue-details-box">
                                <?php echo nl2br(htmlspecialchars($ticket['issue_details'])); ?>
                            </div>
                        </div>

                        <div class="mt-4 small">
                            <h5>Update Status</h5>
                            <form method="POST" class="form-horizontal">
                                <?php if ($ticket['status'] === 'Closed'): ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-lock"></i> This ticket is closed and cannot be modified.
                                    </div>
                                <?php else: ?>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">New Status:</label>
                                        <div class="col-sm-4">
                                            <select name="status" class="form-control" required <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>>
                                                <option value="">Select Status</option>
                                                <option value="Open" <?php echo ($ticket['status'] == 'Open') ? 'selected' : ''; ?>>Open</option>
                                                <option value="In Progress" <?php echo ($ticket['status'] == 'In Progress') ? 'selected' : ''; ?>>In Progress</option>
                                                <option value="Pending" <?php echo ($ticket['status'] == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                                <option value="Closed" <?php echo ($ticket['status'] == 'Closed') ? 'selected' : ''; ?>>Closed</option>
                                            </select>
                                            <input type="hidden" name="ticket_number" value="<?php echo htmlspecialchars($ticket['ticket_number']); ?>">
                                            <input type="hidden" name="current_status" value="<?php echo htmlspecialchars($ticket['status']); ?>">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-sm-2 col-form-label">Comments:</label>
                                        <div class="col-sm-6">
                                            <textarea name="status_comment" class="form-control" rows="2" 
                                                placeholder="Add any comments about this status change..." <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="col-sm-10 offset-sm-2">
                                            <button type="submit" class="btn btn-primary" <?php echo ($ticket['status'] === 'Closed' ? 'disabled' : ''); ?>>
                                                <i class="fas fa-save"></i> Update Status
                                            </button>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </form>
                        </div>

                        <div class="mt-4 small text-secondary">
                            <h5>Status History</h5>
                            <?php
                            $history_query = "SELECT 
                                tsh.*, u.username, u.role
                                FROM ticket_status_history tsh
                                LEFT JOIN users u ON tsh.changed_by = u.id
                                WHERE tsh.ticket_number = ?
                                ORDER BY tsh.changed_at DESC";
                            $history_stmt = $pdo->prepare($history_query);
                            $history_stmt->execute([$ticket_id]);
                            $status_history = $history_stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>
                            
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date/Time</th>
                                            <th>Status</th>
                                            <th>Changed By</th>
                                            <th>Comments</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($status_history as $history): ?>
                                            <tr>
                                                <td><?php echo date('d/m/Y H:i', strtotime($history['changed_at'])); ?></td>
                                                <td>
                                                    <span class="badge badge-<?php echo getStatusBadgeClass($history['status']); ?>">
                                                        <?php echo htmlspecialchars($history['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($history['username']); ?>
                                                    <span class="badge badge-secondary"><?php echo htmlspecialchars($history['role']); ?></span>
                                                </td>
                                                <td><?php echo nl2br(htmlspecialchars($history['comments'])); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                        <?php if (empty($status_history)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">No status changes recorded</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Comments Section -->
                        <div class="mt-4">
                            <h5>Comments</h5>
                            
                            <!-- Comment Form -->
                            <form method="POST" class="mb-4" enctype="multipart/form-data">
                                <div class="form-group">
                                    <textarea id="comment-editor" name="comment" class="form-control" rows="3" 
                                        placeholder="Add a comment..." required></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-comment"></i> Add Comment
                                </button>
                            </form>

                            <!-- Comments List -->
                            <div class="comments-section">
                                <?php if (!empty($comments)): ?>
                                    <?php foreach ($comments as $comment): ?>
                                        <div class="comment card mb-3">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <div>
                                                        <strong class="text-primary">
                                                            <i class="fas fa-user-circle"></i> 
                                                            <?php echo htmlspecialchars($comment['username']); ?>
                                                        </strong>
                                                        <span class="badge badge-secondary ml-2">
                                                            <?php echo htmlspecialchars($comment['role']); ?>
                                                        </span>
                                                    </div>
                                                    <small class="text-muted">
                                                        <i class="fas fa-clock"></i>
                                                        <?php echo date('d/m/Y H:i', strtotime($comment['created_at'])); ?>
                                                    </small>
                                                </div>
                                                <div class="comment-content">
                                                    <?php echo $comment['comment']??base64_decode($comment['comment_base64']); // Display HTML content ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        No comments yet. Be the first to comment!
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-right">
                        <a href="list.php" class="btn btn-secondary">Back to List</a>
                        <a href="edit.php?tkt_id=<?php echo $ticket_id; ?>" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Ticket
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Floating Help Note -->
<div id="helpNote" class="position-fixed shadow-sm" style="width: 300px; z-index: 1000; right: 20px; top: 80px;">
    <div class="card note-card">
        <div class="card-header note-header py-2" id="helpDragHandle">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> Help Guide
                </h6>
                <button type="button" class="btn btn-link btn-sm text-secondary p-0" id="minimizeHelp">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body note-body py-2" id="helpContent">
            <div class="help-item mb-2">
                <strong><i class="fas fa-ticket-alt text-primary"></i> Status Flow:</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Open → In Progress → Closed</li>
                    <li>Use Pending when waiting for response</li>
                    <li>Closed tickets cannot be reopened</li>
                </ul>
            </div>
            <div class="help-item mb-2">
                <strong><i class="fas fa-clock text-warning"></i> Working Time:</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Starts when ticket is created</li>
                    <li>Pauses during Pending status</li>
                    <li>Stops when status is Closed</li>
                </ul>
            </div>
            <div class="help-item">
                <strong><i class="fas fa-comments text-success"></i> Comments:</strong>
                <ul class="small pl-4 mb-0 mt-1">
                    <li>Add updates about progress</li>
                    <li>Include relevant details</li>
                    <li>Mention next actions</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer and scripts
// This is where you would include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
?>
<!--Include the JS & CSS-->
<link rel="stylesheet" href="<?=BASE_URL?>/assets/plugins/richtexteditor/rte_theme_default.css" />
<script type="text/javascript" src="<?=BASE_URL?>/assets/plugins/richtexteditor/rte.js"></script>
<script type="text/javascript" src='<?=BASE_URL?>/assets/plugins/richtexteditor/plugins/all_plugins.js'></script>
<script>
	var editor1 = new RichTextEditor("#comment-editor");
	//editor1.setHTMLCode("Use inline HTML or setHTMLCode to init the default content.");
</script>
<script src="../../assets/js/ticket-view.js?v=<?=$timestamp?>"></script>
<?php
require_once '../../includes/footer.php';
?>


